package com.ktg.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 异常扣减单状态枚举
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@Getter
@AllArgsConstructor
public enum ExceptionDeductionStatusEnum {
    
    /** 统计中 */
    COUNTING("COUNTING", "统计中"),
    
    /** 待领料 */
    PENDING_ISSUE("PENDING_ISSUE", "待领料"),
    
    /** 待推送 */
    PENDING_PUSH("PENDING_PUSH", "待推送"),
    
    /** 已完成 */
    COMPLETED("COMPLETED", "已完成"),
    
    /** 已关闭 */
    CLOSED("CLOSED", "已关闭");
    
    private final String code;
    private final String desc;
    
    /**
     * 根据code获取对应的枚举值
     * 
     * @param code 代码
     * @return 枚举值
     */
    public static ExceptionDeductionStatusEnum getByCode(String code) {
        for (ExceptionDeductionStatusEnum value : ExceptionDeductionStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
    
    /**
     * 验证状态转换是否合法
     * 
     * @param fromStatus 原状态
     * @param toStatus 目标状态
     * @return 是否合法
     */
    public static boolean isValidTransition(String fromStatus, String toStatus) {
        ExceptionDeductionStatusEnum from = getByCode(fromStatus);
        ExceptionDeductionStatusEnum to = getByCode(toStatus);
        
        if (from == null || to == null) {
            return false;
        }
        
        switch (from) {
            case COUNTING:
                return to == PENDING_ISSUE;
            case PENDING_ISSUE:
                return to == PENDING_PUSH || to == CLOSED;
            case PENDING_PUSH:
                return to == COMPLETED || to == PENDING_ISSUE || to == CLOSED;
            case COMPLETED:
                return false; // 已完成状态不能转换到其他状态
            case CLOSED:
                return false; // 已关闭状态不能转换到其他状态
            default:
                return false;
        }
    }
}
