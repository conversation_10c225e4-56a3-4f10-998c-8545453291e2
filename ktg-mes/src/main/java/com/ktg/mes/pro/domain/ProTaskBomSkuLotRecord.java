package com.ktg.mes.pro.domain;

import com.ktg.common.annotation.Excel;
import com.ktg.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/8/11 14:18
 */
@Data
public class ProTaskBomSkuLotRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 批次id记录行ID
     */
    private Long recordId;

    /**
     * 生产工单ID
     */
    @Excel(name = "生产工单ID")
    private Long workorderId;

    /**
     * 生产任务ID
     */
    @Excel(name = "生产任务ID")
    private Long taskId;

    /**
     * BOM物料编号
     */
    @Excel(name = "BOM物料编号")
    private String itemCode;

    /**
     * BOM物料名称
     */
    @Excel(name = "BOM物料名称")
    private String itemName;

    /**
     * 批次id信息
     */
    @Excel(name = "批次id信息")
    private String skuLotNo;

    /**
     * 批次id记录类型
     */
    @Excel(name = "批次id记录类型")
    private Integer type;

    /** 子编码 */
    @Excel(name = "子编码")
    private String subCode;

    /** 是否满足匹配：1：满足；0：不满足（不够扣找个批次挂着的情况） */
    @Excel(name = "是否满足匹配：1：满足；0：不满足", readConverterExp = "不=够扣找个批次挂着的情况")
    private Integer matchType;

    /**
     * 数量
     */
    @Excel(name = "数量")
    private BigDecimal quantity;
}