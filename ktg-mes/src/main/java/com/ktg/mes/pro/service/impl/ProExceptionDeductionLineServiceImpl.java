package com.ktg.mes.pro.service.impl;

import com.ktg.common.utils.DateUtils;
import com.ktg.mes.pro.domain.ProExceptionDeductionLine;
import com.ktg.mes.pro.mapper.ProExceptionDeductionLineMapper;
import com.ktg.mes.pro.service.IProExceptionDeductionLineService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 异常扣减单表体Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@Slf4j
@Service
public class ProExceptionDeductionLineServiceImpl implements IProExceptionDeductionLineService {
    
    @Autowired
    private ProExceptionDeductionLineMapper proExceptionDeductionLineMapper;

    @Override
    public ProExceptionDeductionLine selectProExceptionDeductionLineByLineId(Long lineId) {
        return proExceptionDeductionLineMapper.selectProExceptionDeductionLineByLineId(lineId);
    }

    @Override
    public List<ProExceptionDeductionLine> selectProExceptionDeductionLineList(ProExceptionDeductionLine proExceptionDeductionLine) {
        return proExceptionDeductionLineMapper.selectProExceptionDeductionLineList(proExceptionDeductionLine);
    }

    @Override
    public List<ProExceptionDeductionLine> selectByDeductionId(Long deductionId) {
        return proExceptionDeductionLineMapper.selectByDeductionId(deductionId);
    }

    @Override
    public List<ProExceptionDeductionLine> selectByWorkorderId(Long workorderId) {
        return proExceptionDeductionLineMapper.selectByWorkorderId(workorderId);
    }

    @Override
    public List<ProExceptionDeductionLine> selectByTaskId(Long taskId) {
        return proExceptionDeductionLineMapper.selectByTaskId(taskId);
    }

    @Override
    public List<ProExceptionDeductionLine> selectByTaskIdAndItemCode(Long taskId, String itemCode) {
        return proExceptionDeductionLineMapper.selectByTaskIdAndItemCode(taskId, itemCode);
    }

    @Override
    public ProExceptionDeductionLine checkExistsByTaskIdAndItemCode(Long taskId, String itemCode) {
        return proExceptionDeductionLineMapper.checkExistsByTaskIdAndItemCode(taskId, itemCode);
    }

    @Override
    public int insertProExceptionDeductionLine(ProExceptionDeductionLine proExceptionDeductionLine) {
        proExceptionDeductionLine.setCreateTime(DateUtils.getNowDate());
        return proExceptionDeductionLineMapper.insertProExceptionDeductionLine(proExceptionDeductionLine);
    }

    @Override
    public int updateProExceptionDeductionLine(ProExceptionDeductionLine proExceptionDeductionLine) {
        proExceptionDeductionLine.setUpdateTime(DateUtils.getNowDate());
        return proExceptionDeductionLineMapper.updateProExceptionDeductionLine(proExceptionDeductionLine);
    }

    @Override
    public int deleteProExceptionDeductionLineByLineIds(Long[] lineIds) {
        return proExceptionDeductionLineMapper.deleteProExceptionDeductionLineByLineIds(lineIds);
    }

    @Override
    public int deleteProExceptionDeductionLineByLineId(Long lineId) {
        return proExceptionDeductionLineMapper.deleteProExceptionDeductionLineByLineId(lineId);
    }

    @Override
    public int deleteByDeductionId(Long deductionId) {
        return proExceptionDeductionLineMapper.deleteByDeductionId(deductionId);
    }
}
