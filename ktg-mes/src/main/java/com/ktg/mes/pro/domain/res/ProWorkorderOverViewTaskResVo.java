package com.ktg.mes.pro.domain.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/4/29 17:08
 */
@Data
public class ProWorkorderOverViewTaskResVo implements Serializable {
    @ApiModelProperty("生产任务ID")
    private Long taskId;
    //生产任务编码
    @ApiModelProperty("生产任务编码")
    private String taskCode;
    //计划数量
    @ApiModelProperty("计划数量")
    private BigDecimal quantity;
    //实际产量
    @ApiModelProperty("实际产量")
    private BigDecimal quantityProduced;
    //工作站
    @ApiModelProperty("工作站")
    private String workstationName;
    //任务状态
    @ApiModelProperty("任务状态")
    private String taskStatusDesc;
    //排产时间
    @ApiModelProperty("排产时间")
    private Date startTime;
    //完成时间
    @ApiModelProperty("完成时间")
    private Date endTime;
    // 生产批次号
    @ApiModelProperty("生产批次号")
    private String produceBatchCode;

    /** 单位 */
    @ApiModelProperty("单位")
    private String unitOfMeasure;

    /** 单位描述 */
    @ApiModelProperty("单位描述")
    private String unitOfMeasureDesc;

    private List<ProWorkorderOverViewBomResVo> bomResVoList;

    @ApiModelProperty("数量统计")
    private String countDetail;
//    //待检测数量
//    @ApiModelProperty("待检测数量")
//    private BigDecimal quantityUncheck;
//    //不良品数量
//    @ApiModelProperty("不良品数量")
//    private BigDecimal quantityUnquanlified;
//    //损耗数量
//    @ApiModelProperty("损耗数量")
//    private BigDecimal lossQuantity;
//    //留样&送检数量
//    @ApiModelProperty("留样&送检数量")
//    private BigDecimal leaveSampleQuantity;
}
