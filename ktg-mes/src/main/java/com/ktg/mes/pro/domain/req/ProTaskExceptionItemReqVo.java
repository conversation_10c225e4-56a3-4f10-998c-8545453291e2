package com.ktg.mes.pro.domain.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 生产异常标记项请求对象
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
@Data
public class ProTaskExceptionItemReqVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 异常记录ID（更新时必填，新增时为空） */
    @ApiModelProperty(value = "异常记录ID")
    private Long exceptionId;

    /** 原材料代码 */
    @ApiModelProperty(value = "原材料代码", required = true)
    @NotBlank(message = "原材料代码不能为空")
    private String itemCode;

    /** 异常原因（字典值） */
    @ApiModelProperty(value = "异常原因", required = true)
    @NotBlank(message = "异常原因不能为空")
    private String exceptionReason;

    /** 异常原因描述（字典展示名称） */
    @ApiModelProperty(value = "异常原因描述")
    private String exceptionReasonDesc;

    /** 异常数量 */
    @ApiModelProperty(value = "异常数量", required = true)
    @NotNull(message = "异常数量不能为空")
    private BigDecimal exceptionQuantity;

    /** 备注说明 */
    @ApiModelProperty(value = "备注说明")
    private String remark;

    /** 附件URL */
    @ApiModelProperty(value = "附件URL")
    private String attachmentUrl;

    /** 附件名称 */
    @ApiModelProperty(value = "附件名称")
    private String attachmentName;

    /** 附件大小（字节） */
    @ApiModelProperty(value = "附件大小")
    private Long attachmentSize;
}
