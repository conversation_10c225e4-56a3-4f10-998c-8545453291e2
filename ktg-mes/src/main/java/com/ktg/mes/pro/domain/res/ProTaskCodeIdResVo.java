package com.ktg.mes.pro.domain.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/5/7 14:05
 */
@Data
public class ProTaskCodeIdResVo {
    private Long taskId;
    private String taskCode;
    @ApiModelProperty("生产批次号")
    private String produceBatchCode;
    @ApiModelProperty("产品商品编码")
    private String itemCode;
    @ApiModelProperty("产品商品名称")
    private String itemName;
    @ApiModelProperty("车间名称")
    private String workshopName;
    @ApiModelProperty("本次接受收数量")
    private BigDecimal quantityProduced;
    @ApiModelProperty("生产完成时间")
    private Date endTime;
    @ApiModelProperty("生产类型")
    private Integer linkType;

}
