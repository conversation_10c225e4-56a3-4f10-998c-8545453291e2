package com.ktg.mes.pro.controller;

import com.ktg.common.annotation.Log;
import com.ktg.common.core.controller.BaseController;
import com.ktg.common.core.domain.AjaxResult;
import com.ktg.common.core.page.TableDataInfo;
import com.ktg.common.enums.BusinessType;
import com.ktg.common.utils.poi.ExcelUtil;
import com.ktg.mes.pro.domain.ProTaskBom;
import com.ktg.mes.pro.service.IProTaskBomService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 生产任务BOM组成Controller
 * 
 * <AUTHOR>
 * @date 2025-04-30
 */
@RestController
@RequestMapping("/md/bom")
public class ProTaskBomController extends BaseController
{
    @Autowired
    private IProTaskBomService proTaskBomService;

    /**
     * 查询生产任务BOM组成列表
     */
    @PreAuthorize("@ss.hasPermi('md:pro:protask:bom:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProTaskBom proTaskBom)
    {
        startPage();
        List<ProTaskBom> list = proTaskBomService.selectProTaskBomList(proTaskBom);
        return getDataTable(list);
    }

    /**
     * 导出生产任务BOM组成列表
     */
    @PreAuthorize("@ss.hasPermi('md:pro:protask:bom:export')")
    @Log(title = "生产任务BOM组成", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProTaskBom proTaskBom)
    {
        List<ProTaskBom> list = proTaskBomService.selectProTaskBomList(proTaskBom);
        ExcelUtil<ProTaskBom> util = new ExcelUtil<ProTaskBom>(ProTaskBom.class);
        util.exportExcel(response, list, "生产任务BOM组成数据");
    }

    /**
     * 获取生产任务BOM组成详细信息
     */
    @PreAuthorize("@ss.hasPermi('md:pro:protask:bom:query')")
    @GetMapping(value = "/{lineId}")
    public AjaxResult getInfo(@PathVariable("lineId") Long lineId)
    {
        return AjaxResult.success(proTaskBomService.selectProTaskBomByLineId(lineId));
    }

    /**
     * 新增生产任务BOM组成
     */
    @PreAuthorize("@ss.hasPermi('md:pro:protask:bom:add')")
    @Log(title = "生产任务BOM组成", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProTaskBom proTaskBom)
    {
        return toAjax(proTaskBomService.insertProTaskBom(proTaskBom));
    }

    /**
     * 修改生产任务BOM组成
     */
    @PreAuthorize("@ss.hasPermi('md:pro:protask:bom:edit')")
    @Log(title = "生产任务BOM组成", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProTaskBom proTaskBom)
    {
        return toAjax(proTaskBomService.updateProTaskBom(proTaskBom));
    }

    /**
     * 删除生产任务BOM组成
     */
    @PreAuthorize("@ss.hasPermi('md:pro:protask:bom:remove')")
    @Log(title = "生产任务BOM组成", businessType = BusinessType.DELETE)
	@DeleteMapping("/{lineIds}")
    public AjaxResult remove(@PathVariable Long[] lineIds)
    {
        return toAjax(proTaskBomService.deleteProTaskBomByLineIds(lineIds));
    }
}
