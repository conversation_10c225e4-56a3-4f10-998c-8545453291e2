package com.ktg.mes.pro.domain.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 生产报工新增参数
 * @date 2025/4/21 15:13
 */
@Data
public class ProFeedbackAddReqVo implements Serializable {
    //报工类型
    @ApiModelProperty(value = "报工类型", required = true, notes = "REGULAR-常规报工，EXTRA-额外报工")
    @NotBlank(message = "报工类型不能为空")
    private String feedbackType;

    //生产工单id
    @ApiModelProperty(value = "生产工单id")
    private Long workorderId;
    //生产任务id
    @ApiModelProperty(value = "生产任务id")
    private Long taskId;
    //待检数量
    @ApiModelProperty(value = "待检数量")
    private BigDecimal quantityUncheck;
    @ApiModelProperty(value = "报工数量")
    private BigDecimal quantityFeedback;
    //次品数量
    /**
     * 暂时拿掉次品数量字段
     */
//    @ApiModelProperty(value = "次品数量")
//    private BigDecimal quantityUnquanlified;
    //损耗数量
    @ApiModelProperty(value = "损耗数量")
    private BigDecimal lossQuantity;
    //留样&送检数量
    @ApiModelProperty(value = "留样数量")
    private BigDecimal leaveSampleQuantity;

    @ApiModelProperty(value = "送检数量")
    private BigDecimal inspectionQuantity;
    //报工人
    @ApiModelProperty(value = "报工人")
    private String nickName;
    //报工时间
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "报工时间")
    private Date feedbackTime;
    //审核人
    @ApiModelProperty(value = "审核人")
    private String recordNick;
    //备注
    @ApiModelProperty(value = "备注")
    private String remark;

    //原材料消耗详情
    @ApiModelProperty(value = "原材料消耗详情")
    private List<ProFeedbackIssueLossDetailAddResVo> lossDetail;

}
