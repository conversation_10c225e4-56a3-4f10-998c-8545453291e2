package com.ktg.mes.pro.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.dt.platform.wms.rpc.client.mes.adjust.AdjustQueryRequest;
import com.dt.platform.wms.rpc.client.mes.adjust.AdjustQueryResponse;
import com.dt.platform.wms.rpc.client.mes.adjust.IAdjustMesQuery;
import com.dt.platform.wms.rpc.client.mes.common.Result;
import com.ktg.common.annotation.Log;
import com.ktg.common.constant.UserConstants;
import com.ktg.common.core.controller.BaseController;
import com.ktg.common.core.domain.AjaxResult;
import com.ktg.common.core.page.TableDataInfo;
import com.ktg.common.enums.*;
import com.ktg.common.utils.ConvertUtil;
import com.ktg.common.utils.StringUtils;
import com.ktg.common.utils.poi.ExcelUtil;
import com.ktg.mes.md.domain.MdUnitMeasure;
import com.ktg.mes.md.domain.MdWorkstation;
import com.ktg.mes.md.service.IMdUnitMeasureService;
import com.ktg.mes.md.service.IMdWorkstationService;
import com.ktg.mes.pro.domain.*;
import com.ktg.mes.pro.domain.req.*;
import com.ktg.mes.pro.domain.res.*;
import com.ktg.mes.pro.service.*;
import com.ktg.mes.qc.domain.QcOqc;
import com.ktg.mes.qc.service.IQcOqcService;
import com.ktg.system.strategy.AutoCodeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 生产任务Controller
 * @menu 生产任务
 * <AUTHOR>
 * @date 2022-05-14
 */
@Slf4j
@RestController
@RequestMapping("/mes/pro/protask")
public class ProTaskController extends BaseController {
    @Autowired
    private IProTaskService proTaskService;
    @Autowired
    private IProTaskBomService proTaskBomService;
    @Autowired
    private IProFeedbackService proFeedbackService;
    @Autowired
    private IProWorkorderService proWorkorderService;
    @Autowired
    private IMdWorkstationService mdWorkstationService;

    @Autowired
    private IMdUnitMeasureService mdUnitMeasureService;

    @Autowired
    private IProWorkorderBomService proWorkorderBomService;
    @Autowired
    private IProRouteProductService proRouteProductService;
    @Autowired
    private IProProcessService proProcessService;
    @Autowired
    private IProRouteService proRouteService;
    @Autowired
    private AutoCodeUtil autoCodeUtil;
    @DubboReference
    private IAdjustMesQuery iAdjustMesQuery;
    @Autowired
    private IQcOqcService qcOqcService;
    @Autowired
    private IProTaskExceptionService proTaskExceptionService;


    /**
     * 查询生产任务列表
     * 此请求只会传入工单id
     */
    @GetMapping("/list")
    public TableDataInfo list(ProTaskListReqVo reqVo) {
        startPage();
        List<ProTask> list = proTaskService.proTaskPage(reqVo);
        TableDataInfo dataTable = getDataTable(list);
        Map<String,String> orderStatusMap = new HashMap<>(32);
        Map<String, List<ProTask>> warehouseRecptMap = list.stream().filter(task-> Objects.nonNull(task.getWmsOrderNo())).collect(Collectors.groupingBy(ProTask::getWarehouseCode));
        warehouseRecptMap.forEach((code, issueHeaders)->{
            AdjustQueryRequest adjustQueryRequest = new AdjustQueryRequest();
            adjustQueryRequest.setWarehouseCode(code);
            List<String> wmsOrderList = issueHeaders.stream().map(ProTask::getWmsOrderNo).collect(Collectors.toList());
            adjustQueryRequest.setCodeList(wmsOrderList);
            log.info("查询状态请求参数：{}", JSON.toJSONString(adjustQueryRequest));
            try {
                Result<List<AdjustQueryResponse>> listResult = iAdjustMesQuery.list(adjustQueryRequest);
                log.info("查询状态返回结果：{}", JSON.toJSONString(listResult));
                if (listResult.checkSuccess()){
                    List<AdjustQueryResponse> data = listResult.getData();
                    data.forEach(adjustQueryResponse -> orderStatusMap.put(adjustQueryResponse.getCode(),adjustQueryResponse.getStatusDesc()));
                }
            }catch (Exception e){
                log.error("查询状态异常：{}", e.getMessage(),e);
            }
        });

        // 查询工单信息获取单位
        Map<Long, String> workorderUnitMap = new HashMap<>();
        Map<String, String> unitDescMap = new HashMap<>();
        if (!list.isEmpty()) {
            List<Long> workorderIds = list.stream()
                .map(ProTask::getWorkorderId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

            if (!workorderIds.isEmpty()) {
                List<ProWorkorder> workorders = proWorkorderService.selectProWorkorderByWorkorderIds(workorderIds);
                workorderUnitMap = workorders.stream()
                    .filter(workorder -> Objects.nonNull(workorder.getUnitOfMeasure()))
                    .collect(Collectors.toMap(
                        ProWorkorder::getWorkorderId,
                        ProWorkorder::getUnitOfMeasure,
                        (existing, replacement) -> existing
                    ));

                // 查询单位描述
                Set<String> unitCodes = new HashSet<>(workorderUnitMap.values());
                for (String unitCode : unitCodes) {
                    if (StringUtils.isNotEmpty(unitCode)) {
                        MdUnitMeasure unitMeasure = mdUnitMeasureService.selectMdUnitByCode(unitCode);
                        if (Objects.nonNull(unitMeasure) && StringUtils.isNotEmpty(unitMeasure.getMeasureName())) {
                            unitDescMap.put(unitCode, unitMeasure.getMeasureName());
                        }
                    }
                }
            }
        }

        final Map<Long, String> finalWorkorderUnitMap = workorderUnitMap;
        final Map<String, String> finalUnitDescMap = unitDescMap;
        List<ProTaskListResVo> resVoList = list.stream().map(task -> {
            ProTaskListResVo proTaskListResVo = ConvertUtil.beanConvert(task, ProTaskListResVo.class);
            if (orderStatusMap.containsKey(proTaskListResVo.getWmsOrderNo())) {
                proTaskListResVo.setWmsOrderStatus(orderStatusMap.get(proTaskListResVo.getWmsOrderNo()));
            }

            // 设置单位和单位描述
            String unitCode = finalWorkorderUnitMap.get(task.getWorkorderId());
            if (Objects.nonNull(unitCode) && StringUtils.isNotEmpty(unitCode)) {
                proTaskListResVo.setUnitOfMeasure(unitCode);
                String unitDesc = finalUnitDescMap.get(unitCode);
                if (StringUtils.isNotEmpty(unitDesc)) {
                    proTaskListResVo.setUnitOfMeasureDesc(unitDesc);
                }
            }

            return proTaskListResVo;
        }).collect(Collectors.toList());
        dataTable.setRows(resVoList);
        return dataTable;
    }


    /**
     * 导出生产任务列表
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:protask:export')")
    @Log(title = "生产任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProTask proTask)
    {
        List<ProTask> list = proTaskService.selectProTaskList(proTask);
        ExcelUtil<ProTask> util = new ExcelUtil<ProTask>(ProTask.class);
        util.exportExcel(response, list, "生产任务数据");
    }

    /**
     * 获取生产任务详细信息
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:protask:query')")
    @GetMapping(value = "/{taskId}")
    public AjaxResult getInfo(@PathVariable("taskId") Long taskId) {
        if (Objects.isNull(taskId)){
            return AjaxResult.error("任务id不能为空");
        }
        try{
            ProTask proTask = proTaskService.selectProTaskByTaskId(taskId);
            if (Objects.isNull(proTask)){
                return AjaxResult.error("任务不存在");
            }
            ProTaskResVo proTaskResVo = ConvertUtil.beanConvert(proTask, ProTaskResVo.class);
            return AjaxResult.success(proTaskResVo);
        }catch (Exception e){
            log.error("获取生产任务详情失败：{}", e.getMessage(), e);
            return AjaxResult.error("获取生产任务详情失败");
        }
    }

    /**
     * 获取生产任务详细信息
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:protask:query')")
    @GetMapping(value = "/taskBomInfo/{taskId}")
    public AjaxResult taskBomInfo(@PathVariable("taskId") Long taskId) {
        ProTask proTask = proTaskService.selectProTaskByTaskId(taskId);
        if (Objects.isNull(proTask)){
            return AjaxResult.error("任务不存在");
        }

        // 查询工单BOM信息获取原材料单位
        Map<String, String> bomUnitMap = new HashMap<>();
        Map<String, String> bomUnitDescMap = new HashMap<>();
        if (Objects.nonNull(proTask.getWorkorderId())) {
            List<ProWorkorderBom> workorderBomList = proWorkorderBomService.selectProWorkorderBomListByWorkorderId(proTask.getWorkorderId());
            bomUnitMap = workorderBomList.stream()
                .filter(bom -> StringUtils.isNotEmpty(bom.getItemCode()) && StringUtils.isNotEmpty(bom.getUnitOfMeasure()))
                .collect(Collectors.toMap(
                    ProWorkorderBom::getItemCode,
                    ProWorkorderBom::getUnitOfMeasure,
                    (existing, replacement) -> existing
                ));

            // 查询原材料单位描述
            Set<String> unitCodes = new HashSet<>(bomUnitMap.values());
            for (String unitCode : unitCodes) {
                if (StringUtils.isNotEmpty(unitCode)) {
                    MdUnitMeasure unitMeasure = mdUnitMeasureService.selectMdUnitByCode(unitCode);
                    if (Objects.nonNull(unitMeasure) && StringUtils.isNotEmpty(unitMeasure.getMeasureName())) {
                        bomUnitDescMap.put(unitCode, unitMeasure.getMeasureName());
                    }
                }
            }
        }

        List<ProTaskBom> taskBomList = proTaskBomService.selectProTaskBomListByTaskId(taskId);
        final Map<String, String> finalBomUnitMap = bomUnitMap;
        final Map<String, String> finalBomUnitDescMap = bomUnitDescMap;
        List<ProTaskBomResVo> bomResVoList = taskBomList.stream().map(taskBomInfo -> {
            ProTaskBomResVo proTaskBomResVo = ConvertUtil.beanConvert(taskBomInfo, ProTaskBomResVo.class);
            if (Objects.nonNull(taskBomInfo.getAdjustDetailList())) {
                List<ProTaskBomAdjustDetailResVo> adjustDetailList = JSON.parseArray(taskBomInfo.getAdjustDetailList(), ProTaskBomAdjustDetailResVo.class);
                adjustDetailList.forEach(detail-> detail.setWmsOrderNo(proTask.getWmsOrderNo()));
                proTaskBomResVo.setAdjustDetailList(adjustDetailList);
            }

            // 设置原材料单位和单位描述
            String unitCode = finalBomUnitMap.get(taskBomInfo.getItemCode());
            if (StringUtils.isNotEmpty(unitCode)) {
                proTaskBomResVo.setUnitOfMeasure(unitCode);
                String unitDesc = finalBomUnitDescMap.get(unitCode);
                if (StringUtils.isNotEmpty(unitDesc)) {
                    proTaskBomResVo.setUnitOfMeasureDesc(unitDesc);
                }
            }

            return proTaskBomResVo;
        }).collect(Collectors.toList());
        ProTaskBomInfoResVo proTaskBomInfoResVo = new ProTaskBomInfoResVo();
        proTaskBomInfoResVo.setBomResVoList(bomResVoList);

        // 查询工单信息获取成品单位
        String workorderUnit = null;
        String workorderUnitDesc = null;
        if (Objects.nonNull(proTask.getWorkorderId())) {
            ProWorkorder workorder = proWorkorderService.selectProWorkorderByWorkorderId(proTask.getWorkorderId());
            if (Objects.nonNull(workorder) && StringUtils.isNotEmpty(workorder.getUnitOfMeasure())) {
                workorderUnit = workorder.getUnitOfMeasure();
                MdUnitMeasure unitMeasure = mdUnitMeasureService.selectMdUnitByCode(workorderUnit);
                if (Objects.nonNull(unitMeasure) && StringUtils.isNotEmpty(unitMeasure.getMeasureName())) {
                    workorderUnitDesc = unitMeasure.getMeasureName();
                }
            }
        }

        List<ProFeedback> proFeedbacks = proFeedbackService.selectProFeedbackByTaskId(taskId);
        final String finalWorkorderUnit = workorderUnit;
        final String finalWorkorderUnitDesc = workorderUnitDesc;

        List<ProWorkorderBom> proWorkorderBoms = proWorkorderBomService.selectProWorkorderBomListByWorkorderId(proTask.getWorkorderId());
        Map<String, ProWorkorderBom> itemCodeBomMap = proWorkorderBoms.stream().collect(Collectors.toMap(ProWorkorderBom::getItemCode, Function.identity(), (v1, v2) -> v1));

        List<ProFeedbackResVo> proFeedbackResVos = proFeedbacks.stream().map(feedback -> {
            ProFeedbackResVo proFeedbackResVo = ConvertUtil.beanConvert(feedback, ProFeedbackResVo.class);
            String issueLossDetail = feedback.getIssueLossDetail();
            if (StringUtils.isEmpty(issueLossDetail)) {
                proFeedbackResVo.setLossQuantity(BigDecimal.ZERO);
            } else {
                List<ProFeedbackIssueLossDetailResVo> issueLostDetailResVos = JSON.parseArray(issueLossDetail, ProFeedbackIssueLossDetailResVo.class);
                //设置明细
                BigDecimal quantityFeedback = feedback.getQuantityFeedback();
                BigDecimal quantity = feedback.getQuantity();
                for (ProFeedbackIssueLossDetailResVo issueLostDetailResVo : issueLostDetailResVos) {
                    ProWorkorderBom proWorkorderBom = itemCodeBomMap.get(issueLostDetailResVo.getItemCode());
                    if (Objects.nonNull(proWorkorderBom)) {
                        BigDecimal useRatio = proWorkorderBom.getUseRatio();
                        BigDecimal productQuantity = quantity;
                        //如果保税品
                        if (Objects.equals(feedback.getFeedbackType(), FeedbackTypeEnum.EXTRA.getCode())) {
                            if (Objects.equals(proWorkorderBom.getTradeType(), BomGoodsTradeTypeEnum.BONDED.getCode())) {
                                //如果报工数量大于0，则用总生产数减去报工数=保税粒子实际应计算数量
                                if (quantityFeedback.compareTo(BigDecimal.ZERO) > 0) {
                                    productQuantity = quantityFeedback;
                                } else {
                                    productQuantity = BigDecimal.ZERO;
                                }
                            }
                        }
                        BigDecimal productionUsage = productQuantity.multiply(useRatio);
                        issueLostDetailResVo.setProductionUsage(productionUsage);
                    }
                }
                proFeedbackResVo.setIssueLossDetailResVoList(issueLostDetailResVos);
                BigDecimal totalLoss = issueLostDetailResVos.stream().map(ProFeedbackIssueLossDetailResVo::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                proFeedbackResVo.setLossQuantity(totalLoss);
            }

            // 设置成品单位和单位描述
            if (StringUtils.isNotEmpty(finalWorkorderUnit)) {
                proFeedbackResVo.setUnitOfMeasure(finalWorkorderUnit);
                if (StringUtils.isNotEmpty(finalWorkorderUnitDesc)) {
                    proFeedbackResVo.setUnitOfMeasureDesc(finalWorkorderUnitDesc);
                }
            }

            return proFeedbackResVo;
        }).collect(Collectors.toList());
        proTaskBomInfoResVo.setFeedbackList(proFeedbackResVos);
        return AjaxResult.success(proTaskBomInfoResVo);
    }

    /**
     * 获取甘特图中需要显示的TASK，包括三种类型的内容：
     * 1.Project：基于时间范围搜索的生产工单转换而来的Project。
     *   搜索逻辑为：默认使用当前日期作为开始时间，搜索所有需求时间大于当前时间的生产工单
     * 2.Task：基于生产工单拆分到具体工作站后的生产任务转换而来的Task。
     * 3.Link：根据工序与工序之间的依赖关系转换而来的Link。
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:protask:list')")
    @GetMapping("/listGanttTaskList")
    public AjaxResult getGanttTaskList(ProWorkorder proWorkorder){
        GanttTask ganttTask = new GanttTask();
        List<GanttData> ganttData = new ArrayList<GanttData>();
        List<GanttLink> ganttLinks = new ArrayList<GanttLink>();

        //查询所有的WorkOrder
        List<ProWorkorder> workorders = proWorkorderService.selectProWorkorderList(proWorkorder);

        //为每个workOrder生成type=project的GanttData
        //为每个proTask生产type=task的GanttData
        ProTask param = new ProTask();
        if(CollUtil.isNotEmpty(workorders)){
            for (ProWorkorder workorder: workorders) {
                //先添加当前的生产工单TASK
                GanttData wdata = new GanttData();
                wdata.setId("MO"+workorder.getWorkorderId().toString());
                wdata.setText(new StringBuilder().append(workorder.getProductName()).append(workorder.getQuantity().stripTrailingZeros().toPlainString()).append(workorder.getUnitOfMeasure()).toString());//默认使用“[产品]+[数量]+[单位]”格式。
                wdata.setProduct(workorder.getProductName());
                wdata.setQuantity(workorder.getQuantity());
                if(workorder.getParentId().longValue()!=0L){
                    wdata.setParent("MO"+workorder.getParentId());
                }
                BigDecimal produced = workorder.getQuantityProduced();
                BigDecimal quantitiy = workorder.getQuantity()==null?BigDecimal.ZERO:workorder.getQuantity();
                if (quantitiy.compareTo(BigDecimal.ZERO) == 0) {
                    wdata.setProgress(0.00f);
                }else{
                    wdata.setProgress( produced.divide(quantitiy, RoundingMode.HALF_UP).floatValue());
                }
                wdata.setDuration(0L);
                wdata.setType(UserConstants.GANTT_TASK_TYPE_PROJECT);
                ganttData.add(wdata);

                //查询当前生产工单下所有的生产任务
                param.setWorkorderId(workorder.getWorkorderId());
                List<ProTask> proTasks = proTaskService.selectProTaskList(param);
                if(CollUtil.isNotEmpty(proTasks)){
                    for (ProTask task:proTasks
                         ) {
                        GanttData data = new GanttData();
                        data.setId(task.getTaskId().toString());//使用生产任务的ID作为甘特图TASK的ID
                        data.setText(new StringBuilder().append(task.getItemName()).append(task.getQuantity().stripTrailingZeros().toPlainString()).append(task.getUnitOfMeasure()).toString()); //默认使用“[产品]+[数量]+[单位]”格式。
                        data.setColor(task.getColorCode());
                        data.setDuration(task.getDuration());
                        data.setStart_date(task.getStartTime());
                        data.setParent("MO"+workorder.getWorkorderId().toString());//这里要设置为"MO+生产工单ID"的格式
                        data.setProduct(task.getItemName());
                        data.setQuantity(task.getQuantity());
                        data.setProcess(task.getProcessName());
                        data.setWorkstation(task.getWorkstationName());
                        BigDecimal taskProduced = task.getQuantityProduced() ==null?BigDecimal.ZERO:task.getQuantityProduced();
                        BigDecimal taskQuantity = task.getQuantity()  ==null?BigDecimal.ZERO:task.getQuantity();
                        // 处理特殊情况：任务总量为0
                        if (taskQuantity.compareTo(BigDecimal.ZERO) == 0) {
                            // 当任务总量为0时，根据业务逻辑设置进度
                            // 这里选择返回0%，表示任务未开始或无效
                            data.setProgress(0f);
                        } else {
                            // 正常计算进度，结果保留4位小数并四舍五入
                            BigDecimal progress = taskProduced.divide(taskQuantity, 4, RoundingMode.HALF_UP);
                            data.setProgress(progress.floatValue());
                        }
                        data.setType(UserConstants.GANTT_TASK_TYPE_TASK);
                        ganttData.add(data);
                    }
                }
            }
        }

        ganttTask.setData(ganttData);
        ganttTask.setLinks(ganttLinks);
        return AjaxResult.success(ganttTask);
    }

    /**
     * 按照最新的模式只展示工序级别的生产进度
     * @return
     */
    @GetMapping("/listTaskListByWorkorder")
    public AjaxResult getWorkorderProcessTypeTaskList(ProWorkorder proWorkorder){
        if(!StringUtils.isNotNull(proWorkorder.getWorkorderId())){
            return AjaxResult.error("请选择具体的生产工单!");
        }

        ProWorkorder workorder = proWorkorderService.selectProWorkorderByWorkorderId(proWorkorder.getWorkorderId());
        if(StringUtils.isNotNull(workorder)){
            //检查当前的产品是否配置了对应的工艺路线
            ProRouteProduct param = new ProRouteProduct();
            param.setItemId(workorder.getProductId());
            List<ProRouteProduct> routes = proRouteProductService.selectProRouteProductList(param);
            if(CollectionUtils.isEmpty(routes)){
                return AjaxResult.error("当前工单生产的产品，未配置对应的生产工艺流程！");
            }
        }

        //根据生产工单查询每个工序的生产情况
        List<ProTask> tasks = proTaskService.selectProTaskProcessViewByWorkorder(proWorkorder.getWorkorderId());
        return AjaxResult.success(tasks);
    }


    /**
     * 新增生产任务
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:protask:add')")
    @Log(title = "生产任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProTaskAddReqVo reqVo) {
        ProTask proTask = ConvertUtil.beanConvert(reqVo, ProTask.class);
        if(proTask.getQuantity().compareTo(BigDecimal.ZERO) < 0){
            return AjaxResult.error("排产数量必须大于0！");
        }

        if(!StringUtils.isNotNull(proTask.getWorkstationId())){
            return AjaxResult.error("请选择工作站！");
        }

        if(proTask.getDuration()<=0){
            return AjaxResult.error("生产时长必须大于0！");
        }

        //生产工单
        ProWorkorder order = proWorkorderService.selectProWorkorderByWorkorderId(proTask.getWorkorderId());
        if (Objects.isNull(order)){
            return AjaxResult.error("生产工单不存在！");
        }

        // S TO F模式下的生产任务创建限制
        if (WorkorderTaskLinkTypeEnum.isSToF(order.getLinkType())) {
            AjaxResult sToFValidationResult = validateSToFTaskCreation(order.getWorkorderId());
            if (sToFValidationResult != null) {
                return sToFValidationResult;
            }
        }
        List<ProTask> proTaskList = proTaskService.selectProTaskListByWorkorderId(order.getWorkorderId());
        BigDecimal totalTaskQuantity = BigDecimal.ZERO;
        for (ProTask task : proTaskList) {
            if (Objects.equals(task.getStatus(),OrderStatusEnum.FINISHED.getCode())){
                totalTaskQuantity =  totalTaskQuantity.add(task.getQuantityProduced());
            }else if (Objects.equals(task.getStatus(),OrderStatusEnum.PRODUCING.getCode())){
                totalTaskQuantity =  totalTaskQuantity.add(task.getQuantity());
            }
        }
        if (totalTaskQuantity.add(reqVo.getQuantity()).compareTo(order.getQuantity())>0){
            return AjaxResult.error("本次排产数量大于工单数量,已排产"+totalTaskQuantity+",请调整。");
        }
        MdWorkstation mdWorkstation = mdWorkstationService.selectMdWorkstationByWorkstationId(proTask.getWorkstationId());
        proTask.setWorkstationCode(mdWorkstation.getWorkstationCode());
        proTask.setWorkstationName(mdWorkstation.getWorkstationName());
        proTask.setWorkshopId(mdWorkstation.getWorkshopId());
        proTask.setWorkshopName(mdWorkstation.getWorkshopName());
        proTask.setWarehouseCode(order.getWarehouseCode());
        proTask.setWarehouseName(order.getWarehouseName());
        proTask.setOwnerCode(order.getOwnerCode());
        proTask.setOwnerName(order.getOwnerName());
        proTask.setWorkorderCode(order.getWorkorderCode());
        proTask.setWorkorderName(order.getWorkorderName());
        proTask.setItemId(order.getProductId());
        proTask.setItemCode(order.getProductCode());
        proTask.setItemName(order.getProductName());
        proTask.setSpecification(order.getProductSpc());
        proTask.setUnitOfMeasure(order.getUnitOfMeasure());
        proTask.setClientId(order.getClientId());
        proTask.setClientCode(order.getClientCode());
        proTask.setClientName(order.getClientName());
        //生产批次号=工单下的生产批次号
        proTask.setProduceBatchCode(order.getProduceBatchCode());
        //生产方式=工单下的生产方式
        proTask.setLinkType(order.getLinkType());

        //工艺信息
        if(StringUtils.isNotNull(proTask.getRouteId())){
            ProRoute route = proRouteService.selectProRouteByRouteId(proTask.getRouteId());
            if(StringUtils.isNotNull(route)){
                proTask.setRouteCode(route.getRouteCode());
            }else {
                return AjaxResult.error("当前生产任务对应的工艺路线信息无效！"+proTask.getRouteId());
            }
        }

        //工序信息
        ProProcess process = proProcessService.selectProProcessByProcessId(proTask.getProcessId());
        if(StringUtils.isNotNull(process)){
            proTask.setProcessId(process.getProcessId());
            proTask.setProcessCode(process.getProcessCode());
            proTask.setProcessName(process.getProcessName());
        }else{
            return AjaxResult.error("当前生产任务对应的工序信息无效！"+proTask.getProcessId());
        }


        //自动生成任务编号和名称
        proTask.setTaskCode(autoCodeUtil.genSerialCode(UserConstants.TASK_CODE,null));
        proTask.setTaskName(new StringBuilder().append(proTask.getItemName()).append("【").append(proTask.getQuantity().toString()).append("】").toString());
        //创建就为生产中
        proTask.setStatus(OrderStatusEnum.PRODUCING.getCode());

        return toAjax(proTaskService.insertProTask(proTask));
    }

    /**
     * 修改生产任务
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:protask:edit')")
    @Log(title = "生产任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProTask proTask)
    {
        ProTask theTask = proTaskService.selectProTaskByTaskId(proTask.getTaskId());

        if(proTask.getQuantity().compareTo(BigDecimal.ZERO) !=1){
            return AjaxResult.error("排产数量必须大于0！");
        }

        if(!StringUtils.isNotNull(proTask.getWorkstationId())){
            proTask.setWorkstationId(theTask.getWorkstationId());
        }

        if(!StringUtils.isNotNull(proTask.getWorkorderId())){
            proTask.setWorkorderId(theTask.getWorkorderId());
        }

        if(!StringUtils.isNotNull(proTask.getProcessId())){
            proTask.setProcessId(theTask.getProcessId());
        }

        if(!StringUtils.isNotNull(proTask.getRouteId())){
            proTask.setRouteId(theTask.getRouteId());
        }

        if(proTask.getDuration()<=0){
            proTask.setDuration(theTask.getDuration());
        }

        //生产工单
        ProWorkorder order = proWorkorderService.selectProWorkorderByWorkorderId(proTask.getWorkorderId());
        if(!StringUtils.isNotNull(order)){
            return AjaxResult.error("生产工单不存在!");
        }
        proTask.setWorkorderCode(order.getWorkorderCode());
        proTask.setWorkorderName(order.getWorkorderName());
        proTask.setItemId(order.getProductId());
        proTask.setItemCode(order.getProductCode());
        proTask.setItemName(order.getProductName());
        proTask.setSpecification(order.getProductSpc());
        proTask.setUnitOfMeasure(order.getUnitOfMeasure());
        proTask.setClientId(order.getClientId());
        proTask.setClientCode(order.getClientCode());
        proTask.setClientName(order.getClientName());
        proTask.setProduceBatchCode(order.getProduceBatchCode());

        //工艺信息
        if(StringUtils.isNotNull(proTask.getRouteId())){
            ProRoute route = proRouteService.selectProRouteByRouteId(proTask.getRouteId());
            if(StringUtils.isNotNull(route)){
                proTask.setRouteCode(route.getRouteCode());
            }else {
                return AjaxResult.error("当前生产任务对应的工艺路线信息无效！"+proTask.getRouteId());
            }
        }

        //工序信息
        ProProcess process = proProcessService.selectProProcessByProcessId(proTask.getProcessId());
        if(StringUtils.isNotNull(process)){
            proTask.setProcessId(process.getProcessId());
            proTask.setProcessCode(process.getProcessCode());
            proTask.setProcessName(process.getProcessName());
        }else{
            return AjaxResult.error("当前生产任务对应的工序信息无效！"+proTask.getProcessId());
        }

        List<ProTask> proTaskList = proTaskService.selectProTaskListByWorkorderId(order.getWorkorderId());
        BigDecimal totalTaskQuantity = BigDecimal.ZERO;
        for (ProTask task : proTaskList) {
            if (Objects.equals(task.getStatus(),OrderStatusEnum.FINISHED.getCode())){
                totalTaskQuantity =  totalTaskQuantity.add(task.getQuantityProduced());
            }else if (Objects.equals(task.getStatus(),OrderStatusEnum.PRODUCING.getCode())){
                totalTaskQuantity =  totalTaskQuantity.add(task.getQuantity());
            }
        }
        if (totalTaskQuantity.add(proTask.getQuantity()).compareTo(order.getQuantity())>0){
            return AjaxResult.error("本次排产数量大于工单数量,已排产"+totalTaskQuantity+",请调整。");
        }
        proTask.setTaskName(new StringBuilder().append(proTask.getItemName()).append("【").append(proTask.getQuantity().toString()).append("】").toString());
        return toAjax(proTaskService.updateProTask(proTask));
    }

    /**
     * 删除生产任务
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:protask:remove')")
    @Log(title = "生产任务", businessType = BusinessType.DELETE)
	@DeleteMapping("/{taskIds}")
    public AjaxResult remove(@PathVariable Long[] taskIds)
    {
        return toAjax(proTaskService.deleteProTaskByTaskIds(taskIds));
    }

    /**
     * 余料清点
     * @param taskId
     * @return
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:protask:leftView')")
    @GetMapping("/leftView/{taskId}")
    public AjaxResult leftView(@PathVariable("taskId") Long taskId){
        ProTaskLeftViewResVo resVo = proTaskService.leftView(taskId);
        logger.info("leftView:{}", JSON.toJSONString(resVo));
        return AjaxResult.success(resVo);
    }

    /**
     * 余料清点提交
     * 1.调用wms
     * ● 报工单 - 【审核完成】：MES手工创建，与库存调整(调增)1:1关系；对象为成品调增
     * ● 生产任务 - 【完结】：MES手工创建，与库存调整(调减)1:1关系；对象为原材料调减
     * 2.生成出货检测单
     * @param proTaskLeftSubmitReqVo
     * @return
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:protask:leftView')")
    @PostMapping("/leftItemSubmit")
    public AjaxResult leftItemSubmit(@RequestBody ProTaskLeftSubmitReqVo proTaskLeftSubmitReqVo){
        logger.info("leftView:{}", JSON.toJSONString(proTaskLeftSubmitReqVo));
        synchronized (this){
            try {
                proTaskService.leftItemSubmitProcess(proTaskLeftSubmitReqVo);
            }catch (Exception e){
                logger.error("调用库存调减接口失败,error={}",e.getMessage(),e);
                return AjaxResult.error("调用库存调减接口失败,"+e.getMessage());
            }
        }
        return AjaxResult.success("提交成功");
    }

    /**
     * 根据生产工单查询任务列表
     * @param workorderId
     * @return
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:protask:list')")
    @GetMapping("/listTaskListByWorkorder/{workorderId}")
    public AjaxResult listTaskListByWorkorder(@PathVariable Long workorderId){
        List<ProTaskCodeIdResVo> proTaskCodeIdResVos = proTaskService.listProTaskByWorkorderId(workorderId);
        return AjaxResult.success(proTaskCodeIdResVos);
    }

    /**
     * 根据生产工单查询未完成任务列表
     * @param workorderId
     * @return
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:protask:list')")
    @GetMapping("/listNotFinishTaskListByWorkorder/{workorderId}")
    public AjaxResult listNotFinishTaskListByWorkorder(@PathVariable Long workorderId){
        List<ProTaskCodeIdResVo> proTaskCodeIdResVos = proTaskService.listNotFinishProTaskByWorkorderId(workorderId);
        return AjaxResult.success(proTaskCodeIdResVos);
    }

    /**
     * 根据生产工单与状态查询任务列表
     * @param workorderId
     * @return
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:protask:list')")
    @GetMapping("/listTaskListByWorkorderAndStatus")
    public AjaxResult listTaskListByWorkorderAndStatus(TaskListQueryReqVo reqVo){
        List<ProTaskCodeIdResVo> proTaskCodeIdResVos = proTaskService.listProTaskByWorkorderIdAndStatus(reqVo);
        return AjaxResult.success(proTaskCodeIdResVos);
    }

    @PreAuthorize("@ss.hasPermi('mes:pro:protask:list')")
    @GetMapping("/listQcOqcTaskByWorkorder/{workorderId}")
    public AjaxResult listQcOqcTaskByWorkorder(@PathVariable Long workorderId){
        List<ProTaskCodeIdResVo> proTaskCodeIdResVos = proTaskService.listProTaskByWorkorderId(workorderId);
        List<Long> taskIdList = proTaskCodeIdResVos.stream().map(ProTaskCodeIdResVo::getTaskId).distinct().collect(Collectors.toList());
//        List<WmProductRecpt> wmProductRecpts =wmProductRecptService.selectWmProductRecptListByTaskId(taskIdList);
//        if (!CollectionUtils.isEmpty(wmProductRecpts)){
//            List<Long> existIdList = wmProductRecpts.stream().map(WmProductRecpt::getTaskId).distinct().collect(Collectors.toList());
//            proTaskCodeIdResVos = proTaskCodeIdResVos.stream().filter(task -> !existIdList.contains(task.getTaskId())).collect(Collectors.toList());
//        }
        List<QcOqc> qcOqcList =qcOqcService.selectQcOqcListByTaskId(taskIdList);
        if (!CollectionUtils.isEmpty(qcOqcList)){
            List<Long> existIdList = qcOqcList.stream().map(QcOqc::getTaskId).distinct().collect(Collectors.toList());
            proTaskCodeIdResVos = proTaskCodeIdResVos.stream().filter(task -> !existIdList.contains(task.getTaskId())).collect(Collectors.toList());
        }
        return AjaxResult.success(proTaskCodeIdResVos);
    }

    /**
     * 校验生产任务状态是否允许添加异常标记
     * @param taskId 生产任务ID
     * @return 校验结果
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:protask:query')")
    @GetMapping("/validateTaskStatusForException/{taskId}")
    public AjaxResult validateTaskStatusForException(@PathVariable("taskId") Long taskId) {
        if (Objects.isNull(taskId)) {
            return AjaxResult.error("任务ID不能为空");
        }

        try {
            boolean isValid = proTaskExceptionService.validateTaskStatusForException(taskId);
            if (!isValid) {
                return AjaxResult.error("生产任务状态不是生产中，无法添加异常标记");
            }
            return AjaxResult.success("任务状态校验通过");
        } catch (Exception e) {
            log.error("校验任务状态失败：{}", e.getMessage(), e);
            return AjaxResult.error("校验任务状态失败");
        }
    }

    /**
     * 获取生产任务的BOM原材料列表
     * @param taskId 生产任务ID
     * @return BOM原材料列表
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:protask:query')")
    @GetMapping("/getTaskBomMaterials/{taskId}")
    public AjaxResult getTaskBomMaterials(@PathVariable("taskId") Long taskId) {
        if (Objects.isNull(taskId)) {
            return AjaxResult.error("任务ID不能为空");
        }
        try {
            List<ProTaskExceptionResVo> materials = proTaskExceptionService.getTaskBomMaterials(taskId);
            return AjaxResult.success(materials);
        } catch (Exception e) {
            log.error("获取任务BOM原材料列表失败：{}", e.getMessage(), e);
            return AjaxResult.error("获取任务BOM原材料列表失败");
        }
    }

    /**
     * 查询生产任务异常标记列表
     * @param taskId 生产任务ID
     * @return 异常标记列表
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:protask:query')")
    @GetMapping("/getTaskExceptions/{taskId}")
    public AjaxResult getTaskExceptions(@PathVariable("taskId") Long taskId) {
        if (Objects.isNull(taskId)) {
            return AjaxResult.error("任务ID不能为空");
        }

        try {
            List<ProTaskExceptionResVo> exceptions = proTaskExceptionService.selectProTaskExceptionByTaskId(taskId);
            return AjaxResult.success(exceptions);
        } catch (Exception e) {
            log.error("查询任务异常标记列表失败：{}", e.getMessage(), e);
            return AjaxResult.error("查询任务异常标记列表失败");
        }
    }

    /**
     * 批量新增或更新生产异常标记
     * @param reqVo 批量异常标记请求对象
     * @return 操作结果
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:protask:edit')")
    @Log(title = "生产异常标记", businessType = BusinessType.INSERT)
    @PostMapping("/batchSaveOrUpdateTaskException")
    public AjaxResult batchSaveOrUpdateTaskException(@RequestBody ProTaskExceptionBatchReqVo reqVo) {
        if (Objects.isNull(reqVo)) {
            return AjaxResult.error("请求参数不能为空");
        }

        if (Objects.isNull(reqVo.getTaskId())) {
            return AjaxResult.error("生产任务ID不能为空");
        }

        if (Objects.isNull(reqVo.getExceptionList()) || reqVo.getExceptionList().isEmpty()) {
            return AjaxResult.error("异常标记列表不能为空");
        }

        // 校验每一行数据
        for (int i = 0; i < reqVo.getExceptionList().size(); i++) {
            ProTaskExceptionItemReqVo item = reqVo.getExceptionList().get(i);
            String prefix = "第" + (i + 1) + "行：";

            if (StringUtils.isEmpty(item.getItemCode())) {
                return AjaxResult.error(prefix + "原材料代码不能为空");
            }

            if (StringUtils.isEmpty(item.getExceptionReason())) {
                return AjaxResult.error(prefix + "异常原因不能为空");
            }

            // 如果前端没有传递异常原因描述，则根据异常原因代码自动设置
            if (StringUtils.isEmpty(item.getExceptionReasonDesc())) {
                // 这里可以添加从字典获取描述的逻辑，或者在Service层处理
            }

            if (Objects.isNull(item.getExceptionQuantity()) || item.getExceptionQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                return AjaxResult.error(prefix + "异常数量必须大于0");
            }
        }

        try {
            int result = proTaskExceptionService.batchSaveOrUpdateProTaskException(reqVo);
            return AjaxResult.success("操作成功，共处理 " + result + " 条记录");
        } catch (Exception e) {
            log.error("批量保存生产异常标记失败：{}", e.getMessage(), e);
            return AjaxResult.error("批量保存生产异常标记失败：" + e.getMessage());
        }
    }

    /**
     * 删除单条生产异常标记
     * @param exceptionId 异常记录ID
     * @return 操作结果
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:protask:edit')")
    @Log(title = "生产异常标记", businessType = BusinessType.DELETE)
    @DeleteMapping("/deleteTaskException/{exceptionId}")
    public AjaxResult deleteTaskException(@PathVariable("exceptionId") Long exceptionId) {
        if (Objects.isNull(exceptionId)) {
            return AjaxResult.error("异常记录ID不能为空");
        }

        try {
            // 先查询记录是否存在
            ProTaskException exception = proTaskExceptionService.selectProTaskExceptionByExceptionId(exceptionId);
            if (Objects.isNull(exception)) {
                return AjaxResult.error("异常记录不存在");
            }

            // 校验任务状态（只有生产中的任务才能删除异常记录）
            if (!proTaskExceptionService.validateTaskStatusForException(exception.getTaskId())) {
                return AjaxResult.error("生产任务状态不是生产中，无法删除异常标记");
            }

            int result = proTaskExceptionService.deleteProTaskExceptionByExceptionId(exceptionId);
            return toAjax(result);
        } catch (Exception e) {
            log.error("删除生产异常标记失败：{}", e.getMessage(), e);
            return AjaxResult.error("删除生产异常标记失败：" + e.getMessage());
        }
    }

    /**
     * 验证S TO F模式下的生产任务创建限制
     * 同一生产工单下若需要新建生产任务时，前提必须是该生产工单下不存在[生产中]的生产任务时允许创建
     *
     * @param workorderId 生产工单ID
     * @return 验证结果，null表示验证通过
     */
    private AjaxResult validateSToFTaskCreation(Long workorderId) {
        List<ProTask> existingTasks = proTaskService.selectProTaskListByWorkorderId(workorderId);

        for (ProTask task : existingTasks) {
            if (OrderStatusEnum.PRODUCING.getCode().equals(task.getStatus())) {
                log.warn("S TO F模式下，生产工单 {} 存在生产中的任务 {}，不允许创建新任务", workorderId, task.getTaskCode());
                return AjaxResult.error("S TO F模式下，该生产工单存在生产中的任务，不允许创建新任务");
            }
        }

        return null;
    }
}
