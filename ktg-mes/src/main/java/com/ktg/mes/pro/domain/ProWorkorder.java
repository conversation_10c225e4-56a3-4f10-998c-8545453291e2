package com.ktg.mes.pro.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ktg.common.annotation.Excel;
import com.ktg.common.core.domain.TreeEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 生产工单对象 pro_workorder
 * 
 * <AUTHOR>
 * @date 2022-05-15
 */
@Data
public class ProWorkorder extends TreeEntity {
    private static final long serialVersionUID = 1L;

    /** 工单ID */
    private Long workorderId;

    /** 仓库编码 */
    @Excel(name = "仓库编码")
    private String warehouseCode;

    /** 仓库名称 */
    @Excel(name = "仓库名称")
    private String warehouseName;

    /** 工单编码 */
    @Excel(name = "工单编码")
    private String workorderCode;

    /** 工单名称 */
    @Excel(name = "工单名称")
    private String workorderName;

    /** 来源类型 */
    @Excel(name = "来源类型")
    private String orderSource;

    /** 来源单据 */
    @Excel(name = "来源单据")
    private String sourceCode;

    /** 产品ID */
    @Excel(name = "产品ID")
    private Long productId;

    /** 产品编号 */
    @Excel(name = "产品编号")
    private String productCode;

    /** 产品名称 */
    @Excel(name = "产品名称")
    private String productName;

    /** 规格型号 */
    @Excel(name = "规格型号")
    private String productSpc;

    /** 单位 */
    @Excel(name = "单位")
    private String unitOfMeasure;

    /** 生产数量 */
    @Excel(name = "生产数量")
    private BigDecimal quantity;

    /** 已生产数量 */
    @Excel(name = "已生产数量")
    private BigDecimal quantityProduced;

    /** 调整数量 */
    @Excel(name = "调整数量")
    private BigDecimal quantityChanged;

    /** 已排产数量 */
    @Excel(name = "已排产数量")
    private BigDecimal quantityScheduled;

    /** 客户ID */
    @Excel(name = "客户ID")
    private Long clientId;

    /** 客户编码 */
    @Excel(name = "客户编码")
    private String clientCode;

    /** 客户名称 */
    @Excel(name = "客户名称")
    private String clientName;

    /** 需求日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "需求日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date requestDate;

    /** 父工单 */
    @Excel(name = "父工单")
    private Long parentId;

    /** 所有父节点ID */
    @Excel(name = "所有父节点ID")
    private String ancestors;

    /** 单据状态 */
    @Excel(name = "单据状态")
    private String status;

    /** 生产方式（S_TO_S-允许多任务，S_TO_F-限制任务和报工） */
    @Excel(name = "生产方式")
    private String productionMode;

    /** 货主编码 */
    @Excel(name = "货主编码")
    private String ownerCode;

    /** 货主名称 */
    @Excel(name = "货主名称")
    private String ownerName;

    /** 车间id */
    @Excel(name = "车间id")
    private Long workshopId;

    /** 车间名称 */
    @Excel(name = "车间名称")
    private String workshopName;

    /** 出货检验 */
    @Excel(name = "出货检验")
    private String oqcCheck;

    /** 关键原材料要求-JSON格式 */
    @Excel(name = "关键原材料要求-JSON格式")
    private String keyItems;

    /** 入库关联单号 */
    @Excel(name = "入库关联单号")
    private String externalLinkBillNo;

    /** 外部关联行号 */
    @Excel(name = "外部关联行号")
    private String externalLinkLineNo;

    /** 生产要求 */
    @Excel(name = "生产要求")
    private String requirement;

    /** 生产日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生产日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date productionDate;

    /** 失效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "失效日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expirationDate;

    /** 保质期 */
    @Excel(name = "保质期")
    private Long shelfLife;

    /** 理论产量 */
    @Excel(name = "理论产量")
    private BigDecimal theoreticalOutput;

    /** 品牌 */
    @Excel(name = "品牌")
    private String brand;

    /** 上游推送状态，N为未推送，不在mes展示；Y为已推送，D为已删除 */
    @Excel(name = "上游推送状态，N为未推送，不在mes展示；Y为已推送，D为已删除")
    private String submitStatus;

    /** 推送状态 */
    @Excel(name = "推送状态")
    private String pushStatus;

    /** 推送时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "推送时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date pushDate;

    /** 包装规格 */
    @Excel(name = "包装规格")
    private String skuWrap;

    /** 生产批次号 */
    @Excel(name = "生产批次号")
    private String produceBatchCode;

    /** 推送erp附件 */
    @Excel(name = "推送erp附件")
    private String pushErpAttach;

    /** 生产方式（1-S TO S，2-S TO F） */
    @Excel(name = "生产方式")
    private Integer linkType;

    /** 预留字段1 */
    @Excel(name = "预留字段1")
    private String attr1;

    /** 预留字段2 */
    @Excel(name = "预留字段2")
    private String attr2;

    /** 预留字段3 */
    @Excel(name = "预留字段3")
    private Long attr3;

    /** 预留字段4 */
    @Excel(name = "预留字段4")
    private Long attr4;

    /**  */
    @Excel(name = "")
    private String batchCode;

    /** 完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date finishDate;

    /**  */
    @Excel(name = "")
    private String workorderType;

    /**  */
    @Excel(name = "")
    private Long vendorId;

    /**  */
    @Excel(name = "")
    private String vendorCode;

    /**  */
    @Excel(name = "")
    private String vendorName;

    private List<ProTask> tasks;

}
