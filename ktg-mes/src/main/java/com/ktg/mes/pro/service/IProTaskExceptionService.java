package com.ktg.mes.pro.service;

import com.ktg.mes.pro.domain.ProTask;
import com.ktg.mes.pro.domain.ProTaskException;
import com.ktg.mes.pro.domain.req.ProTaskExceptionAddReqVo;
import com.ktg.mes.pro.domain.req.ProTaskExceptionBatchReqVo;
import com.ktg.mes.pro.domain.res.ProTaskExceptionResVo;

import java.util.List;

/**
 * 生产异常标记Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
public interface IProTaskExceptionService 
{
    /**
     * 查询生产异常标记
     * 
     * @param exceptionId 生产异常标记主键
     * @return 生产异常标记
     */
    public ProTaskException selectProTaskExceptionByExceptionId(Long exceptionId);

    /**
     * 查询生产异常标记列表
     * 
     * @param proTaskException 生产异常标记
     * @return 生产异常标记集合
     */
    public List<ProTaskException> selectProTaskExceptionList(ProTaskException proTaskException);

    /**
     * 根据任务ID查询生产异常标记列表
     * 
     * @param taskId 生产任务ID
     * @return 生产异常标记集合
     */
    public List<ProTaskExceptionResVo> selectProTaskExceptionByTaskId(Long taskId);

    /**
     * 根据任务ID列表查询生产异常标记列表
     * 
     * @param taskIdList 生产任务ID列表
     * @return 生产异常标记集合
     */
    public List<ProTaskExceptionResVo> selectProTaskExceptionByTaskIdList(List<Long> taskIdList);

    /**
     * 根据工单ID查询生产异常标记列表
     * 
     * @param workorderId 生产工单ID
     * @return 生产异常标记集合
     */
    public List<ProTaskExceptionResVo> selectProTaskExceptionByWorkorderId(Long workorderId);

    /**
     * 查询异常原因合并统计信息
     * 
     * @param taskId 生产任务ID
     * @return 合并统计结果
     */
    public List<ProTaskExceptionResVo> selectExceptionSummaryByTaskId(Long taskId);

    /**
     * 新增生产异常标记
     *
     * @param reqVo 生产异常标记请求对象
     * @return 结果
     */
    public int insertProTaskException(ProTaskExceptionAddReqVo reqVo);

    /**
     * 批量新增或更新生产异常标记
     *
     * @param reqVo 批量操作请求对象
     * @return 结果
     */
    public int batchSaveOrUpdateProTaskException(ProTaskExceptionBatchReqVo reqVo);

    /**
     * 修改生产异常标记
     * 
     * @param proTaskException 生产异常标记
     * @return 结果
     */
    public int updateProTaskException(ProTaskException proTaskException);

    /**
     * 批量删除生产异常标记
     * 
     * @param exceptionIds 需要删除的生产异常标记主键集合
     * @return 结果
     */
    public int deleteProTaskExceptionByExceptionIds(Long[] exceptionIds);

    /**
     * 删除生产异常标记信息
     * 
     * @param exceptionId 生产异常标记主键
     * @return 结果
     */
    public int deleteProTaskExceptionByExceptionId(Long exceptionId);

    /**
     * 校验生产任务状态是否允许添加异常标记
     *
     * @param taskId 生产任务ID
     * @return 校验结果
     */
    public boolean validateTaskStatusForException(Long taskId);

    /**
     * 校验生产任务状态是否允许添加异常标记
     *
     * @param proTask 生产任务对象
     * @return 校验结果
     */
    public boolean validateTaskStatusForException(ProTask proTask);

    /**
     * 获取生产任务的BOM原材料列表
     * 
     * @param taskId 生产任务ID
     * @return BOM原材料列表
     */
    public List<ProTaskExceptionResVo> getTaskBomMaterials(Long taskId);
}
