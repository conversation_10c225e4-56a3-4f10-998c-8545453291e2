package com.ktg.mes.pro.domain.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 生产任务添加参数
 * @date 2025/5/12 11:20
 */
@Data
public class ProTaskAddReqVo implements Serializable {
    @ApiModelProperty("工单id")
    private Long workorderId;
    @ApiModelProperty("工作站id")
    private Long workstationId;
    @ApiModelProperty("工艺流程ID")
    private Long routeId;
    @ApiModelProperty("工序ID")
    private Long processId;
    @ApiModelProperty("颜色编码")
    private String colorCode;
    @ApiModelProperty("生产时长")
    private Long duration;
    @ApiModelProperty("排产数量")
    private BigDecimal quantity;
    @ApiModelProperty("开始时间")
    private Date startTime;
    @ApiModelProperty("结束时间")
    private Date endTime;
}
