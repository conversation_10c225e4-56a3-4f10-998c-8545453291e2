package com.ktg.mes.pro.domain.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 生产工单分页查询接口
 * @date 2025/5/8 16:16
 */
@Data
public class ProWorkorderErpPageReqVo implements Serializable {
    @ApiModelProperty("工单编码")
    private String workorderCode;
    @ApiModelProperty("生产车间")
    private Long workshopId;
    @ApiModelProperty("货主名称")
    private String ownerCode;
    @ApiModelProperty("货主名称列表")
    private List<String> ownerCodeList;
    @ApiModelProperty("产品商品编码")
    private String productCode;
    @ApiModelProperty("产品商品名称")
    private String productName;
    @ApiModelProperty("需求时间开始")
    private Date requestDateBegin;
    @ApiModelProperty("需求时间结束")
    private Date requestDateEnd;
    @ApiModelProperty("推送时间开始")
    private Date pushDateBegin;
    @ApiModelProperty("推送时间结束")
    private Date pushDateEnd;
    @ApiModelProperty("状态")
    private String status;
    @ApiModelProperty("状态列表")
    private String multipleStatus;
    private List<String> statusList;
    @ApiModelProperty("品牌")
    private String brand;
    @ApiModelProperty("入库关联单号")
    private String externalLinkBillNo;
}
