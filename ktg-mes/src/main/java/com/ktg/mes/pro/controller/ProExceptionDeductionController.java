package com.ktg.mes.pro.controller;

import com.alibaba.fastjson.JSON;
import com.ktg.common.annotation.Log;
import com.ktg.common.core.controller.BaseController;
import com.ktg.common.core.domain.AjaxResult;
import com.ktg.common.core.page.TableDataInfo;
import com.ktg.common.enums.BusinessType;
import com.ktg.common.enums.ExceptionDeductionStatusEnum;
import com.ktg.common.utils.ConvertUtil;
import com.ktg.common.utils.poi.ExcelUtil;
import com.ktg.mes.pro.domain.ProExceptionDeduction;
import com.ktg.mes.pro.domain.req.ProExceptionDeductionCloseReqVo;
import com.ktg.mes.pro.domain.req.ProExceptionDeductionListReqVo;
import com.ktg.mes.pro.domain.res.ProExceptionDeductionResVo;
import com.ktg.mes.pro.service.IProExceptionDeductionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 异常扣减单Controller
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@Slf4j
@RestController
@RequestMapping("/mes/pro/exceptionDeduction")
public class ProExceptionDeductionController extends BaseController {
    
    @Autowired
    private IProExceptionDeductionService proExceptionDeductionService;

    /**
     * 查询异常扣减单列表
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:exceptionDeduction:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProExceptionDeductionListReqVo reqVo) {
        log.info("查询异常扣减单列表 reqVo={}", JSON.toJSONString(reqVo));
        startPage();
        
        ProExceptionDeduction queryParam = ConvertUtil.beanConvert(reqVo, ProExceptionDeduction.class);
        List<ProExceptionDeduction> list = proExceptionDeductionService.selectProExceptionDeductionList(queryParam);
        
        // 转换为响应VO并添加可执行操作
        List<ProExceptionDeductionResVo> resVoList = list.stream().map(this::convertToResVo).collect(Collectors.toList());
        
        return getDataTable(resVoList);
    }

    /**
     * 导出异常扣减单列表
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:exceptionDeduction:export')")
    @Log(title = "异常扣减单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProExceptionDeductionListReqVo reqVo) {
        ProExceptionDeduction queryParam = ConvertUtil.beanConvert(reqVo, ProExceptionDeduction.class);
        List<ProExceptionDeduction> list = proExceptionDeductionService.selectProExceptionDeductionList(queryParam);
        ExcelUtil<ProExceptionDeduction> util = new ExcelUtil<ProExceptionDeduction>(ProExceptionDeduction.class);
        util.exportExcel(response, list, "异常扣减单数据");
    }

    /**
     * 获取异常扣减单详细信息
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:exceptionDeduction:query')")
    @GetMapping(value = "/{deductionId}")
    public AjaxResult getInfo(@PathVariable("deductionId") Long deductionId) {
        ProExceptionDeduction deduction = proExceptionDeductionService.selectProExceptionDeductionByDeductionId(deductionId);
        if (deduction == null) {
            return AjaxResult.error("异常扣减单不存在");
        }
        ProExceptionDeductionResVo resVo = convertToResVo(deduction);
        return AjaxResult.success(resVo);
    }

    /**
     * 根据工单ID查询异常扣减单列表
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:exceptionDeduction:list')")
    @GetMapping("/workorder/{workorderId}")
    public AjaxResult getByWorkorderId(@PathVariable("workorderId") Long workorderId) {
        List<ProExceptionDeduction> list = proExceptionDeductionService.selectByWorkorderId(workorderId);
        List<ProExceptionDeductionResVo> resVoList = list.stream().map(this::convertToResVo).collect(Collectors.toList());
        return AjaxResult.success(resVoList);
    }

    /**
     * 领料操作
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:exceptionDeduction:issue')")
    @Log(title = "异常扣减单领料", businessType = BusinessType.UPDATE)
    @PostMapping("/issue/{deductionId}")
    public AjaxResult issue(@PathVariable("deductionId") Long deductionId) {
        log.info("异常扣减单领料操作 deductionId={}", deductionId);
        try {
            int result = proExceptionDeductionService.issueOperation(deductionId);
            if (result > 0) {
                return AjaxResult.success("领料操作成功");
            } else {
                return AjaxResult.error("领料操作失败");
            }
        } catch (Exception e) {
            log.error("异常扣减单领料操作失败", e);
            return AjaxResult.error("领料操作失败：" + e.getMessage());
        }
    }

    /**
     * 推送操作
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:exceptionDeduction:push')")
    @Log(title = "异常扣减单推送", businessType = BusinessType.UPDATE)
    @PostMapping("/push/{deductionId}")
    public AjaxResult push(@PathVariable("deductionId") Long deductionId) {
        log.info("异常扣减单推送操作 deductionId={}", deductionId);
        try {
            int result = proExceptionDeductionService.pushOperation(deductionId);
            if (result > 0) {
                return AjaxResult.success("推送操作成功");
            } else {
                return AjaxResult.error("推送操作失败");
            }
        } catch (Exception e) {
            log.error("异常扣减单推送操作失败", e);
            return AjaxResult.error("推送操作失败：" + e.getMessage());
        }
    }

    /**
     * 关闭操作
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:exceptionDeduction:close')")
    @Log(title = "异常扣减单关闭", businessType = BusinessType.UPDATE)
    @PostMapping("/close")
    public AjaxResult close(@Valid @RequestBody ProExceptionDeductionCloseReqVo reqVo) {
        log.info("异常扣减单关闭操作 reqVo={}", JSON.toJSONString(reqVo));
        try {
            int result = proExceptionDeductionService.closeOperation(reqVo.getDeductionId(), reqVo.getCloseReason());
            if (result > 0) {
                return AjaxResult.success("关闭操作成功");
            } else {
                return AjaxResult.error("关闭操作失败");
            }
        } catch (Exception e) {
            log.error("异常扣减单关闭操作失败", e);
            return AjaxResult.error("关闭操作失败：" + e.getMessage());
        }
    }

    /**
     * 删除异常扣减单
     */
    @PreAuthorize("@ss.hasPermi('mes:pro:exceptionDeduction:remove')")
    @Log(title = "异常扣减单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deductionIds}")
    public AjaxResult remove(@PathVariable Long[] deductionIds) {
        return toAjax(proExceptionDeductionService.deleteProExceptionDeductionByDeductionIds(deductionIds));
    }

    /**
     * 转换为响应VO并添加可执行操作
     */
    private ProExceptionDeductionResVo convertToResVo(ProExceptionDeduction deduction) {
        ProExceptionDeductionResVo resVo = ConvertUtil.beanConvert(deduction, ProExceptionDeductionResVo.class);
        
        // 根据状态设置可执行操作
        String[] availableActions = getAvailableActions(deduction.getStatus());
        resVo.setAvailableActions(availableActions);
        
        return resVo;
    }

    /**
     * 根据状态获取可执行操作
     */
    private String[] getAvailableActions(String status) {
        ExceptionDeductionStatusEnum statusEnum = ExceptionDeductionStatusEnum.getByCode(status);
        if (statusEnum == null) {
            return new String[0];
        }
        
        switch (statusEnum) {
            case COUNTING:
                return new String[0]; // 统计中状态无可执行操作
            case PENDING_ISSUE:
                return new String[]{"issue", "close"}; // 待领料状态可以领料和关闭
            case PENDING_PUSH:
                return new String[]{"push", "close"}; // 待推送状态可以推送和关闭
            case COMPLETED:
            case CLOSED:
            default:
                return new String[0]; // 已完成和已关闭状态无可执行操作
        }
    }
}
