package com.ktg.mes.pro.domain.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 生产工单编辑请求参数
 * @date 2025/4/17 13:45
 */
@Data
public class ProWorkOrderUpdateReqVo implements Serializable {
    /**
     * 工单id
     */
    private Long workorderId;

    /**
     * 工单数量
     */
    private BigDecimal quantity;

    /**
     * 货主编码
     */
    private String ownerCode;

    /**
     * 货主名称
     */
    private String ownerName;


    /**
     * 产品物料id
     */
    private Long productId;

    /**
     * 产品物料编码
     */
    private String productCode;

    /**
     * 产品物料名称
     */
    private String productName;


    /**
     * 车间id
     */
    private Long workshopId;

    /**
     * 出货检验
     */
    private boolean goodsCheck;

    /**
     * 需求日期
     */
    private Date requestDate;

    private String status;

    /**
     * 生产需求
     */
    private String requirement;

    /**
     * 生产日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date productionDate;

    /**
     * 关键原材料要求
     */
    private List<ProWorkOrderKeyMdItemReqVo> keyItems;

    /**
     * 出货检测
     */
    private String oqcCheck;

    /** 生产批次号 */
    private String produceBatchCode;

    /**
     * 备注
     */
    private String remark;
}
