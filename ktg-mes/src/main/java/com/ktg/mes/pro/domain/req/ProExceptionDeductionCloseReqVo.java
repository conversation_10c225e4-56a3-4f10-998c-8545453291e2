package com.ktg.mes.pro.domain.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 异常扣减单关闭请求VO
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
public class ProExceptionDeductionCloseReqVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 扣减单ID */
    @NotNull(message = "扣减单ID不能为空")
    private Long deductionId;

    /** 关闭原因 */
    @NotNull(message = "关闭原因不能为空")
    private String closeReason;
}
