package com.ktg.mes.pro.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ktg.mes.pro.domain.ProExceptionDeduction;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 异常扣减单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Mapper
public interface ProExceptionDeductionMapper extends BaseMapper<ProExceptionDeduction> {

    /**
     * 查询异常扣减单
     *
     * @param deductionId 异常扣减单主键
     * @return 异常扣减单
     */
    ProExceptionDeduction selectProExceptionDeductionByDeductionId(Long deductionId);

    /**
     * 查询异常扣减单列表
     *
     * @param proExceptionDeduction 异常扣减单
     * @return 异常扣减单集合
     */
    List<ProExceptionDeduction> selectProExceptionDeductionList(ProExceptionDeduction proExceptionDeduction);

    /**
     * 根据工单ID和物料编码查询异常扣减单
     *
     * @param workorderId 工单ID
     * @param itemCode 物料编码
     * @return 异常扣减单
     */
    ProExceptionDeduction selectByWorkorderIdAndItemCode(@Param("workorderId") Long workorderId, @Param("itemCode") String itemCode);

    /**
     * 根据工单ID查询异常扣减单列表
     *
     * @param workorderId 工单ID
     * @return 异常扣减单集合
     */
    List<ProExceptionDeduction> selectByWorkorderId(@Param("workorderId") Long workorderId);

    /**
     * 根据状态查询异常扣减单列表
     *
     * @param status 状态
     * @return 异常扣减单集合
     */
    List<ProExceptionDeduction> selectByStatus(@Param("status") String status);

    /**
     * 根据领料单ID查询异常扣减单列表
     *
     * @param issueId 领料单ID
     * @return 异常扣减单集合
     */
    List<ProExceptionDeduction> selectByIssueId(@Param("issueId") Long issueId);

    /**
     * 新增异常扣减单
     *
     * @param proExceptionDeduction 异常扣减单
     * @return 结果
     */
    int insertProExceptionDeduction(ProExceptionDeduction proExceptionDeduction);

    /**
     * 修改异常扣减单
     *
     * @param proExceptionDeduction 异常扣减单
     * @return 结果
     */
    int updateProExceptionDeduction(ProExceptionDeduction proExceptionDeduction);

    /**
     * 删除异常扣减单
     *
     * @param deductionId 异常扣减单主键
     * @return 结果
     */
    int deleteProExceptionDeductionByDeductionId(Long deductionId);

    /**
     * 批量删除异常扣减单
     *
     * @param deductionIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteProExceptionDeductionByDeductionIds(Long[] deductionIds);

    /**
     * 检查扣减单编号是否唯一
     *
     * @param proExceptionDeduction 异常扣减单
     * @return 异常扣减单
     */
    ProExceptionDeduction checkDeductionCodeUnique(ProExceptionDeduction proExceptionDeduction);
}
