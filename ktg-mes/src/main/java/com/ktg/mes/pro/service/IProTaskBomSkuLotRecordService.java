package com.ktg.mes.pro.service;


import com.ktg.mes.pro.domain.ProTaskBomSkuLotRecord;

import java.util.List;

/**
 * 生产任务BOM批次信息Service接口
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
public interface IProTaskBomSkuLotRecordService
{
    /**
     * 查询生产任务BOM批次信息
     *
     * @param recordId 生产任务BOM批次信息主键
     * @return 生产任务BOM批次信息
     */
    public ProTaskBomSkuLotRecord selectProTaskBomSkuLotRecordByRecordId(Long recordId);

    /**
     * 查询生产任务BOM批次信息列表
     *
     * @param proTaskBomSkuLotRecord 生产任务BOM批次信息
     * @return 生产任务BOM批次信息集合
     */
    public List<ProTaskBomSkuLotRecord> selectProTaskBomSkuLotRecordList(ProTaskBomSkuLotRecord proTaskBomSkuLotRecord);

    /**
     * 新增生产任务BOM批次信息
     *
     * @param proTaskBomSkuLotRecord 生产任务BOM批次信息
     * @return 结果
     */
    public int insertProTaskBomSkuLotRecord(ProTaskBomSkuLotRecord proTaskBomSkuLotRecord);

    /**
     * 修改生产任务BOM批次信息
     *
     * @param proTaskBomSkuLotRecord 生产任务BOM批次信息
     * @return 结果
     */
    public int updateProTaskBomSkuLotRecord(ProTaskBomSkuLotRecord proTaskBomSkuLotRecord);

    /**
     * 批量删除生产任务BOM批次信息
     *
     * @param recordIds 需要删除的生产任务BOM批次信息主键集合
     * @return 结果
     */
    public int deleteProTaskBomSkuLotRecordByRecordIds(Long[] recordIds);

    /**
     * 删除生产任务BOM批次信息信息
     *
     * @param recordId 生产任务BOM批次信息主键
     * @return 结果
     */
    public int deleteProTaskBomSkuLotRecordByRecordId(Long recordId);

    List<ProTaskBomSkuLotRecord> selectByTaskId(Long taskId);
}