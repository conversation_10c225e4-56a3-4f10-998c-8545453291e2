package com.ktg.mes.pro.service;

import com.ktg.mes.pro.domain.ProExceptionDeduction;

import java.math.BigDecimal;
import java.util.List;

/**
 * 异常扣减单Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
public interface IProExceptionDeductionService {
    
    /**
     * 查询异常扣减单
     * 
     * @param deductionId 异常扣减单主键
     * @return 异常扣减单
     */
    ProExceptionDeduction selectProExceptionDeductionByDeductionId(Long deductionId);

    /**
     * 查询异常扣减单列表
     * 
     * @param proExceptionDeduction 异常扣减单
     * @return 异常扣减单集合
     */
    List<ProExceptionDeduction> selectProExceptionDeductionList(ProExceptionDeduction proExceptionDeduction);

    /**
     * 根据工单ID和物料编码查询异常扣减单
     * 
     * @param workorderId 工单ID
     * @param itemCode 物料编码
     * @return 异常扣减单
     */
    ProExceptionDeduction selectByWorkorderIdAndItemCode(Long workorderId, String itemCode);

    /**
     * 根据工单ID查询异常扣减单列表
     * 
     * @param workorderId 工单ID
     * @return 异常扣减单集合
     */
    List<ProExceptionDeduction> selectByWorkorderId(Long workorderId);

    /**
     * 根据状态查询异常扣减单列表
     * 
     * @param status 状态
     * @return 异常扣减单集合
     */
    List<ProExceptionDeduction> selectByStatus(String status);

    /**
     * 新增异常扣减单
     * 
     * @param proExceptionDeduction 异常扣减单
     * @return 结果
     */
    int insertProExceptionDeduction(ProExceptionDeduction proExceptionDeduction);

    /**
     * 修改异常扣减单
     * 
     * @param proExceptionDeduction 异常扣减单
     * @return 结果
     */
    int updateProExceptionDeduction(ProExceptionDeduction proExceptionDeduction);

    /**
     * 批量删除异常扣减单
     * 
     * @param deductionIds 需要删除的异常扣减单主键集合
     * @return 结果
     */
    int deleteProExceptionDeductionByDeductionIds(Long[] deductionIds);

    /**
     * 删除异常扣减单信息
     * 
     * @param deductionId 异常扣减单主键
     * @return 结果
     */
    int deleteProExceptionDeductionByDeductionId(Long deductionId);

    /**
     * 检查扣减单编号是否唯一
     * 
     * @param proExceptionDeduction 异常扣减单
     * @return 结果
     */
    String checkDeductionCodeUnique(ProExceptionDeduction proExceptionDeduction);

    /**
     * 创建或更新异常扣减单
     * 基于生产工单+物料SKU的数据维度创建代扣单数据
     * 
     * @param workorderId 工单ID
     * @param itemCode 物料编码
     * @param itemName 物料名称
     * @param specification 规格型号
     * @param unitOfMeasure 单位
     * @param deductionQuantity 代扣数量
     * @param workshopId 车间ID
     * @param workshopName 车间名称
     * @param warehouseCode 仓库编码
     * @param warehouseName 仓库名称
     * @param ownerCode 货主编码
     * @param ownerName 货主名称
     * @return 异常扣减单
     */
    ProExceptionDeduction createOrUpdateExceptionDeduction(Long workorderId, String itemCode, String itemName, 
                                                          String specification, String unitOfMeasure, 
                                                          BigDecimal deductionQuantity, Long workshopId, 
                                                          String workshopName, String warehouseCode, 
                                                          String warehouseName, String ownerCode, String ownerName);

    /**
     * 领料操作
     * 创建对应领料单，状态变更为待推送
     * 
     * @param deductionId 扣减单ID
     * @return 结果
     */
    int issueOperation(Long deductionId) throws Exception;

    /**
     * 推送操作
     * 创建原材料扣减(调减单据)，状态变更为已完成
     * 
     * @param deductionId 扣减单ID
     * @return 结果
     */
    int pushOperation(Long deductionId) throws Exception;

    /**
     * 关闭操作
     * 状态变更为已关闭
     * 
     * @param deductionId 扣减单ID
     * @param closeReason 关闭原因
     * @return 结果
     */
    int closeOperation(Long deductionId, String closeReason) throws Exception;

    /**
     * 工单完成后状态变更
     * 统计中状态变更为待领料
     * 
     * @param workorderId 工单ID
     * @return 结果
     */
    int updateStatusAfterWorkorderComplete(Long workorderId);
}
