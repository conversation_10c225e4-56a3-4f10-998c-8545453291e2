package com.ktg.mes.pro.service.impl;

import com.ktg.common.core.domain.entity.SysDictData;
import com.ktg.common.enums.MesDictEnums;
import com.ktg.common.enums.OrderStatusEnum;
import com.ktg.common.utils.ConvertUtil;
import com.ktg.common.utils.DateUtils;
import com.ktg.common.utils.SecurityUtils;
import com.ktg.common.utils.StringUtils;
import com.ktg.mes.pro.domain.ProTask;
import com.ktg.mes.pro.domain.ProTaskException;
import com.ktg.mes.pro.domain.ProWorkorderBom;
import com.ktg.mes.pro.domain.req.ProTaskExceptionAddReqVo;
import com.ktg.mes.pro.domain.req.ProTaskExceptionBatchReqVo;
import com.ktg.mes.pro.domain.req.ProTaskExceptionItemReqVo;
import com.ktg.mes.pro.domain.res.ProTaskExceptionResVo;
import com.ktg.mes.pro.mapper.ProTaskExceptionMapper;
import com.ktg.mes.pro.service.IProTaskExceptionService;
import com.ktg.mes.pro.service.IProTaskService;
import com.ktg.mes.pro.service.IProWorkorderBomService;
import com.ktg.system.service.ISysDictDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 生产异常标记Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
@Slf4j
@Service
public class ProTaskExceptionServiceImpl implements IProTaskExceptionService 
{
    @Autowired
    private ProTaskExceptionMapper proTaskExceptionMapper;

    @Autowired
    private IProTaskService proTaskService;

    @Autowired
    private IProWorkorderBomService proWorkorderBomService;

    @Autowired
    private ISysDictDataService sysDictDataService;

    /**
     * 查询生产异常标记
     * 
     * @param exceptionId 生产异常标记主键
     * @return 生产异常标记
     */
    @Override
    public ProTaskException selectProTaskExceptionByExceptionId(Long exceptionId)
    {
        return proTaskExceptionMapper.selectProTaskExceptionByExceptionId(exceptionId);
    }

    /**
     * 查询生产异常标记列表
     * 
     * @param proTaskException 生产异常标记
     * @return 生产异常标记
     */
    @Override
    public List<ProTaskException> selectProTaskExceptionList(ProTaskException proTaskException)
    {
        return proTaskExceptionMapper.selectProTaskExceptionList(proTaskException);
    }

    /**
     * 根据任务ID查询生产异常标记列表
     * 
     * @param taskId 生产任务ID
     * @return 生产异常标记集合
     */
    @Override
    public List<ProTaskExceptionResVo> selectProTaskExceptionByTaskId(Long taskId)
    {
        List<ProTaskException> list = proTaskExceptionMapper.selectProTaskExceptionByTaskId(taskId);
        return convertToResVoList(list);
    }

    /**
     * 根据任务ID列表查询生产异常标记列表
     * 
     * @param taskIdList 生产任务ID列表
     * @return 生产异常标记集合
     */
    @Override
    public List<ProTaskExceptionResVo> selectProTaskExceptionByTaskIdList(List<Long> taskIdList)
    {
        if (taskIdList == null || taskIdList.isEmpty()) {
            return new ArrayList<>();
        }
        List<ProTaskException> list = proTaskExceptionMapper.selectProTaskExceptionByTaskIdList(taskIdList);
        return convertToResVoList(list);
    }

    /**
     * 根据工单ID查询生产异常标记列表
     * 
     * @param workorderId 生产工单ID
     * @return 生产异常标记集合
     */
    @Override
    public List<ProTaskExceptionResVo> selectProTaskExceptionByWorkorderId(Long workorderId)
    {
        List<ProTaskException> list = proTaskExceptionMapper.selectProTaskExceptionByWorkorderId(workorderId);
        return convertToResVoList(list);
    }

    /**
     * 查询异常原因合并统计信息
     * 
     * @param taskId 生产任务ID
     * @return 合并统计结果
     */
    @Override
    public List<ProTaskExceptionResVo> selectExceptionSummaryByTaskId(Long taskId)
    {
        List<ProTaskException> list = proTaskExceptionMapper.selectExceptionSummaryByTaskId(taskId);
        return convertToResVoList(list);
    }

    /**
     * 新增生产异常标记
     * 
     * @param reqVo 生产异常标记请求对象
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertProTaskException(ProTaskExceptionAddReqVo reqVo)
    {
        // 校验异常数量
        if (reqVo.getExceptionQuantity() == null || reqVo.getExceptionQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new RuntimeException("异常数量必须大于0");
        }

        // 获取生产任务信息
        ProTask proTask = proTaskService.selectProTaskByTaskId(reqVo.getTaskId());
        if (proTask == null) {
            throw new RuntimeException("生产任务不存在");
        }

        // 校验生产任务状态（使用重载方法，减少一次查询）
        if (!validateTaskStatusForException(proTask)) {
            throw new RuntimeException("生产任务状态不是生产中，无法添加异常标记");
        }

        // 转换为实体对象
        ProTaskException proTaskException = ConvertUtil.beanConvert(reqVo, ProTaskException.class);

        // 设置任务和工单信息
        proTaskException.setTaskCode(proTask.getTaskCode());
        proTaskException.setWorkorderId(proTask.getWorkorderId());
        proTaskException.setWorkorderCode(proTask.getWorkorderCode());

        // 设置异常原因描述
        setExceptionReasonDesc(proTaskException);

        // 根据原材料代码获取原材料名称
        ProWorkorderBom bomQuery = new ProWorkorderBom();
        bomQuery.setWorkorderId(proTask.getWorkorderId());
        bomQuery.setItemCode(reqVo.getItemCode());
        List<ProWorkorderBom> bomList = proWorkorderBomService.selectProWorkorderBomList(bomQuery);
        if (!bomList.isEmpty()) {
            proTaskException.setItemName(bomList.get(0).getItemName());
        }

        // 设置登记信息
        proTaskException.setRegistrant(SecurityUtils.getUsername());
        proTaskException.setRegistrantName(SecurityUtils.getLoginUser().getUser().getNickName());
        proTaskException.setRegisterTime(new Date());
        proTaskException.setStatus("0");
        proTaskException.setCreateBy(SecurityUtils.getUsername());
        proTaskException.setCreateTime(DateUtils.getNowDate());

        return proTaskExceptionMapper.insertProTaskException(proTaskException);
    }

    /**
     * 批量新增或更新生产异常标记
     *
     * @param reqVo 批量操作请求对象
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchSaveOrUpdateProTaskException(ProTaskExceptionBatchReqVo reqVo)
    {
        // 获取生产任务信息
        ProTask proTask = proTaskService.selectProTaskByTaskId(reqVo.getTaskId());
        if (proTask == null) {
            throw new RuntimeException("生产任务不存在");
        }

        // 校验生产任务状态（使用重载方法，减少一次查询）
        if (!validateTaskStatusForException(proTask)) {
            throw new RuntimeException("生产任务状态不是生产中，无法添加异常标记");
        }

        int totalResult = 0;

        for (ProTaskExceptionItemReqVo itemReqVo : reqVo.getExceptionList()) {
            // 校验异常数量
            if (itemReqVo.getExceptionQuantity() == null || itemReqVo.getExceptionQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                throw new RuntimeException("异常数量必须大于0");
            }

            if (itemReqVo.getExceptionId() != null) {
                // 更新操作
                ProTaskException existingException = proTaskExceptionMapper.selectProTaskExceptionByExceptionId(itemReqVo.getExceptionId());
                if (existingException == null) {
                    throw new RuntimeException("异常记录不存在，ID: " + itemReqVo.getExceptionId());
                }

                // 校验异常记录是否属于当前任务
                if (!existingException.getTaskId().equals(reqVo.getTaskId())) {
                    throw new RuntimeException("异常记录不属于当前任务");
                }

                // 更新异常记录
                ProTaskException updateException = ConvertUtil.beanConvert(itemReqVo, ProTaskException.class);
                updateException.setExceptionId(itemReqVo.getExceptionId());
                updateException.setTaskId(reqVo.getTaskId());
                updateException.setTaskCode(proTask.getTaskCode());
                updateException.setWorkorderId(proTask.getWorkorderId());
                updateException.setWorkorderCode(proTask.getWorkorderCode());

                // 设置异常原因描述
                setExceptionReasonDesc(updateException);

                // 根据原材料代码获取原材料名称
                ProWorkorderBom bomQuery = new ProWorkorderBom();
                bomQuery.setWorkorderId(proTask.getWorkorderId());
                bomQuery.setItemCode(itemReqVo.getItemCode());
                List<ProWorkorderBom> bomList = proWorkorderBomService.selectProWorkorderBomList(bomQuery);
                if (!bomList.isEmpty()) {
                    updateException.setItemName(bomList.get(0).getItemName());
                }
                updateException.setRegistrant(SecurityUtils.getUsername());
                updateException.setRegistrantName(SecurityUtils.getLoginUser().getUser().getNickName());
                updateException.setRegisterTime(new Date());
                updateException.setUpdateBy(SecurityUtils.getUsername());
                updateException.setUpdateTime(DateUtils.getNowDate());

                totalResult += proTaskExceptionMapper.updateProTaskException(updateException);
            } else {
                // 新增操作
                ProTaskException proTaskException = ConvertUtil.beanConvert(itemReqVo, ProTaskException.class);

                // 设置任务和工单信息
                proTaskException.setTaskId(reqVo.getTaskId());
                proTaskException.setTaskCode(proTask.getTaskCode());
                proTaskException.setWorkorderId(proTask.getWorkorderId());
                proTaskException.setWorkorderCode(proTask.getWorkorderCode());

                // 设置异常原因描述
                setExceptionReasonDesc(proTaskException);

                // 根据原材料代码获取原材料名称
                ProWorkorderBom bomQuery = new ProWorkorderBom();
                bomQuery.setWorkorderId(proTask.getWorkorderId());
                bomQuery.setItemCode(itemReqVo.getItemCode());
                List<ProWorkorderBom> bomList = proWorkorderBomService.selectProWorkorderBomList(bomQuery);
                if (!bomList.isEmpty()) {
                    proTaskException.setItemName(bomList.get(0).getItemName());
                }

                // 设置登记信息
                proTaskException.setRegistrant(SecurityUtils.getUsername());
                proTaskException.setRegistrantName(SecurityUtils.getLoginUser().getUser().getNickName());
                proTaskException.setRegisterTime(new Date());
                proTaskException.setStatus("0");
                proTaskException.setCreateBy(SecurityUtils.getUsername());
                proTaskException.setCreateTime(DateUtils.getNowDate());

                totalResult += proTaskExceptionMapper.insertProTaskException(proTaskException);
            }
        }

        return totalResult;
    }

    /**
     * 修改生产异常标记
     * 
     * @param proTaskException 生产异常标记
     * @return 结果
     */
    @Override
    public int updateProTaskException(ProTaskException proTaskException)
    {
        proTaskException.setUpdateTime(DateUtils.getNowDate());
        return proTaskExceptionMapper.updateProTaskException(proTaskException);
    }

    /**
     * 批量删除生产异常标记
     * 
     * @param exceptionIds 需要删除的生产异常标记主键
     * @return 结果
     */
    @Override
    public int deleteProTaskExceptionByExceptionIds(Long[] exceptionIds)
    {
        return proTaskExceptionMapper.deleteProTaskExceptionByExceptionIds(exceptionIds);
    }

    /**
     * 删除生产异常标记信息
     * 
     * @param exceptionId 生产异常标记主键
     * @return 结果
     */
    @Override
    public int deleteProTaskExceptionByExceptionId(Long exceptionId)
    {
        return proTaskExceptionMapper.deleteProTaskExceptionByExceptionId(exceptionId);
    }

    /**
     * 校验生产任务状态是否允许添加异常标记
     *
     * @param taskId 生产任务ID
     * @return 校验结果
     */
    @Override
    public boolean validateTaskStatusForException(Long taskId)
    {
        ProTask proTask = proTaskService.selectProTaskByTaskId(taskId);
        return validateTaskStatusForException(proTask);
    }

    /**
     * 校验生产任务状态是否允许添加异常标记
     *
     * @param proTask 生产任务对象
     * @return 校验结果
     */
    @Override
    public boolean validateTaskStatusForException(ProTask proTask)
    {
        if (proTask == null) {
            return false;
        }
        // 只有生产中状态才能添加异常标记
        return OrderStatusEnum.PRODUCING.getCode().equals(proTask.getStatus());
    }

    /**
     * 获取生产任务的BOM原材料列表
     * 
     * @param taskId 生产任务ID
     * @return BOM原材料列表
     */
    @Override
    public List<ProTaskExceptionResVo> getTaskBomMaterials(Long taskId)
    {
        ProTask proTask = proTaskService.selectProTaskByTaskId(taskId);
        if (proTask == null) {
            return new ArrayList<>();
        }

        ProWorkorderBom bomQuery = new ProWorkorderBom();
        bomQuery.setWorkorderId(proTask.getWorkorderId());
        List<ProWorkorderBom> bomList = proWorkorderBomService.selectProWorkorderBomList(bomQuery);

        return bomList.stream().map(bom -> {
            ProTaskExceptionResVo resVo = new ProTaskExceptionResVo();
            resVo.setItemCode(bom.getItemCode());
            resVo.setItemName(bom.getItemName());
            return resVo;
        }).collect(Collectors.toList());
    }

    /**
     * 设置异常原因描述
     *
     * @param proTaskException 异常记录对象
     */
    private void setExceptionReasonDesc(ProTaskException proTaskException)
    {
        if (StringUtils.isNotEmpty(proTaskException.getExceptionReason())) {
            List<SysDictData> dictDataList = sysDictDataService.selectByType(MesDictEnums.PRODUCTION_EXCEPTION_REASON.getDictType());
            dictDataList.stream()
                .filter(dict -> proTaskException.getExceptionReason().equals(dict.getDictValue()))
                .findFirst()
                .ifPresent(dict -> proTaskException.setExceptionReasonDesc(dict.getDictLabel()));
        }
    }

    /**
     * 转换为响应VO列表
     *
     * @param list 实体列表
     * @return 响应VO列表
     */
    private List<ProTaskExceptionResVo> convertToResVoList(List<ProTaskException> list)
    {
        if (list == null || list.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取异常原因字典数据
//        List<SysDictData> dictDataList = sysDictDataService.selectByType(MesDictEnums.PRODUCTION_EXCEPTION_REASON.getDictType());

        return list.stream().map(exception -> {
            ProTaskExceptionResVo resVo = ConvertUtil.beanConvert(exception, ProTaskExceptionResVo.class);

            // 如果数据库中没有异常原因描述，则从字典中获取
//            if (StringUtils.isEmpty(resVo.getExceptionReasonDesc()) && StringUtils.isNotEmpty(exception.getExceptionReason())) {
//                dictDataList.stream()
//                    .filter(dict -> exception.getExceptionReason().equals(dict.getDictValue()))
//                    .findFirst()
//                    .ifPresent(dict -> resVo.setExceptionReasonDesc(dict.getDictLabel()));
//            }
            return resVo;
        }).collect(Collectors.toList());
    }
}
