package com.ktg.mes.pro.domain.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 原材料损耗统计
 * @date 2025/6/7 10:28
 */
@Data
public class ProFeedbackIssueLossDetailResVo implements Serializable {
    @ApiModelProperty(value = "原材料代码")
    private String itemCode;
    @ApiModelProperty(value = "原材料名称")
    private String itemName;
    @ApiModelProperty(value = "损耗原因")
    private String lossReason;
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "贸易类型")
    private String tradeType;
    @ApiModelProperty(value = "生产使用")
    private BigDecimal productionUsage;
    @ApiModelProperty(value = "期初数量")
    private BigDecimal beginQuantity;
    @ApiModelProperty(value = "结余数量")
    private BigDecimal endQuantity;
    @ApiModelProperty(value = "实际损耗数量")
    private BigDecimal quantity;
}
