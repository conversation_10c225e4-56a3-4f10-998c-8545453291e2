package com.ktg.mes.pro.service.impl;

import com.alibaba.fastjson.JSON;
import com.dt.component.common.enums.bill.AdjustBusinessTypeEnum;
import com.dt.component.common.enums.bill.AdjustDetailReasonEnum;
import com.dt.component.common.enums.bill.AdjustReasonEnum;
import com.dt.component.common.enums.bill.AdjustTypeEnum;
import com.dt.component.common.enums.sku.InventoryTypeEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.platform.wms.rpc.client.mes.adjust.AdjustAddRequest;
import com.dt.platform.wms.rpc.client.mes.adjust.AdjustAddResponse;
import com.dt.platform.wms.rpc.client.mes.adjust.AdjustDetail;
import com.dt.platform.wms.rpc.client.mes.adjust.IAdjustMesClient;
import com.dt.platform.wms.rpc.client.mes.common.Result;
import com.ktg.common.constant.UserConstants;
import com.ktg.common.enums.FeedbackTypeEnum;
import com.ktg.common.enums.OrderStatusEnum;
import com.ktg.common.utils.DateUtils;
import com.ktg.mes.md.domain.MdWorkshop;
import com.ktg.mes.md.service.IMdWorkshopService;
import com.ktg.mes.pro.domain.ProFeedback;
import com.ktg.mes.pro.domain.ProTask;
import com.ktg.mes.pro.domain.ProWorkorder;
import com.ktg.mes.pro.domain.req.ProFeedbackApproveReqVo;
import com.ktg.mes.pro.domain.req.ProFeedbackListReqVo;
import com.ktg.mes.pro.mapper.ProFeedbackMapper;
import com.ktg.mes.pro.service.IProFeedbackService;
import com.ktg.mes.pro.service.IProTaskService;
import com.ktg.mes.pro.service.IProWorkorderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 生产报工记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-07-10
 */
@Slf4j
@Service
public class ProFeedbackServiceImpl implements IProFeedbackService {
    @Autowired
    private ProFeedbackMapper proFeedbackMapper;
    @Autowired
    private IProTaskService proTaskService;
    @DubboReference
    private IAdjustMesClient adjustMesClient;
    @Autowired
    private IProWorkorderService proWorkorderService;
    @Autowired
    private IMdWorkshopService mdWorkshopService;

    /**
     * 查询生产报工记录
     *
     * @param recordId 生产报工记录主键
     * @return 生产报工记录
     */
    @Override
    public ProFeedback selectProFeedbackByRecordId(Long recordId) {
        return proFeedbackMapper.selectProFeedbackByRecordId(recordId);
    }

    /**
     * 查询生产报工记录列表
     *
     * @param proFeedback 生产报工记录
     * @return 生产报工记录
     */
    @Override
    public List<ProFeedback> selectProFeedbackList(ProFeedback proFeedback) {
        return proFeedbackMapper.selectProFeedbackList(proFeedback);
    }

    @Override
    public List<ProFeedback> proFeedbackPage(ProFeedbackListReqVo reqVo) {
        return proFeedbackMapper.proFeedbackPage(reqVo);
    }

    /**
     * 新增生产报工记录
     *
     * @param proFeedback 生产报工记录
     * @return 结果
     */
    @Override
    public int insertProFeedback(ProFeedback proFeedback) {
        proFeedback.setCreateTime(DateUtils.getNowDate());
        return proFeedbackMapper.insertProFeedback(proFeedback);
    }

    /**
     * 修改生产报工记录
     *
     * @param proFeedback 生产报工记录
     * @return 结果
     */
    @Override
    public int updateProFeedback(ProFeedback proFeedback) {
        proFeedback.setUpdateTime(DateUtils.getNowDate());
        return proFeedbackMapper.updateProFeedback(proFeedback);
    }

    /**
     * 批量删除生产报工记录
     *
     * @param recordIds 需要删除的生产报工记录主键
     * @return 结果
     */
    @Override
    public int deleteProFeedbackByRecordIds(Long[] recordIds) {
        return proFeedbackMapper.deleteProFeedbackByRecordIds(recordIds);
    }

    /**
     * 删除生产报工记录信息
     *
     * @param recordId 生产报工记录主键
     * @return 结果
     */
    @Override
    public int deleteProFeedbackByRecordId(Long recordId) {
        return proFeedbackMapper.deleteProFeedbackByRecordId(recordId);
    }

    @Override
    public void approveProFeedback(ProFeedbackApproveReqVo proFeedbackApproveReqVo) throws Exception {
        if (Objects.isNull(proFeedbackApproveReqVo) || Objects.isNull(proFeedbackApproveReqVo.getRecordId())) {
            throw new Exception("报工记录ID不能为空");
        }
        if (Objects.isNull(proFeedbackApproveReqVo.getApproveOpinion())) {
            throw new Exception("审批意见不能为空");
        }
        Long recordId = proFeedbackApproveReqVo.getRecordId();
        ProFeedback proFeedback = proFeedbackMapper.selectProFeedbackByRecordId(recordId);
        if (Objects.isNull(proFeedback)) {
            throw new Exception("报工记录不存在");
        }
        if (!Objects.equals(proFeedback.getStatus(), UserConstants.ORDER_STATUS_APPROVING)) {
            throw new Exception("报工记录状态不正确");
        }

        Long taskId = proFeedback.getTaskId();
        ProTask proTask = proTaskService.selectProTaskByTaskId(taskId);
        if (Objects.isNull(proTask)) {
            throw new Exception("生产任务不存在");
        }
        if (Objects.equals(proTask.getStatus(), OrderStatusEnum.FINISHED.getCode())) {
            throw new Exception("生产任务已完成");
        }

        Long workorderId = proFeedback.getWorkorderId();
        ProWorkorder proWorkorder = proWorkorderService.selectProWorkorderByWorkorderId(workorderId);
        if (Objects.isNull(proWorkorder)) {
            throw new Exception("工单不存在");
        }
        if (Objects.equals(proWorkorder.getStatus(), OrderStatusEnum.FINISHED.getCode())) {
            throw new Exception("工单已完成无法报工");
        }

        BigDecimal historyFeedbackQuantity = BigDecimal.ZERO;
        List<ProFeedback> feedbackList = this.selectProFeedbackByTaskId(proFeedback.getTaskId());
        if (!CollectionUtils.isEmpty(feedbackList)) {
            historyFeedbackQuantity = feedbackList.stream()
                    .filter(f -> Objects.equals(f.getStatus(), OrderStatusEnum.FINISHED.getCode()))
                    .map(ProFeedback::getQuantityFeedback)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        log.info("历史报工数量:{} 此次报工数量:{} 任务需求数量:{}", historyFeedbackQuantity, proFeedback.getQuantityFeedback(), proTask.getQuantity());
        if (!FeedbackTypeEnum.EXTRA.getCode().equals(proFeedback.getFeedbackType())) {
            if (proFeedback.getQuantityFeedback().add(historyFeedbackQuantity).compareTo(proTask.getQuantity()) > 0) {
                throw new Exception("本次报工数量:" + proFeedback.getQuantityFeedback() + "加历史报工数量:" + historyFeedbackQuantity + "大于排产数量:" + proTask.getQuantity() + "，请调整！");
            }
        }

        if (Objects.equals(proFeedbackApproveReqVo.getApproveOpinion(), Boolean.TRUE)) {
//            if (Objects.equals(proFeedback.getFeedbackType(), FeedbackTypeEnum.EXTRA.getCode())) {
//                proFeedback.setStatus(UserConstants.ORDER_STATUS_FINISHED);
//                proFeedback.setUpdateBy(proFeedbackApproveReqVo.getOperator());
//                proFeedback.setUpdateTime(new Date());
//                proFeedbackMapper.updateProFeedback(proFeedback);
//            } else {
                try {
                    Long workShopId = proWorkorder.getWorkshopId();
                    MdWorkshop mdWorkshop = mdWorkshopService.selectMdWorkshopByWorkshopId(workShopId);
                    //调用wms接口
                    AdjustAddRequest adjustAddRequest = new AdjustAddRequest();
                    adjustAddRequest.setWarehouseCode(proWorkorder.getWarehouseCode());
                    adjustAddRequest.setCargoCode(proWorkorder.getOwnerCode());
                    adjustAddRequest.setReason(AdjustReasonEnum.MES.getCode());
                    adjustAddRequest.setType(AdjustTypeEnum.ADD.getStatus());
                    adjustAddRequest.setBillNo(proFeedback.getFeedbackCode());
                    adjustAddRequest.setNote(proTask.getTaskCode());
                    adjustAddRequest.setBusinessType(AdjustBusinessTypeEnum.MES_PRODUCT_ADD.getCode());
                    List<AdjustDetail> detailList = new ArrayList<>();
                    if (Objects.nonNull(proFeedback.getQuantityUncheck()) && proFeedback.getQuantityUncheck().compareTo(BigDecimal.ZERO) > 0) {
                        AdjustDetail adjustDetail = new AdjustDetail();
                        String itemCode = proFeedback.getItemCode();
                        adjustDetail.setSkuCode(itemCode);
                        adjustDetail.setLocationCode(mdWorkshop.getAvlLineSideLocationCode());
                        adjustDetail.setAdjustQty(proFeedback.getQuantityUncheck());
                        adjustDetail.setProductionNo(proTask.getProduceBatchCode());
                        adjustDetail.setInventoryType(InventoryTypeEnum.ZP.getCode());
                        adjustDetail.setSkuQuality(SkuQualityEnum.SKU_QUALITY_AVL.getLevel());
                        adjustDetail.setManufDate(proWorkorder.getProductionDate().getTime());
//                    adjustDetail.setExternalSkuLotNo(proWorkorder.getWorkorderCode());
                        adjustDetail.setExternalLinkBillNo(proTask.getTaskCode());
                        adjustDetail.setReceiveDate(proFeedback.getCreateTime().getTime());
                        adjustDetail.setReason(AdjustDetailReasonEnum.MES_ADD.getCode());
                        detailList.add(adjustDetail);
                    }
                    if (Objects.nonNull(proFeedback.getQuantityUnquanlified()) && proFeedback.getQuantityUnquanlified().compareTo(BigDecimal.ZERO) > 0) {
                        AdjustDetail adjustDetail = new AdjustDetail();
                        String itemCode = proFeedback.getItemCode();
                        adjustDetail.setSkuCode(itemCode);
                        adjustDetail.setLocationCode(mdWorkshop.getDamageLineSideLocationCode());
                        adjustDetail.setAdjustQty(proFeedback.getQuantityUnquanlified());
                        adjustDetail.setProductionNo(proTask.getProduceBatchCode());
                        adjustDetail.setInventoryType(InventoryTypeEnum.CC.getCode());
                        adjustDetail.setSkuQuality(SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel());
                        adjustDetail.setManufDate(proWorkorder.getProductionDate().getTime());
//                    adjustDetail.setExternalSkuLotNo(proWorkorder.getWorkorderCode());
                        adjustDetail.setExternalLinkBillNo(proTask.getTaskCode());
                        adjustDetail.setReceiveDate(proFeedback.getCreateTime().getTime());
                        adjustDetail.setReason(AdjustDetailReasonEnum.MES_ADD.getCode());
                        detailList.add(adjustDetail);
                    }
                    if (Objects.nonNull(proFeedback.getLeaveSampleQuantity()) && proFeedback.getLeaveSampleQuantity().compareTo(BigDecimal.ZERO) > 0) {
                        AdjustDetail adjustDetail = new AdjustDetail();
                        String itemCode = proFeedback.getItemCode();
                        adjustDetail.setSkuCode(itemCode);
                        adjustDetail.setLocationCode(mdWorkshop.getDamageLineSideLocationCode());
                        adjustDetail.setAdjustQty(proFeedback.getLeaveSampleQuantity());
                        adjustDetail.setProductionNo(proTask.getProduceBatchCode());
                        adjustDetail.setInventoryType(InventoryTypeEnum.BLC.getCode());
                        adjustDetail.setSkuQuality(SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel());
                        adjustDetail.setManufDate(proWorkorder.getProductionDate().getTime());
//                    adjustDetail.setExternalSkuLotNo(proWorkorder.getWorkorderCode());
                        adjustDetail.setExternalLinkBillNo(proTask.getTaskCode());
                        adjustDetail.setReceiveDate(proFeedback.getCreateTime().getTime());
                        adjustDetail.setReason(AdjustDetailReasonEnum.MES_ADD.getCode());
                        adjustDetail.setRemark("留样品");
                        detailList.add(adjustDetail);
                    }
                    if (Objects.nonNull(proFeedback.getInspectionQuantity()) && proFeedback.getInspectionQuantity().compareTo(BigDecimal.ZERO) > 0) {
                        AdjustDetail adjustDetail = new AdjustDetail();
                        String itemCode = proFeedback.getItemCode();
                        adjustDetail.setSkuCode(itemCode);
                        adjustDetail.setLocationCode(mdWorkshop.getDamageLineSideLocationCode());
                        adjustDetail.setAdjustQty(proFeedback.getInspectionQuantity());
                        adjustDetail.setProductionNo(proTask.getProduceBatchCode());
                        adjustDetail.setInventoryType(InventoryTypeEnum.BLC.getCode());
                        adjustDetail.setSkuQuality(SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel());
                        adjustDetail.setManufDate(proWorkorder.getProductionDate().getTime());
//                    adjustDetail.setExternalSkuLotNo(proWorkorder.getWorkorderCode());
                        adjustDetail.setExternalLinkBillNo(proTask.getTaskCode());
                        adjustDetail.setReceiveDate(proFeedback.getCreateTime().getTime());
                        adjustDetail.setReason(AdjustDetailReasonEnum.MES_ADD.getCode());
                        adjustDetail.setRemark("送检品");
                        detailList.add(adjustDetail);
                    }
                    adjustAddRequest.setDetailList(detailList);
                    log.info("调用wms接口请求 request={}", JSON.toJSONString(adjustAddRequest));
                    Result<AdjustAddResponse> result = adjustMesClient.add(adjustAddRequest);
                    log.info("调用wms接口返回 result={}", JSON.toJSONString(result));
                    if (result.checkSuccess()) {
                        AdjustAddResponse data = result.getData();
                        List<AdjustAddResponse.Detail> resultList = data.getDetailList();
                        proFeedback.setWmsBatchDetail(JSON.toJSONString(resultList));
                        proFeedback.setStatus(UserConstants.ORDER_STATUS_FINISHED);
                        proFeedback.setWmsOrderNo(data.getCode());
                        proFeedback.setUpdateBy(proFeedbackApproveReqVo.getOperator());
                        proFeedback.setUpdateTime(new Date());
                        proFeedbackMapper.updateProFeedback(proFeedback);


                        //更新生产任务的生产数量
                        BigDecimal quantityProduced, quantityQuanlify, quantityUnquanlify;
                        quantityQuanlify = proTask.getQuantityQuanlify() == null ? new BigDecimal(0) : proTask.getQuantityQuanlify();
//                    quantityUnquanlify = proTask.getQuantityUnquanlify() == null ? new BigDecimal(0) : proTask.getQuantityUnquanlify();
                        quantityProduced = proTask.getQuantityProduced() == null ? new BigDecimal(0) : proTask.getQuantityProduced();
                        proTask.setQuantityProduced(quantityProduced.add(proFeedback.getQuantityFeedback()));
                        proTask.setQuantityQuanlify(quantityQuanlify.add(proFeedback.getQuantityUncheck()));
//                    proTask.setQuantityUnquanlify(quantityUnquanlify.add(proFeedback.getQuantityUnquanlified()));

                        proTask.setUpdateBy(proFeedbackApproveReqVo.getOperator());
                        proTask.setUpdateTime(new Date());
                        proTaskService.updateProTask(proTask);
                    } else {
                        throw new Exception(result.getMessage());
                    }
                } catch (Exception e) {
                    log.error("调用wms接口失败,error={}", e.getMessage(), e);
                    throw new Exception("调用wms接口失败," + e.getMessage());
                }
//            }
        } else {
            proFeedback.setStatus(UserConstants.ORDER_STATUS_PREPARE);
            proFeedback.setUpdateBy(proFeedbackApproveReqVo.getOperator());
            proFeedback.setUpdateTime(new Date());
            proFeedbackMapper.updateProFeedback(proFeedback);
        }
    }

    @Override
    public List<ProFeedback> selectProFeedbackByTaskId(Long taskId) {
        if (Objects.isNull(taskId)) {
            return new ArrayList<>();
        }
        ProFeedback proFeedback = new ProFeedback();
        proFeedback.setTaskId(taskId);
        return proFeedbackMapper.selectProFeedbackList(proFeedback);
    }

    @Override
    public List<ProFeedback> selectProFeedbackByTaskId(List<Long> taskIdList) {
        if (CollectionUtils.isEmpty(taskIdList)) {
            return new ArrayList<>();
        }
        return proFeedbackMapper.selectProFeedbackByTaskIdList(taskIdList);
    }

    @Override
    public List<ProFeedback> selectProFeedbackByWorkorderId(Long workorderId) {
        if (Objects.isNull(workorderId)) {
            return new ArrayList<>();
        }
        ProFeedback proFeedback = new ProFeedback();
        proFeedback.setWorkorderId(workorderId);
        return proFeedbackMapper.selectProFeedbackList(proFeedback);
    }

    @Override
    public List<ProFeedback> selectProFeedbackByWorkorderId(List<Long> workorderIdList) {
        return proFeedbackMapper.selectProFeedbackByWorkorderIdList(workorderIdList);
    }

    @Override
    public List<ProFeedback> selectProFeedbackBySkuLotNoList(List<String> skuLotNoList) {
        if (CollectionUtils.isEmpty(skuLotNoList)) {
            return new ArrayList<>();
        }
        return proFeedbackMapper.selectProFeedbackBySkuLotNoList(skuLotNoList);
    }
}
