package com.ktg.mes.pro.domain.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ktg.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/5/13 09:47
 */
@Data
public class ProFeedbackResVo implements Serializable {
    /** 记录ID */
    private Long recordId;

    /** 报工类型（REGULAR-常规报工，EXTRA-额外报工） */
    @Excel(name = "报工类型")
    private String feedbackType;
    private String feedbackTypeDesc;

    /** 报工单编号 */
    @Excel(name = "报工单编号")
    private String feedbackCode;

    /** 工作站ID */
    @Excel(name = "工作站ID")
    private Long workstationId;

    /** 工作站编号 */
    @Excel(name = "工作站编号")
    private String workstationCode;

    /** 工作站名称 */
    @Excel(name = "工作站名称")
    private String workstationName;

    /** 生产工单ID */
    @Excel(name = "生产工单ID")
    private Long workorderId;

    /** 生产工单编号 */
    @Excel(name = "生产工单编号")
    private String workorderCode;

    /** 生产工单名称 */
    @Excel(name = "生产工单名称")
    private String workorderName;

    @Excel(name = "生产类型")
    private Integer linkType;

    /** 仓库编码 */
    @Excel(name = "仓库编码")
    private String warehouseCode;

    /** 仓库名称 */
    @Excel(name = "仓库名称")
    private String warehouseName;

    /** 货主编码 */
    @Excel(name = "货主编码")
    private String ownerCode;

    /** 货主名称 */
    @Excel(name = "货主名称")
    private String ownerName;

    /** 工艺流程ID */
    @Excel(name = "工艺流程ID")
    private Long routeId;

    /** 工艺流程编号 */
    @Excel(name = "工艺流程编号")
    private String routeCode;

    /** 工序ID */
    @Excel(name = "工序ID")
    private Long processId;

    /** 工序编码 */
    @Excel(name = "工序编码")
    private String processCode;

    /** 工序名称 */
    @Excel(name = "工序名称")
    private String processName;

    /** 生产任务ID */
    @Excel(name = "生产任务ID")
    private Long taskId;

    /** 生产任务编号 */
    @Excel(name = "生产任务编号")
    private String taskCode;

    /** 产品物料ID */
    @Excel(name = "产品物料ID")
    private Long itemId;

    /** 产品物料编码 */
    @Excel(name = "产品物料编码")
    private String itemCode;

    /** 产品物料名称 */
    @Excel(name = "产品物料名称")
    private String itemName;

    /** 单位 */
    @Excel(name = "单位")
    private String unitOfMeasure;

    /** 单位描述 */
    private String unitOfMeasureDesc;

    /** 规格型号 */
    @Excel(name = "规格型号")
    private String specification;

    /** 排产数量 */
    @Excel(name = "排产数量")
    private BigDecimal quantity;

    /** 本次报工数量 */
    @Excel(name = "本次报工数量")
    private BigDecimal quantityFeedback;

    /** 合格品数量 */
    @Excel(name = "合格品数量")
    private BigDecimal quantityQualified;

    /** 不良品数量 */
    @Excel(name = "不良品数量")
    private BigDecimal quantityUnquanlified;

    /** 待检测数量 */
    @Excel(name = "待检测数量")
    private BigDecimal quantityUncheck;

    /** 损耗量 */
    @Excel(name = "损耗量")
    private BigDecimal lossQuantity;

    /** 留样&送检数量 */
    @Excel(name = "留样&送检数量")
    private BigDecimal leaveSampleQuantity;

    /** 送检数量 */
    @Excel(name = "送检数量")
    private BigDecimal inspectionQuantity;

    /** 报工用户名 */
    @Excel(name = "报工用户名")
    private String userName;

    /** 昵称 */
    @Excel(name = "昵称")
    private String nickName;

    /** 报工途径 */
    @Excel(name = "报工途径")
    private String feedbackChannel;

    /** 报工时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "报工时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date feedbackTime;

    /** 记录人 */
    @Excel(name = "记录人")
    private String recordUser;

    /** 记录人名称 */
    @Excel(name = "记录人名称")
    private String recordNick;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** wms单据编码 */
    @Excel(name = "wms单据编码")
    private String wmsOrderNo;

    /**
     * 备注
     */
    private String remark;

    private List<ProFeedbackIssueLossDetailResVo> issueLossDetailResVoList;
}
