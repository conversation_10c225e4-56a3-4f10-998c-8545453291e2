package com.ktg.mes.pro.mapper;

import com.ktg.mes.pro.domain.ProWorkorder;
import com.ktg.mes.pro.domain.req.ProWorkorderErpPageReqVo;
import com.ktg.mes.pro.domain.req.ProWorkorderListReqVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 生产工单Mapper接口
 *
 * <AUTHOR>
 * @date 2022-05-09
 */
public interface ProWorkorderMapper {
    /**
     * 查询生产工单
     *
     * @param workorderId 生产工单主键
     * @return 生产工单
     */
    public ProWorkorder selectProWorkorderByWorkorderId(Long workorderId);


    ProWorkorder selectProWorkorderByWorkorderCode(@Param("workorderCode") String workorderCode);

    /**
     * 查询生产工单
     *
     * @param workorderIdList 生产工单主键
     * @return 生产工单
     */
    public List<ProWorkorder> selectProWorkorderByWorkorderIds(@Param("workorderIdList") List<Long> workorderIdList);

    /**
     * 查询生产工单
     *
     * @param workorderId 生产工单主键
     * @return 生产工单
     */
    public List<ProWorkorder> selectProWorkorderListByParentId(Long workorderId);

    /**
     * 查询生产工单列表
     *
     * @param proWorkorder 生产工单
     * @return 生产工单集合
     */
    public List<ProWorkorder> selectProWorkorderList(ProWorkorder proWorkorder);

    List<ProWorkorder> proWorkorderPage(ProWorkorderListReqVo reqVo);

    ProWorkorder checkWorkorderCodeUnique(ProWorkorder proWorkorder);

    /**
     * 新增生产工单
     *
     * @param proWorkorder 生产工单
     * @return 结果
     */
    public int insertProWorkorder(ProWorkorder proWorkorder);

    /**
     * 修改生产工单
     *
     * @param proWorkorder 生产工单
     * @return 结果
     */
    public int updateProWorkorder(ProWorkorder proWorkorder);

    /**
     * 删除生产工单
     *
     * @param workorderId 生产工单主键
     * @return 结果
     */
    public int deleteProWorkorderByWorkorderId(Long workorderId);

    /**
     * 批量删除生产工单
     *
     * @param workorderIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProWorkorderByWorkorderIds(Long[] workorderIds);


    List<ProWorkorder> proWorkorderErpPage(ProWorkorderErpPageReqVo reqVo);

    /**
     * 根据成品SKU列表查询生产工单
     * @param productSkuList 成品SKU列表
     * @return 生产工单列表
     */
    List<ProWorkorder> selectByProductSkuList(@Param("productSkuList") List<String> productSkuList);

    /**
     * 根据条件查询生产工单
     *
     * @param proWorkorderCodeList       工单编码列表
     * @param proWorkorderStatus         工单状态
     * @param finishDateBegin            完成日期开始时间（时间戳）
     * @param finishDateEnd              完成日期结束时间（时间戳）
     * @param productSkuList
     * @param productSkuLotWorkorderList
     * @return 生产工单列表
     */
    List<ProWorkorder> selectByCondition(@Param("proWorkorderCodeList") List<String> proWorkorderCodeList,
                                         @Param("proWorkorderStatus") String proWorkorderStatus,
                                         @Param("finishDateBegin") Long finishDateBegin,
                                         @Param("finishDateEnd") Long finishDateEnd,
                                         @Param("productSkuList") List<String> productSkuList,
                                         @Param("productSkuLotWorkorderList") List<Long> productSkuLotWorkorderList);

}
