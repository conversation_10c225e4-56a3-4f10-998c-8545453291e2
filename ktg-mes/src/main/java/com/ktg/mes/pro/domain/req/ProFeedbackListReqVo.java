package com.ktg.mes.pro.domain.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 生产报工分页查询请求参数
 * @date 2025/5/8 18:54
 */
@Data
public class ProFeedbackListReqVo implements Serializable {
    @ApiModelProperty("报工单号")
    private String feedbackCode;
    @ApiModelProperty("生产工单编码")
    private String workorderCode;
    @ApiModelProperty("生产任务编号")
    private String taskCode;
    @ApiModelProperty("工作站")
    private Long workstationId;
    @ApiModelProperty("状态")
    private String status;
    @ApiModelProperty(value = "报工类型", notes = "REGULAR-常规报工，EXTRA-额外报工")
    private String feedbackType;
    @ApiModelProperty("报工时间开始")
    private Date feedbackTimeBegin;
    @ApiModelProperty("报工时间结束")
    private Date feedbackTimeEnd;
    @ApiModelProperty("产品商品编码")
    private String itemCode;
    @ApiModelProperty("WMS单据编号")
    private String wmsOrderNo;
}
