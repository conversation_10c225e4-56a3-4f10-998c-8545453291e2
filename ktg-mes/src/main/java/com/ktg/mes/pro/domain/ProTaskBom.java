package com.ktg.mes.pro.domain;

import com.ktg.common.annotation.Excel;
import com.ktg.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 生产任务BOM组成对象 pro_task_bom
 * 
 * <AUTHOR>
 * @date 2022-05-09
 */
@Data
public class ProTaskBom extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /** BOM行ID */
    private Long lineId;

    /** 生产工单ID */
    @Excel(name = "生产工单ID")
    private Long workorderId;

    /** 生产任务ID */
    @Excel(name = "生产任务ID")
    private Long taskId;

    /** BOM物料编号 */
    @Excel(name = "BOM物料编号")
    private String itemCode;

    /** BOM物料名称 */
    @Excel(name = "BOM物料名称")
    private String itemName;

    /** 总领料 */
    @Excel(name = "总领料")
    private BigDecimal itemTotalIssue;

    /** 总退料 */
    @Excel(name = "总退料")
    private BigDecimal itemTotalRt;

    /** 系统应还料 */
    @Excel(name = "系统应还料")
    private BigDecimal systemRequiredMaterialReturn;

    /** 线边库剩余 */
    @Excel(name = "线边库剩余")
    private BigDecimal lineSideWarehouseLeft;

    /** 实际使用量 */
    @Excel(name = "实际使用量")
    private BigDecimal actualUsage;

    /** 理论使用量 */
    @Excel(name = "理论使用量")
    private BigDecimal theoreticalUsage;

    /** 生产损耗 */
    @Excel(name = "生产损耗")
    private BigDecimal productionLoss;

    /** 损耗量 */
    @Excel(name = "损耗量")
    private BigDecimal lossQuantity;

    /** 损耗百分比 */
    @Excel(name = "损耗百分比")
    private BigDecimal lossPercentage;

    /** 多到货/少到货 */
    @Excel(name = "多到货/少到货")
    private BigDecimal excessShortQuantity;

    /** 系统损耗 */
    @Excel(name = "系统损耗")
    private BigDecimal systemDisplayLoss;

    /** 库存调整明细 */
    @Excel(name = "库存调整明细")
    private String adjustDetailList;

    /** 损耗明细 */
    @Excel(name = "损耗明细")
    private String lossDetail;

    /** 损耗批次id信息 */
    @Excel(name = "损耗批次id信息")
    private String lossSkuLotNo;
}
