package com.ktg.mes.pro.domain.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/4/23 17:12
 */
@Data
public class ProWorkorderBomViewResVo {
    @ApiModelProperty("商品编码")
    private String itemCode;
    @ApiModelProperty("商品名称")
    private String itemName;
    //所在仓库
    @ApiModelProperty("所在仓库")
    private String warehouseName;
    //计划数量
    @ApiModelProperty("计划数量")
    private BigDecimal planQty;
    //可用库存量
    @ApiModelProperty("可用库存量")
    private BigDecimal availableQty;
}
