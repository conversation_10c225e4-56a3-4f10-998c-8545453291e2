package com.ktg.mes.pro.domain.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/5/13 09:48
 */
@Data
public class ProFeedbackWmsBatchDetailResVo implements Serializable {
    @ApiModelProperty(value = "商品编码")
    private String skuCode;
    @ApiModelProperty(value = "批次ID")
    private String skuLotNo;
    /**
     * {@link com.dt.component.common.enums.sku.SkuQualityEnum}
     */
    @ApiModelProperty(value = "商品属性【质量等级】")
    private String skuQuality;
    private String skuQualityDesc;
    /**
     * {@link com.dt.component.common.enums.sku.InventoryTypeEnum}
     */
    @ApiModelProperty(value = "残次等级")
    private String inventoryType;
    private String inventoryTypeDesc;
    @ApiModelProperty(value = "调整数量")
    private BigDecimal adjustQty;
    @ApiModelProperty(value = "生产批次号")
    private String productionNo;
}
