package com.ktg.mes.pro.domain.res;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/4/29 17:09
 */
@Data
public class ProWorkorderOverViewBomResVo implements Serializable {
    /**
     * 序号
     */
    private Integer number;

    /**
     * BOM物料编号
     */
    private String itemCode;

    /**
     * BOM物料名称
     */
    private String itemName;

    /** 单位 */
    private String unitOfMeasure;
    private String unitOfMeasureDesc;

    /**
     * 实际使用量 AKA 生产使用物料
     */
    private BigDecimal actualUsage;

    /**
     * 理论使用量
     */
    @Deprecated
    private BigDecimal theoreticalUsage;


    /**
     * 生产损耗
     */
    private BigDecimal productionLoss;


    /**
     * 损耗量 AKA 异常物料
     */
    private BigDecimal lossQuantity;

    /**
     * 损耗百分比 lossQuantity/theoreticalUsage*100
     * 损耗量/理论使用量，百分比展示，保留两位小数
     */
    private BigDecimal lossPercentage;

    /**
     * 损耗明细
     */
    private String lossDetail;
}
