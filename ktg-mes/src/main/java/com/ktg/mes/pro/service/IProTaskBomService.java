package com.ktg.mes.pro.service;

import com.ktg.mes.pro.domain.ProTaskBom;

import java.util.List;

/**
 * 生产任务BOM组成Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-30
 */
public interface IProTaskBomService 
{
    /**
     * 查询生产任务BOM组成
     * 
     * @param lineId 生产任务BOM组成主键
     * @return 生产任务BOM组成
     */
    public ProTaskBom selectProTaskBomByLineId(Long lineId);

    /**
     * 查询生产任务BOM组成列表
     * 
     * @param proTaskBom 生产任务BOM组成
     * @return 生产任务BOM组成集合
     */
    public List<ProTaskBom> selectProTaskBomList(ProTaskBom proTaskBom);
    List<ProTaskBom> selectProTaskBomListByWorkorderId(Long  workorderId);

    /**
     * 新增生产任务BOM组成
     * 
     * @param proTaskBom 生产任务BOM组成
     * @return 结果
     */
    public int insertProTaskBom(ProTaskBom proTaskBom);

    /**
     * 修改生产任务BOM组成
     * 
     * @param proTaskBom 生产任务BOM组成
     * @return 结果
     */
    public int updateProTaskBom(ProTaskBom proTaskBom);

    /**
     * 批量删除生产任务BOM组成
     * 
     * @param lineIds 需要删除的生产任务BOM组成主键集合
     * @return 结果
     */
    public int deleteProTaskBomByLineIds(Long[] lineIds);

    /**
     * 删除生产任务BOM组成信息
     * 
     * @param lineId 生产任务BOM组成主键
     * @return 结果
     */
    public int deleteProTaskBomByLineId(Long lineId);

    void insertProTaskBomList(List<ProTaskBom> proTaskBomList);

    List<ProTaskBom> selectProTaskBomListByTaskId(Long taskId);

    List<ProTaskBom> selectProTaskBomListByWorkorderId(List<Long> workorderIdList);
}
