package com.ktg.mes.pro.domain.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/4/17 17:11
 */
@Data
public class ProTaskListReqVo implements Serializable {
    @ApiModelProperty("工单id")
    private Long workorderId;

    @ApiModelProperty("工单编码")
    private String workorderCode;

    @ApiModelProperty("生产任务编号")
    private String taskCode;

    @ApiModelProperty("产品物料编号")
    private String itemCode;

    @ApiModelProperty("车间id")
    private String workshopId;

    @ApiModelProperty("生产批次号")
    private String produceBatchCode;

    /**
     * [生产中] PRODUCING
     * [已完成] FINISHED
     */
    @ApiModelProperty("生产任务状态")
    private String status;

    @ApiModelProperty("WMS单据编号")
    private String wmsOrderNo;

    @ApiModelProperty(name = "工艺流程ID")
    private Long routeId;

    @ApiModelProperty(name = "工序ID")
    private Long processId;
}
