package com.ktg.mes.pro.mapper;

import com.ktg.mes.pro.domain.ProTaskBom;

import java.util.List;

/**
 * 生产任务BOM组成Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-30
 */
public interface ProTaskBomMapper 
{
    /**
     * 查询生产任务BOM组成
     * 
     * @param lineId 生产任务BOM组成主键
     * @return 生产任务BOM组成
     */
    public ProTaskBom selectProTaskBomByLineId(Long lineId);

    /**
     * 查询生产任务BOM组成列表
     * 
     * @param proTaskBom 生产任务BOM组成
     * @return 生产任务BOM组成集合
     */
    public List<ProTaskBom> selectProTaskBomList(ProTaskBom proTaskBom);

    /**
     * 新增生产任务BOM组成
     * 
     * @param proTaskBom 生产任务BOM组成
     * @return 结果
     */
    public int insertProTaskBom(ProTaskBom proTaskBom);

    /**
     * 修改生产任务BOM组成
     * 
     * @param proTaskBom 生产任务BOM组成
     * @return 结果
     */
    public int updateProTaskBom(ProTaskBom proTaskBom);

    /**
     * 删除生产任务BOM组成
     * 
     * @param lineId 生产任务BOM组成主键
     * @return 结果
     */
    public int deleteProTaskBomByLineId(Long lineId);

    /**
     * 批量删除生产任务BOM组成
     * 
     * @param lineIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProTaskBomByLineIds(Long[] lineIds);

    List<ProTaskBom> selectProTaskBomListByWorkorderId(List<Long> workorderIdList);
}
