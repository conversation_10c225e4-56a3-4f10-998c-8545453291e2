package com.ktg.mes.pro.domain.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ktg.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 生产工单列表响应对象
 *
 * <AUTHOR>
 * @description: 生产工单列表ResVo
 * @date 2025/7/17
 */
@Data
public class ProWorkorderListResVo implements Serializable {

    /** 工单ID */
    private Long workorderId;

    /** 工单编码 */
    private String workorderCode;

    /** 工单名称 */
    private String workorderName;

    /** 工单类型 */
    private String workorderType;

    /** 来源类型 */
    private String orderSource;
    private String orderSourceDesc;

    /** 来源单据 */
    private String sourceCode;

    /** 产品ID */
    private Long productId;

    /** 产品编号 */
    private String productCode;

    /** 产品名称 */
    private String productName;

    /** 规格型号 */
    private String productSpc;

    /** 单位 */
    private String unitOfMeasure;

    /** 批次号 */
    private String batchCode;

    /** 生产数量 */
    private BigDecimal quantity;

    /** 已生产数量 */
    private BigDecimal quantityProduced;

    /** 调整数量 */
    private BigDecimal quantityChanged;

    /** 已排产数量 */
    private BigDecimal quantityScheduled;

    /** 客户ID */
    private Long clientId;

    /** 客户编码 */
    private String clientCode;

    /** 客户名称 */
    private String clientName;

    /** 供应商ID */
    private Long vendorId;

    /** 供应商编号 */
    private String vendorCode;

    /** 供应商名称 */
    private String vendorName;

    /** 需求日期 */
    private Date requestDate;

    /** 完成时间 */
    private Date finishDate;

    /** 单据状态 */
    private String status;

    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 货主编码
     */
    private String ownerCode;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 所属车间ID
     */
    private Long workshopId;

    /**
     * 所属车间名称
     */
    private String workshopName;

    /**
     * 出货检验
     * {@link com.ktg.common.constant.UserConstants} YES / NO
     */
    private String oqcCheck;

    /** 预留字段1 */
    private String attr1;

    /** 预留字段2 */
    private String attr2;

    /** 预留字段3 */
    private Long attr3;

    /** 预留字段4 */
    private Long attr4;

    /**
     * 关键原材料要求
     */
    private List<ProWorkOrderKeyMdItemResVo> keyItems;

    private Integer taskCount;

    /**
     * 备注
     */
    private String remark;

    /** 理论产量 */
    private BigDecimal theoreticalOutput;

    /** 入库关联单号 */
    private String externalLinkBillNo;

    /** 生产要求 */
    private String requirement;
    private String requirementDesc;

    /**
     * 数据是否同步ERP 判断业务用
     */
    private String pushStatus;
    /**
     * 数据是否同步ERP 展示
     */
    private String pushStatusDesc;

    /** 生产日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date productionDate;

    /** 失效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expirationDate;

    /** 保质期 */
    @Excel(name = "保质期")
    private Long shelfLife;

    /** 生产批次号 */
    @Excel(name = "生产批次号")
    private String produceBatchCode;

    /**
     * 报关单附件url
     */
    private String declareOrderAttachUrl;

    /**
     * 报关单附件名称
     */
    private String declareOrderAttachName;

    /**
     * 核放单附件url
     */
    private String checklistAttachUrl;

    /**
     * 核放单附件名称
     */
    private String checklistAttachName;

}
