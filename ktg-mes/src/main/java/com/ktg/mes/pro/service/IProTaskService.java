package com.ktg.mes.pro.service;

import com.ktg.mes.pro.domain.ProTask;
import com.ktg.mes.pro.domain.req.ProTaskLeftSubmitReqVo;
import com.ktg.mes.pro.domain.req.ProTaskListReqVo;
import com.ktg.mes.pro.domain.req.TaskListQueryReqVo;
import com.ktg.mes.pro.domain.res.ProTaskCodeIdResVo;
import com.ktg.mes.pro.domain.res.ProTaskLeftViewResVo;

import java.util.List;

/**
 * 生产任务Service接口
 * 
 * <AUTHOR>
 * @date 2022-05-14
 */
public interface IProTaskService 
{
    /**
     * 查询生产任务
     * 
     * @param taskId 生产任务主键
     * @return 生产任务
     */
    public ProTask selectProTaskByTaskId(Long taskId);
    List<ProTask> selectProTaskByTaskId(List<Long> taskIdList);

    /**
     * 查询生产任务列表
     * 
     * @param proTask 生产任务
     * @return 生产任务集合
     */
    List<ProTask> selectProTaskList(ProTask proTask);

    List<ProTask> proTaskPage(ProTaskListReqVo reqVo);

    List<ProTask> selectProTaskListByWorkorderId(Long workorderId);

    List<ProTask> selectProTaskListByWorkorderId(List<Long> workorderId);


    /**
     * 查询某个工单的各个工序生产进度
     * @param workorderId
     * @return
     */
    public List<ProTask> selectProTaskProcessViewByWorkorder(Long workorderId);

    /**
     * 新增生产任务
     * 
     * @param proTask 生产任务
     * @return 结果
     */
    public int insertProTask(ProTask proTask);

    /**
     * 修改生产任务
     * 
     * @param proTask 生产任务
     * @return 结果
     */
    public int updateProTask(ProTask proTask);

    /**
     * 批量删除生产任务
     * 
     * @param taskIds 需要删除的生产任务主键集合
     * @return 结果
     */
    public int deleteProTaskByTaskIds(Long[] taskIds);

    /**
     * 删除生产任务信息
     * 
     * @param taskId 生产任务主键
     * @return 结果
     */
    public int deleteProTaskByTaskId(Long taskId);

    /**
     * 余料清点
     *
     * @param taskId
     * @return
     */
    ProTaskLeftViewResVo leftView(Long taskId);

    /**
     * 处理生产任务完成后续处理
     * @param proTaskLeftSubmitReqVo
     */
    void leftItemSubmitProcess(ProTaskLeftSubmitReqVo proTaskLeftSubmitReqVo) throws Exception;

    List<ProTaskCodeIdResVo> listProTaskByWorkorderId(Long workorderId);

    List<ProTaskCodeIdResVo> listNotFinishProTaskByWorkorderId(Long workorderId);

    List<ProTaskCodeIdResVo> listProTaskByWorkorderIdAndStatus(TaskListQueryReqVo reqVo);
}
