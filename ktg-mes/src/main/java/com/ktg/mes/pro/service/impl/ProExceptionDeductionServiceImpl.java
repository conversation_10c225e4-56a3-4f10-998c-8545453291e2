package com.ktg.mes.pro.service.impl;

import com.ktg.common.constant.UserConstants;
import com.ktg.common.enums.ExceptionDeductionStatusEnum;
import com.ktg.common.utils.ConvertUtil;
import com.ktg.common.utils.DateUtils;
import com.ktg.common.utils.StringUtils;
import com.ktg.common.utils.autocode.AutoCodeUtil;
import com.ktg.mes.pro.domain.ProExceptionDeduction;
import com.ktg.mes.pro.domain.ProExceptionDeductionLine;
import com.ktg.mes.pro.domain.ProWorkorder;
import com.ktg.mes.pro.mapper.ProExceptionDeductionMapper;
import com.ktg.mes.pro.service.IProExceptionDeductionLineService;
import com.ktg.mes.pro.service.IProExceptionDeductionService;
import com.ktg.mes.pro.service.IProWorkorderService;
import com.ktg.mes.wm.domain.WmIssueHeader;
import com.ktg.mes.wm.domain.WmIssueLine;
import com.ktg.mes.wm.service.IWmIssueHeaderService;
import com.ktg.mes.wm.service.IWmIssueLineService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 异常扣减单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@Slf4j
@Service
public class ProExceptionDeductionServiceImpl implements IProExceptionDeductionService {
    
    @Autowired
    private ProExceptionDeductionMapper proExceptionDeductionMapper;
    
    @Autowired
    private IProExceptionDeductionLineService proExceptionDeductionLineService;
    
    @Autowired
    private IProWorkorderService proWorkorderService;
    
    @Autowired
    private IWmIssueHeaderService wmIssueHeaderService;
    
    @Autowired
    private IWmIssueLineService wmIssueLineService;
    
    @Autowired
    private AutoCodeUtil autoCodeUtil;

    @Override
    public ProExceptionDeduction selectProExceptionDeductionByDeductionId(Long deductionId) {
        return proExceptionDeductionMapper.selectProExceptionDeductionByDeductionId(deductionId);
    }

    @Override
    public List<ProExceptionDeduction> selectProExceptionDeductionList(ProExceptionDeduction proExceptionDeduction) {
        return proExceptionDeductionMapper.selectProExceptionDeductionList(proExceptionDeduction);
    }

    @Override
    public ProExceptionDeduction selectByWorkorderIdAndItemCode(Long workorderId, String itemCode) {
        return proExceptionDeductionMapper.selectByWorkorderIdAndItemCode(workorderId, itemCode);
    }

    @Override
    public List<ProExceptionDeduction> selectByWorkorderId(Long workorderId) {
        return proExceptionDeductionMapper.selectByWorkorderId(workorderId);
    }

    @Override
    public List<ProExceptionDeduction> selectByStatus(String status) {
        return proExceptionDeductionMapper.selectByStatus(status);
    }

    @Override
    public int insertProExceptionDeduction(ProExceptionDeduction proExceptionDeduction) {
        proExceptionDeduction.setCreateTime(DateUtils.getNowDate());
        return proExceptionDeductionMapper.insertProExceptionDeduction(proExceptionDeduction);
    }

    @Override
    public int updateProExceptionDeduction(ProExceptionDeduction proExceptionDeduction) {
        proExceptionDeduction.setUpdateTime(DateUtils.getNowDate());
        return proExceptionDeductionMapper.updateProExceptionDeduction(proExceptionDeduction);
    }

    @Override
    public int deleteProExceptionDeductionByDeductionIds(Long[] deductionIds) {
        // 先删除表体数据
        for (Long deductionId : deductionIds) {
            proExceptionDeductionLineService.deleteByDeductionId(deductionId);
        }
        // 再删除表头数据
        return proExceptionDeductionMapper.deleteProExceptionDeductionByDeductionIds(deductionIds);
    }

    @Override
    public int deleteProExceptionDeductionByDeductionId(Long deductionId) {
        // 先删除表体数据
        proExceptionDeductionLineService.deleteByDeductionId(deductionId);
        // 再删除表头数据
        return proExceptionDeductionMapper.deleteProExceptionDeductionByDeductionId(deductionId);
    }

    @Override
    public String checkDeductionCodeUnique(ProExceptionDeduction proExceptionDeduction) {
        Long deductionId = StringUtils.isNull(proExceptionDeduction.getDeductionId()) ? -1L : proExceptionDeduction.getDeductionId();
        ProExceptionDeduction info = proExceptionDeductionMapper.checkDeductionCodeUnique(proExceptionDeduction);
        if (StringUtils.isNotNull(info) && info.getDeductionId().longValue() != deductionId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    @Override
    @Transactional
    public int createExceptionDeductionFromTaskBom(Long taskId, String taskCode, Long workorderId, String workorderCode,
                                                  String itemCode, String itemName, String specification, String unitOfMeasure,
                                                  BigDecimal actualLoss, BigDecimal systemLoss, String missingLotId,
                                                  Long workshopId, String workshopName, String warehouseCode, 
                                                  String warehouseName, String ownerCode, String ownerName) {
        
        BigDecimal missingQuantity = actualLoss.subtract(systemLoss);
        if (missingQuantity.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("实际损耗-系统损耗<=0，跳过创建异常扣减单 - taskId:{} itemCode:{} actualLoss:{} systemLoss:{}", 
                    taskId, itemCode, actualLoss, systemLoss);
            return 0;
        }
        
        // 创建或更新表头
        ProExceptionDeduction header = createOrUpdateHeader(workorderId, workorderCode, itemCode, itemName, 
                specification, unitOfMeasure, workshopId, workshopName, warehouseCode, warehouseName, ownerCode, ownerName);
        
        // 创建表体记录
        ProExceptionDeductionLine line = new ProExceptionDeductionLine();
        line.setDeductionId(header.getDeductionId());
        line.setDeductionCode(header.getDeductionCode());
        line.setWorkorderId(workorderId);
        line.setWorkorderCode(workorderCode);
        line.setTaskId(taskId);
        line.setTaskCode(taskCode);
        line.setItemCode(itemCode);
        line.setItemName(itemName);
        line.setMissingReason("额外生产报工");
        line.setMissingLotId(missingLotId);
        line.setMissingQuantity(missingQuantity);
        line.setUnitOfMeasure(unitOfMeasure);
        line.setTriggerType("TASK_BOM_RECORD");
        line.setTriggerDescription("任务余料清点记录proTaskBom时，实际损耗-系统损耗>0");
        line.setActualLoss(actualLoss);
        line.setSystemLoss(systemLoss);
        
        int result = proExceptionDeductionLineService.insertProExceptionDeductionLine(line);
        
        // 更新表头总数量
        updateHeaderTotalQuantity(header.getDeductionId());
        
        log.info("创建异常扣减单表体记录成功 - taskId:{} itemCode:{} missingQuantity:{}", taskId, itemCode, missingQuantity);
        return result;
    }

    @Override
    @Transactional
    public int createExceptionDeductionFromExtraFeedback(Long taskId, String taskCode, Long workorderId, String workorderCode,
                                                        String itemCode, String itemName, String specification, String unitOfMeasure,
                                                        BigDecimal extraFeedbackQuantity, BigDecimal bondedParticleRatio, String missingLotId,
                                                        Long workshopId, String workshopName, String warehouseCode, 
                                                        String warehouseName, String ownerCode, String ownerName) {
        
        BigDecimal missingQuantity = extraFeedbackQuantity.multiply(bondedParticleRatio);
        if (missingQuantity.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("额外报工数量*保税粒子使用比例<=0，跳过创建异常扣减单 - taskId:{} itemCode:{} extraFeedbackQuantity:{} bondedParticleRatio:{}", 
                    taskId, itemCode, extraFeedbackQuantity, bondedParticleRatio);
            return 0;
        }
        
        // 创建或更新表头
        ProExceptionDeduction header = createOrUpdateHeader(workorderId, workorderCode, itemCode, itemName, 
                specification, unitOfMeasure, workshopId, workshopName, warehouseCode, warehouseName, ownerCode, ownerName);
        
        // 创建表体记录
        ProExceptionDeductionLine line = new ProExceptionDeductionLine();
        line.setDeductionId(header.getDeductionId());
        line.setDeductionCode(header.getDeductionCode());
        line.setWorkorderId(workorderId);
        line.setWorkorderCode(workorderCode);
        line.setTaskId(taskId);
        line.setTaskCode(taskCode);
        line.setItemCode(itemCode);
        line.setItemName(itemName);
        line.setMissingReason("实际损耗");
        line.setMissingLotId(missingLotId);
        line.setMissingQuantity(missingQuantity);
        line.setUnitOfMeasure(unitOfMeasure);
        line.setTriggerType("TASK_LOT_RECORD");
        line.setTriggerDescription("任务余料清点记录任务批次id时，存在额外报工的保税粒子情况");
        line.setExtraFeedbackQuantity(extraFeedbackQuantity);
        line.setBondedParticleRatio(bondedParticleRatio);
        
        int result = proExceptionDeductionLineService.insertProExceptionDeductionLine(line);
        
        // 更新表头总数量
        updateHeaderTotalQuantity(header.getDeductionId());
        
        log.info("创建异常扣减单表体记录成功 - taskId:{} itemCode:{} missingQuantity:{}", taskId, itemCode, missingQuantity);
        return result;
    }
