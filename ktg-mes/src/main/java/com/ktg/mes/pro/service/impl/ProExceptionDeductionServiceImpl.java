package com.ktg.mes.pro.service.impl;

import com.ktg.common.constant.UserConstants;
import com.ktg.common.enums.ExceptionDeductionStatusEnum;
import com.ktg.common.utils.DateUtils;
import com.ktg.common.utils.StringUtils;
import com.ktg.mes.pro.domain.ProExceptionDeduction;
import com.ktg.mes.pro.domain.ProWorkorder;
import com.ktg.mes.pro.mapper.ProExceptionDeductionMapper;
import com.ktg.mes.pro.service.IProExceptionDeductionService;
import com.ktg.mes.pro.service.IProWorkorderService;
import com.ktg.mes.wm.domain.WmIssueHeader;
import com.ktg.mes.wm.domain.WmIssueLine;
import com.ktg.mes.wm.service.IWmIssueHeaderService;
import com.ktg.mes.wm.service.IWmIssueLineService;
import com.ktg.system.strategy.AutoCodeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 异常扣减单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@Slf4j
@Service
public class ProExceptionDeductionServiceImpl implements IProExceptionDeductionService {
    
    @Autowired
    private ProExceptionDeductionMapper proExceptionDeductionMapper;
    
    @Autowired
    private IProWorkorderService proWorkorderService;
    
    @Autowired
    private IWmIssueHeaderService wmIssueHeaderService;
    
    @Autowired
    private IWmIssueLineService wmIssueLineService;
    
    @Autowired
    private AutoCodeUtil autoCodeUtil;

    /**
     * 查询异常扣减单
     * 
     * @param deductionId 异常扣减单主键
     * @return 异常扣减单
     */
    @Override
    public ProExceptionDeduction selectProExceptionDeductionByDeductionId(Long deductionId) {
        return proExceptionDeductionMapper.selectProExceptionDeductionByDeductionId(deductionId);
    }

    /**
     * 查询异常扣减单列表
     * 
     * @param proExceptionDeduction 异常扣减单
     * @return 异常扣减单
     */
    @Override
    public List<ProExceptionDeduction> selectProExceptionDeductionList(ProExceptionDeduction proExceptionDeduction) {
        return proExceptionDeductionMapper.selectProExceptionDeductionList(proExceptionDeduction);
    }

    @Override
    public ProExceptionDeduction selectByWorkorderIdAndItemCode(Long workorderId, String itemCode) {
        return proExceptionDeductionMapper.selectByWorkorderIdAndItemCode(workorderId, itemCode);
    }

    @Override
    public List<ProExceptionDeduction> selectByWorkorderId(Long workorderId) {
        return proExceptionDeductionMapper.selectByWorkorderId(workorderId);
    }

    @Override
    public List<ProExceptionDeduction> selectByStatus(String status) {
        return proExceptionDeductionMapper.selectByStatus(status);
    }

    /**
     * 新增异常扣减单
     * 
     * @param proExceptionDeduction 异常扣减单
     * @return 结果
     */
    @Override
    public int insertProExceptionDeduction(ProExceptionDeduction proExceptionDeduction) {
        proExceptionDeduction.setCreateTime(DateUtils.getNowDate());
        return proExceptionDeductionMapper.insertProExceptionDeduction(proExceptionDeduction);
    }

    /**
     * 修改异常扣减单
     * 
     * @param proExceptionDeduction 异常扣减单
     * @return 结果
     */
    @Override
    public int updateProExceptionDeduction(ProExceptionDeduction proExceptionDeduction) {
        proExceptionDeduction.setUpdateTime(DateUtils.getNowDate());
        return proExceptionDeductionMapper.updateProExceptionDeduction(proExceptionDeduction);
    }

    /**
     * 批量删除异常扣减单
     * 
     * @param deductionIds 需要删除的异常扣减单主键
     * @return 结果
     */
    @Override
    public int deleteProExceptionDeductionByDeductionIds(Long[] deductionIds) {
        return proExceptionDeductionMapper.deleteProExceptionDeductionByDeductionIds(deductionIds);
    }

    /**
     * 删除异常扣减单信息
     * 
     * @param deductionId 异常扣减单主键
     * @return 结果
     */
    @Override
    public int deleteProExceptionDeductionByDeductionId(Long deductionId) {
        return proExceptionDeductionMapper.deleteProExceptionDeductionByDeductionId(deductionId);
    }

    @Override
    public String checkDeductionCodeUnique(ProExceptionDeduction proExceptionDeduction) {
        Long deductionId = StringUtils.isNull(proExceptionDeduction.getDeductionId()) ? -1L : proExceptionDeduction.getDeductionId();
        ProExceptionDeduction info = proExceptionDeductionMapper.checkDeductionCodeUnique(proExceptionDeduction);
        if (StringUtils.isNotNull(info) && info.getDeductionId().longValue() != deductionId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    @Override
    @Transactional
    public ProExceptionDeduction createOrUpdateExceptionDeduction(Long workorderId, String itemCode, String itemName, 
                                                                 String specification, String unitOfMeasure, 
                                                                 BigDecimal deductionQuantity, Long workshopId, 
                                                                 String workshopName, String warehouseCode, 
                                                                 String warehouseName, String ownerCode, String ownerName) {
        // 查询是否已存在该工单+物料的扣减单
        ProExceptionDeduction existingDeduction = selectByWorkorderIdAndItemCode(workorderId, itemCode);
        
        if (existingDeduction != null) {
            // 更新现有扣减单的数量
            existingDeduction.setDeductionQuantity(existingDeduction.getDeductionQuantity().add(deductionQuantity));
            existingDeduction.setUpdateTime(new Date());
            updateProExceptionDeduction(existingDeduction);
            log.info("更新异常扣减单 - workorderId:{}, itemCode:{}, 新增数量:{}, 总数量:{}", 
                    workorderId, itemCode, deductionQuantity, existingDeduction.getDeductionQuantity());
            return existingDeduction;
        } else {
            // 创建新的扣减单
            ProWorkorder workorder = proWorkorderService.selectProWorkorderByWorkorderId(workorderId);
            if (workorder == null) {
                throw new RuntimeException("工单不存在: " + workorderId);
            }
            
            ProExceptionDeduction newDeduction = new ProExceptionDeduction();
            newDeduction.setDeductionCode(autoCodeUtil.genSerialCode(UserConstants.PRO_EXCEPTION_DEDUCTION_CODE));
            newDeduction.setWorkorderId(workorderId);
            newDeduction.setWorkorderCode(workorder.getWorkorderCode());
            newDeduction.setWorkorderName(workorder.getWorkorderName());
            newDeduction.setItemCode(itemCode);
            newDeduction.setItemName(itemName);
            newDeduction.setSpecification(specification);
            newDeduction.setUnitOfMeasure(unitOfMeasure);
            newDeduction.setDeductionQuantity(deductionQuantity);
            newDeduction.setStatus(ExceptionDeductionStatusEnum.COUNTING.getCode());
            newDeduction.setStatusDesc(ExceptionDeductionStatusEnum.COUNTING.getDesc());
            newDeduction.setWorkshopId(workshopId);
            newDeduction.setWorkshopName(workshopName);
            newDeduction.setWarehouseCode(warehouseCode);
            newDeduction.setWarehouseName(warehouseName);
            newDeduction.setOwnerCode(ownerCode);
            newDeduction.setOwnerName(ownerName);
            newDeduction.setCreateTime(new Date());
            
            insertProExceptionDeduction(newDeduction);
            log.info("创建异常扣减单 - workorderId:{}, itemCode:{}, 数量:{}", workorderId, itemCode, deductionQuantity);
            return newDeduction;
        }
    }

    @Override
    @Transactional
    public int issueOperation(Long deductionId) throws Exception {
        ProExceptionDeduction deduction = selectProExceptionDeductionByDeductionId(deductionId);
        if (deduction == null) {
            throw new Exception("异常扣减单不存在");
        }
        
        // 状态校验
        if (!ExceptionDeductionStatusEnum.PENDING_ISSUE.getCode().equals(deduction.getStatus())) {
            throw new Exception("当前状态不允许领料操作");
        }
        
        // 创建领料单
        WmIssueHeader issueHeader = createIssueHeader(deduction);
        wmIssueHeaderService.insertWmIssueHeader(issueHeader);
        
        // 创建领料单行
        WmIssueLine issueLine = createIssueLine(deduction, issueHeader.getIssueId());
        wmIssueLineService.insertWmIssueLine(issueLine);
        
        // 更新扣减单状态
        deduction.setStatus(ExceptionDeductionStatusEnum.PENDING_PUSH.getCode());
        deduction.setStatusDesc(ExceptionDeductionStatusEnum.PENDING_PUSH.getDesc());
        deduction.setIssueId(issueHeader.getIssueId());
        deduction.setIssueCode(issueHeader.getIssueCode());
        deduction.setIssueTime(new Date());
        
        return updateProExceptionDeduction(deduction);
    }

    @Override
    @Transactional
    public int pushOperation(Long deductionId) throws Exception {
        ProExceptionDeduction deduction = selectProExceptionDeductionByDeductionId(deductionId);
        if (deduction == null) {
            throw new Exception("异常扣减单不存在");
        }
        
        // 状态校验
        if (!ExceptionDeductionStatusEnum.PENDING_PUSH.getCode().equals(deduction.getStatus())) {
            throw new Exception("当前状态不允许推送操作");
        }
        
        // TODO: 调用WMS接口创建调减单据
        // 这里需要根据实际的WMS接口进行实现
        String wmsAdjustOrderNo = createWmsAdjustOrder(deduction);
        
        // 更新扣减单状态
        deduction.setStatus(ExceptionDeductionStatusEnum.COMPLETED.getCode());
        deduction.setStatusDesc(ExceptionDeductionStatusEnum.COMPLETED.getDesc());
        deduction.setWmsAdjustOrderNo(wmsAdjustOrderNo);
        deduction.setPushTime(new Date());
        deduction.setCompleteTime(new Date());
        
        return updateProExceptionDeduction(deduction);
    }

    @Override
    @Transactional
    public int closeOperation(Long deductionId, String closeReason) throws Exception {
        ProExceptionDeduction deduction = selectProExceptionDeductionByDeductionId(deductionId);
        if (deduction == null) {
            throw new Exception("异常扣减单不存在");
        }
        
        // 状态校验 - 待领料和待推送状态可以关闭
        if (!ExceptionDeductionStatusEnum.PENDING_ISSUE.getCode().equals(deduction.getStatus()) &&
            !ExceptionDeductionStatusEnum.PENDING_PUSH.getCode().equals(deduction.getStatus())) {
            throw new Exception("当前状态不允许关闭操作");
        }
        
        // 如果是待推送状态，需要删除对应的领料单
        if (ExceptionDeductionStatusEnum.PENDING_PUSH.getCode().equals(deduction.getStatus()) && 
            deduction.getIssueId() != null) {
            // 删除领料单行
            wmIssueLineService.deleteByIssueHeaderId(deduction.getIssueId());
            // 删除领料单头
            wmIssueHeaderService.deleteWmIssueHeaderByIssueIds(new Long[]{deduction.getIssueId()});
        }
        
        // 更新扣减单状态
        deduction.setStatus(ExceptionDeductionStatusEnum.CLOSED.getCode());
        deduction.setStatusDesc(ExceptionDeductionStatusEnum.CLOSED.getDesc());
        deduction.setCloseReason(closeReason);
        deduction.setCloseTime(new Date());
        
        return updateProExceptionDeduction(deduction);
    }

    @Override
    @Transactional
    public int updateStatusAfterWorkorderComplete(Long workorderId) {
        List<ProExceptionDeduction> deductions = selectByWorkorderId(workorderId);
        int updateCount = 0;
        
        for (ProExceptionDeduction deduction : deductions) {
            if (ExceptionDeductionStatusEnum.COUNTING.getCode().equals(deduction.getStatus())) {
                deduction.setStatus(ExceptionDeductionStatusEnum.PENDING_ISSUE.getCode());
                deduction.setStatusDesc(ExceptionDeductionStatusEnum.PENDING_ISSUE.getDesc());
                updateProExceptionDeduction(deduction);
                updateCount++;
            }
        }
        
        log.info("工单完成后更新异常扣减单状态 - workorderId:{}, 更新数量:{}", workorderId, updateCount);
        return updateCount;
    }

    /**
     * 创建领料单头
     */
    private WmIssueHeader createIssueHeader(ProExceptionDeduction deduction) {
        WmIssueHeader issueHeader = new WmIssueHeader();
        issueHeader.setIssueCode(autoCodeUtil.genSerialCode(UserConstants.WM_ISSUE_HEAD_CODE));
        issueHeader.setIssueName("异常扣减领料单-" + deduction.getDeductionCode());
        issueHeader.setWorkorderId(deduction.getWorkorderId());
        issueHeader.setWorkorderCode(deduction.getWorkorderCode());
        issueHeader.setWorkshopId(deduction.getWorkshopId());
        issueHeader.setWorkshopName(deduction.getWorkshopName());
        issueHeader.setWarehouseCode(deduction.getWarehouseCode());
        issueHeader.setWarehouseName(deduction.getWarehouseName());
        issueHeader.setOwnerCode(deduction.getOwnerCode());
        issueHeader.setOwnerName(deduction.getOwnerName());
        issueHeader.setStatus(UserConstants.ORDER_STATUS_PREPARE);
        issueHeader.setCreateTime(new Date());
        return issueHeader;
    }

    /**
     * 创建领料单行
     */
    private WmIssueLine createIssueLine(ProExceptionDeduction deduction, Long issueId) {
        WmIssueLine issueLine = new WmIssueLine();
        issueLine.setIssueId(issueId);
        issueLine.setWorkorderId(deduction.getWorkorderId());
        issueLine.setItemCode(deduction.getItemCode());
        issueLine.setItemName(deduction.getItemName());
        issueLine.setSpecification(deduction.getSpecification());
        issueLine.setUnitOfMeasure(deduction.getUnitOfMeasure());
        issueLine.setQuantityIssued(deduction.getDeductionQuantity());
        issueLine.setWarehouseCode(deduction.getWarehouseCode());
        issueLine.setWarehouseName(deduction.getWarehouseName());
        issueLine.setCreateTime(new Date());
        return issueLine;
    }

    /**
     * 创建WMS调减单据
     * TODO: 根据实际WMS接口实现
     */
    private String createWmsAdjustOrder(ProExceptionDeduction deduction) {
        // 这里应该调用实际的WMS接口创建调减单据
        // 暂时返回模拟的单据号
        return "ADJ" + System.currentTimeMillis();
    }
}
