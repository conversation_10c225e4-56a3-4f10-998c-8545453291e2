package com.ktg.mes.pro.domain.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ktg.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description:  生产任务列表返回对象
 * @date 2025/4/17 16:24
 */
@Data
public class ProTaskListResVo implements Serializable {
    /** 任务ID */
    private Long taskId;

    /** 任务编号 */
    @Excel(name = "任务编号")
    private String taskCode;

    /** 任务名称 */
    @Excel(name = "任务名称")
    private String taskName;

    /** 生产工单ID */
    @Excel(name = "生产工单ID")
    private Long workorderId;

    /** 生产工单编号 */
    @Excel(name = "生产工单编号")
    private String workorderCode;

    /** 工单名称 */
    @Excel(name = "工单名称")
    private String workorderName;

    /** 工作站ID */
    @Excel(name = "工作站ID")
    private Long workstationId;

    /** 工作站编号 */
    @Excel(name = "工作站编号")
    private String workstationCode;

    /** 工作站名称 */
    @Excel(name = "工作站名称")
    private String workstationName;

    /** 仓库编码 */
    @Excel(name = "仓库编码")
    private String warehouseCode;

    /** 仓库名称 */
    @Excel(name = "仓库名称")
    private String warehouseName;

    /** 货主名称 */
    @Excel(name = "货主名称")
    private String ownerCode;

    /** 货主名称 */
    @Excel(name = "货主名称")
    private String ownerName;

    /** 工艺ID */
    @Excel(name = "工艺ID")
    private Long routeId;

    /** 工艺编号 */
    @Excel(name = "工艺编号")
    private String routeCode;

    /** 工序ID */
    @Excel(name = "工序ID")
    private Long processId;

    /** 工序编码 */
    @Excel(name = "工序编码")
    private String processCode;

    /** 工序名称 */
    @Excel(name = "工序名称")
    private String processName;

    /** 产品物料ID */
    @Excel(name = "产品物料ID")
    private Long itemId;

    /** 产品物料编码 */
    @Excel(name = "产品物料编码")
    private String itemCode;

    /** 产品物料名称 */
    @Excel(name = "产品物料名称")
    private String itemName;

    /** 规格型号 */
    @Excel(name = "规格型号")
    private String specification;

    /** 单位 */
    @Excel(name = "单位")
    private String unitOfMeasure;

    /** 单位描述 */
    private String unitOfMeasureDesc;

    /** 排产数量 */
    @Excel(name = "排产数量")
    private BigDecimal quantity;

    /** 已生产数量 */
    @Excel(name = "已生产数量")
    private BigDecimal quantityProduced;

    /** 合格品数量 */
    @Excel(name = "合格品数量")
    private BigDecimal quantityQuanlify;

    /** 不良品数量 */
    @Excel(name = "不良品数量")
    private BigDecimal quantityUnquanlify;

    /** 调整数量 */
    @Excel(name = "调整数量")
    private BigDecimal quantityChanged;

    /** 客户ID */
    @Excel(name = "客户ID")
    private Long clientId;

    /** 客户编码 */
    @Excel(name = "客户编码")
    private String clientCode;

    /** 客户名称 */
    @Excel(name = "客户名称")
    private String clientName;

    /** 客户简称 */
    @Excel(name = "客户简称")
    private String clientNick;

    /** 开始生产时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始生产时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 生产时长 */
    @Excel(name = "生产时长")
    private Long duration;

    /** 完成生产时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "完成生产时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

    /** 甘特图显示颜色 */
    @Excel(name = "甘特图显示颜色")
    private String colorCode;

    /** 需求日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "需求日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date requestDate;

    /** 生产状态 */
    @Excel(name = "生产状态")
    private String status;

    /** 预留字段1 */
    @Excel(name = "预留字段1")
    private String attr1;

    /** 预留字段2 */
    @Excel(name = "预留字段2")
    private String attr2;

    /** 预留字段3 */
    @Excel(name = "预留字段3")
    private Long attr3;

    /** 预留字段4 */
    @Excel(name = "预留字段4")
    private Long attr4;

    /** 生产批次号 */
    @Excel(name = "生产批次号")
    private String produceBatchCode;

    /** wms单据编号 */
    @Excel(name = "wms单据编号")
    private String wmsOrderNo;

    private String wmsOrderStatus;

    /** 车间id */
    @Excel(name = "车间id")
    private Long workshopId;

    /** 车间名称 */
    @Excel(name = "车间名称")
    private String workshopName;
}
