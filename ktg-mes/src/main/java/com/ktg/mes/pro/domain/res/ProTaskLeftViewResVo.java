package com.ktg.mes.pro.domain.res;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 余料清点VO
 * @date 2025/4/21 13:59
 */
@Data
public class ProTaskLeftViewResVo implements Serializable {
    /**
     * 已生产数量
     */
    private BigDecimal quantityProduced;
    private List<ProTaskItemUsageViewResVo> proTaskItemUsageViewResVoList;
    //报工明细
    private List<ProFeedbackResVo> proFeedbackList;
}
