package com.ktg.mes.pro.domain.req;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 数据推送erp请求参数
 * @date 2025/7/1 14:39
 */
@Data
public class ProWorkOrderPushErpReqVo implements Serializable {
    /**
     * 工单id
     */
    private Long workorderId;

    /**
     * 报关单附件url
     */
    private String declareOrderAttachUrl;

    /**
     * 报关单附件名称
     */
    private String declareOrderAttachName;

    /**
     * 核放单附件url
     */
    private String checklistAttachUrl;

    /**
     * 核放单附件名称
     */
    private String checklistAttachName;

}
