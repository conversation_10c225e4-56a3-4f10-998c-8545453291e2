package com.ktg.mes.pro.domain.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/5/26 19:15
 */
@Data
public class TaskListQueryReqVo implements Serializable {
    @ApiModelProperty("工单id")
    private Long workorderId;

    @ApiModelProperty("状态,多选时用逗号分割")
    private String status;

    @ApiModelProperty("排除状态,多选时用逗号分割")
    private String excludeStatus;

    private List<String> statusList;

    private List<String> excludeStatusList;
}
