package com.ktg.mes.pro.mapper;

import com.ktg.mes.pro.domain.ProTaskBomSkuLotRecord;

import java.util.List;

/**
 * 生产任务BOM批次信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
public interface ProTaskBomSkuLotRecordMapper
{
    /**
     * 查询生产任务BOM批次信息
     *
     * @param recordId 生产任务BOM批次信息主键
     * @return 生产任务BOM批次信息
     */
    public ProTaskBomSkuLotRecord selectProTaskBomSkuLotRecordByRecordId(Long recordId);

    /**
     * 查询生产任务BOM批次信息列表
     *
     * @param proTaskBomSkuLotRecord 生产任务BOM批次信息
     * @return 生产任务BOM批次信息集合
     */
    public List<ProTaskBomSkuLotRecord> selectProTaskBomSkuLotRecordList(ProTaskBomSkuLotRecord proTaskBomSkuLotRecord);

    /**
     * 新增生产任务BOM批次信息
     *
     * @param proTaskBomSkuLotRecord 生产任务BOM批次信息
     * @return 结果
     */
    public int insertProTaskBomSkuLotRecord(ProTaskBomSkuLotRecord proTaskBomSkuLotRecord);

    /**
     * 修改生产任务BOM批次信息
     *
     * @param proTaskBomSkuLotRecord 生产任务BOM批次信息
     * @return 结果
     */
    public int updateProTaskBomSkuLotRecord(ProTaskBomSkuLotRecord proTaskBomSkuLotRecord);

    /**
     * 删除生产任务BOM批次信息
     *
     * @param recordId 生产任务BOM批次信息主键
     * @return 结果
     */
    public int deleteProTaskBomSkuLotRecordByRecordId(Long recordId);

    /**
     * 批量删除生产任务BOM批次信息
     *
     * @param recordIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProTaskBomSkuLotRecordByRecordIds(Long[] recordIds);
}