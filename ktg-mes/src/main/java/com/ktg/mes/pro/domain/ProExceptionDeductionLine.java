package com.ktg.mes.pro.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ktg.common.annotation.Excel;
import com.ktg.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 异常扣减单表体对象 pro_exception_deduction_line
 * 记录具体的任务编码、缺失原因、缺失批次id、缺失数量等详细信息
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@TableName("pro_exception_deduction_line")
public class ProExceptionDeductionLine extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 表体ID */
    private Long lineId;

    /** 扣减单ID */
    @Excel(name = "扣减单ID")
    private Long deductionId;

    /** 扣减单编号 */
    @Excel(name = "扣减单编号")
    private String deductionCode;

    /** 生产工单ID */
    @Excel(name = "生产工单ID")
    private Long workorderId;

    /** 生产工单编号 */
    @Excel(name = "生产工单编号")
    private String workorderCode;

    /** 生产任务ID */
    @Excel(name = "生产任务ID")
    private Long taskId;

    /** 生产任务编码 */
    @Excel(name = "生产任务编码")
    private String taskCode;

    /** 物料编码 */
    @Excel(name = "物料编码")
    private String itemCode;

    /** 物料名称 */
    @Excel(name = "物料名称")
    private String itemName;

    /** 缺失原因 */
    @Excel(name = "缺失原因")
    private String missingReason;

    /** 缺失批次ID */
    @Excel(name = "缺失批次ID")
    private String missingLotId;

    /** 缺失数量 */
    @Excel(name = "缺失数量")
    private BigDecimal missingQuantity;

    /** 单位 */
    @Excel(name = "单位")
    private String unitOfMeasure;

    /** 触发类型 */
    @Excel(name = "触发类型")
    private String triggerType;

    /** 触发说明 */
    @Excel(name = "触发说明")
    private String triggerDescription;

    /** 实际损耗 */
    @Excel(name = "实际损耗")
    private BigDecimal actualLoss;

    /** 系统损耗 */
    @Excel(name = "系统损耗")
    private BigDecimal systemLoss;

    /** 额外报工数量 */
    @Excel(name = "额外报工数量")
    private BigDecimal extraFeedbackQuantity;

    /** 保税粒子使用比例 */
    @Excel(name = "保税粒子使用比例")
    private BigDecimal bondedParticleRatio;

    /** 预留字段1 */
    @Excel(name = "预留字段1")
    private String attr1;

    /** 预留字段2 */
    @Excel(name = "预留字段2")
    private String attr2;

    /** 预留字段3 */
    @Excel(name = "预留字段3")
    private Long attr3;

    /** 预留字段4 */
    @Excel(name = "预留字段4")
    private Long attr4;
}
