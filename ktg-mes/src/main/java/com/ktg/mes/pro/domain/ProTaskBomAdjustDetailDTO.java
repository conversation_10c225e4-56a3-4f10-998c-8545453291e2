package com.ktg.mes.pro.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 调用wms库存调整明细
 * @date 2025/5/13 14:51
 */
@Data
public class ProTaskBomAdjustDetailDTO implements Serializable {
    @ApiModelProperty(value = "商品编码")
    private String skuCode;
    @ApiModelProperty(value = "批次ID")
    private String skuLotNo;
    @ApiModelProperty(value = "库位")
    private String locationCode;
    @ApiModelProperty(value = "调整数量")
    private BigDecimal adjustQty;
    @ApiModelProperty(value = "入库关联单号")
    private String externalLinkBillNo;
    @ApiModelProperty(value = "类型")
    private String type;
}
