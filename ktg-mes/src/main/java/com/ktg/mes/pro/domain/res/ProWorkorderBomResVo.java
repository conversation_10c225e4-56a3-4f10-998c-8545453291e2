package com.ktg.mes.pro.domain.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 生产工单BOM组成响应对象
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
public class ProWorkorderBomResVo implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /** 记录ID */
    @ApiModelProperty("记录ID")
    private Long lineId;

    /** 生产工单ID */
    @ApiModelProperty("生产工单ID")
    private Long workorderId;

    /** 产品物料ID */
    @ApiModelProperty("产品物料ID")
    private Long itemId;

    /** 产品物料编码 */
    @ApiModelProperty("产品物料编码")
    private String itemCode;

    /** 产品物料名称 */
    @ApiModelProperty("产品物料名称")
    private String itemName;

    /** 规格型号 */
    @ApiModelProperty("规格型号")
    private String itemSpc;

    /** 单位 */
    @ApiModelProperty("单位")
    private String unitOfMeasure;

    /** 物料使用比例 */
    @ApiModelProperty("物料使用比例")
    private BigDecimal useRatio;

    /** 预计使用量 */
    @ApiModelProperty("预计使用量")
    private BigDecimal quantity;

    /** 贸易类型代码 */
    @ApiModelProperty("贸易类型代码")
    private Integer tradeType;

    /** 贸易类型描述 */
    @ApiModelProperty("贸易类型描述")
    private String tradeTypeDesc;

    /** 是否启用 */
    @ApiModelProperty("是否启用")
    private String enableFlag;

    /** 备注 */
    @ApiModelProperty("备注")
    private String remark;

    /** 预留字段1 */
    @ApiModelProperty("预留字段1")
    private String attr1;

    /** 预留字段2 */
    @ApiModelProperty("预留字段2")
    private String attr2;

    /** 预留字段3 */
    @ApiModelProperty("预留字段3")
    private Long attr3;

    /** 预留字段4 */
    @ApiModelProperty("预留字段4")
    private Long attr4;

    /** 创建者 */
    @ApiModelProperty("创建者")
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;

    /** 更新者 */
    @ApiModelProperty("更新者")
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private Date updateTime;
}
