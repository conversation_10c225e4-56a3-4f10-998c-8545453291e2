package com.ktg.mes.pro.domain.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 异常扣减单响应VO
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
public class ProExceptionDeductionResVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 扣减单ID */
    private Long deductionId;

    /** 扣减单编号 */
    private String deductionCode;

    /** 生产工单ID */
    private Long workorderId;

    /** 生产工单编号 */
    private String workorderCode;

    /** 生产工单名称 */
    private String workorderName;

    /** 物料编码 */
    private String itemCode;

    /** 物料名称 */
    private String itemName;

    /** 规格型号 */
    private String specification;

    /** 单位 */
    private String unitOfMeasure;

    /** 代扣数量 */
    private BigDecimal deductionQuantity;

    /** 状态 */
    private String status;

    /** 状态描述 */
    private String statusDesc;

    /** 车间ID */
    private Long workshopId;

    /** 车间名称 */
    private String workshopName;

    /** 仓库编码 */
    private String warehouseCode;

    /** 仓库名称 */
    private String warehouseName;

    /** 货主编码 */
    private String ownerCode;

    /** 货主名称 */
    private String ownerName;

    /** 关联领料单ID */
    private Long issueId;

    /** 关联领料单编号 */
    private String issueCode;

    /** WMS调减单号 */
    private String wmsAdjustOrderNo;

    /** 领料时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date issueTime;

    /** 推送时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date pushTime;

    /** 完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;

    /** 关闭时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date closeTime;

    /** 关闭原因 */
    private String closeReason;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 备注 */
    private String remark;

    /** 可执行操作列表 */
    private String[] availableActions;
}
