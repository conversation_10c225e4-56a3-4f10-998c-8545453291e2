package com.ktg.mes.pro.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ktg.common.annotation.Excel;
import com.ktg.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 生产异常标记对象 pro_task_exception
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
@Data
@TableName("pro_task_exception")
public class ProTaskException extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 异常记录ID */
    private Long exceptionId;

    /** 生产任务ID */
    @Excel(name = "生产任务ID")
    private Long taskId;

    /** 生产任务编号 */
    @Excel(name = "生产任务编号")
    private String taskCode;

    /** 生产工单ID */
    @Excel(name = "生产工单ID")
    private Long workorderId;

    /** 生产工单编号 */
    @Excel(name = "生产工单编号")
    private String workorderCode;

    /** 原材料代码 */
    @Excel(name = "原材料代码")
    private String itemCode;

    /** 原材料名称 */
    @Excel(name = "原材料名称")
    private String itemName;

    /** 异常原因（字典值） */
    @Excel(name = "异常原因", readConverterExp = "字典值")
    private String exceptionReason;

    /** 异常原因描述（字典展示名称） */
    @Excel(name = "异常原因描述", readConverterExp = "字典展示名称")
    private String exceptionReasonDesc;

    /** 异常数量 */
    @Excel(name = "异常数量")
    private BigDecimal exceptionQuantity;

    /** 登记人 */
    @Excel(name = "登记人")
    private String registrant;

    /** 登记人姓名 */
    @Excel(name = "登记人姓名")
    private String registrantName;

    /** 登记时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "登记时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date registerTime;

    /** 附件URL */
    @Excel(name = "附件URL")
    private String attachmentUrl;

    /** 附件名称 */
    @Excel(name = "附件名称")
    private String attachmentName;

    /** 附件大小（字节） */
    @Excel(name = "附件大小", readConverterExp = "字节")
    private Long attachmentSize;

    /** 状态（0正常 1删除） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=删除")
    private String status;
}
