package com.ktg.mes.pro.domain.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 生产异常标记批量操作请求对象
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
@Data
public class ProTaskExceptionBatchReqVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 生产任务ID */
    @ApiModelProperty(value = "生产任务ID", required = true)
    @NotNull(message = "生产任务ID不能为空")
    private Long taskId;

    /** 异常标记列表 */
    @ApiModelProperty(value = "异常标记列表", required = true)
    @NotEmpty(message = "异常标记列表不能为空")
    @Valid
    private List<ProTaskExceptionItemReqVo> exceptionList;
}
