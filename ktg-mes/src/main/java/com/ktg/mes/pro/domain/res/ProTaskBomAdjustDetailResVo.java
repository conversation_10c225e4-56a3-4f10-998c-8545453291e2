package com.ktg.mes.pro.domain.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/5/13 15:36
 */
@Data
public class ProTaskBomAdjustDetailResVo implements Serializable {
    @ApiModelProperty(value = "商品编码")
    private String skuCode;
    @ApiModelProperty(value = "批次ID")
    private String skuLotNo;
    @ApiModelProperty(value = "库位")
    private String locationCode;
    @ApiModelProperty(value = "调整数量")
    private BigDecimal adjustQty;
    @ApiModelProperty(value = "WMS单号")
    private String wmsOrderNo;
}
