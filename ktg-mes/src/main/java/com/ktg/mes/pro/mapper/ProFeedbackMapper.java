package com.ktg.mes.pro.mapper;

import com.ktg.mes.pro.domain.ProFeedback;
import com.ktg.mes.pro.domain.req.ProFeedbackListReqVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 生产报工记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-07-10
 */
public interface ProFeedbackMapper 
{
    /**
     * 查询生产报工记录
     * 
     * @param recordId 生产报工记录主键
     * @return 生产报工记录
     */
    public ProFeedback selectProFeedbackByRecordId(Long recordId);

    /**
     * 查询生产报工记录列表
     * 
     * @param proFeedback 生产报工记录
     * @return 生产报工记录集合
     */
    public List<ProFeedback> selectProFeedbackList(ProFeedback proFeedback);

    /**
     * 新增生产报工记录
     * 
     * @param proFeedback 生产报工记录
     * @return 结果
     */
    public int insertProFeedback(ProFeedback proFeedback);

    /**
     * 修改生产报工记录
     * 
     * @param proFeedback 生产报工记录
     * @return 结果
     */
    public int updateProFeedback(ProFeedback proFeedback);

    /**
     * 删除生产报工记录
     * 
     * @param recordId 生产报工记录主键
     * @return 结果
     */
    public int deleteProFeedbackByRecordId(Long recordId);

    /**
     * 批量删除生产报工记录
     * 
     * @param recordIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProFeedbackByRecordIds(Long[] recordIds);

    List<ProFeedback> proFeedbackPage(ProFeedbackListReqVo reqVo);

    List<ProFeedback> selectProFeedbackByWorkorderIdList(@Param("workorderIdList")List<Long> workorderIdList);

    List<ProFeedback> selectProFeedbackByTaskIdList(@Param("taskIdList")List<Long> taskIdList);

    /**
     * 根据批次ID列表查询生产报工记录
     * @param skuLotNoList 批次ID列表
     * @return 生产报工记录集合
     */
    List<ProFeedback> selectProFeedbackBySkuLotNoList(@Param("skuLotNoList") List<String> skuLotNoList);
}
