package com.ktg.mes.pro.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.dt.component.common.enums.base.LocationTypeEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.platform.wms.rpc.client.mes.stock.IStockLocationMesQuery;
import com.dt.platform.wms.rpc.client.mes.stock.StockLocationQueryRequest;
import com.dt.platform.wms.rpc.client.mes.stock.StockLocationQueryResponse;
import com.ktg.common.constant.UserConstants;
import com.ktg.common.enums.OrderStatusEnum;
import com.ktg.common.enums.ProductRequirementEnum;
import com.ktg.common.utils.DateUtils;
import com.ktg.common.utils.StringUtils;
import com.ktg.mes.md.domain.MdUnitMeasure;
import com.ktg.mes.md.service.IMdUnitMeasureService;
import com.ktg.mes.pro.domain.*;
import com.ktg.mes.pro.domain.req.ProWorkorderErpPageReqVo;
import com.ktg.mes.pro.domain.req.ProWorkorderListReqVo;
import com.ktg.mes.pro.domain.res.*;
import com.ktg.mes.pro.mapper.ProWorkorderMapper;
import com.ktg.mes.pro.service.*;
import com.ktg.mes.wm.domain.WmWarehouse;
import com.ktg.mes.wm.service.IWmIssueHeaderService;
import com.ktg.mes.wm.service.IWmRtIssueService;
import com.ktg.mes.wm.service.IWmWarehouseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 生产工单Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-05-09
 */
@Slf4j
@Service
public class ProWorkorderServiceImpl implements IProWorkorderService {
    @Autowired
    private ProWorkorderMapper proWorkorderMapper;

    @Autowired
    private IProWorkorderBomService proWorkorderBomService;
    @Autowired
    private IProTaskService proTaskService;
    @Autowired
    private IProTaskBomService proTaskBomService;
    @Autowired
    private IProFeedbackService proFeedbackService;
    @Autowired
    private IWmIssueHeaderService wmIssueHeaderService;
    @Autowired
    private IWmRtIssueService wmRtIssueService;
    @Autowired
    private IWmWarehouseService wmWarehouseService;
    @Autowired
    private IMdUnitMeasureService mdUnitMeasureService;
    @DubboReference
    private IStockLocationMesQuery iStockLocationMesQuery;

    /**
     * 查询生产工单
     *
     * @param workorderId 生产工单主键
     * @return 生产工单
     */
    @Override
    public ProWorkorder selectProWorkorderByWorkorderId(Long workorderId) {
        return proWorkorderMapper.selectProWorkorderByWorkorderId(workorderId);
    }

    @Override
    public ProWorkorder selectProWorkorderByWorkorderCode(String workorderCode) {
        return proWorkorderMapper.selectProWorkorderByWorkorderCode(workorderCode);
    }

    /**
     * 查询生产工单列表
     *
     * @param workorderIdList 生产工单ID集合
     * @return 生产工单
     */
    @Override
    public List<ProWorkorder> selectProWorkorderByWorkorderIds(List<Long> workorderIdList) {
        return proWorkorderMapper.selectProWorkorderByWorkorderIds(workorderIdList);
    }

    /**
     * 查询生产工单
     *
     * @param workorderId 生产工单主键
     * @return 生产工单
     */
    @Override
    public List<ProWorkorder> selectProWorkorderListByParentId(Long workorderId) {
        return proWorkorderMapper.selectProWorkorderListByParentId(workorderId);
    }

    /**
     * 查询生产工单列表
     *
     * @param proWorkorder 生产工单
     * @return 生产工单
     */
    @Override
    public List<ProWorkorder> selectProWorkorderList(ProWorkorder proWorkorder) {
        return proWorkorderMapper.selectProWorkorderList(proWorkorder);
    }

    @Override
    public List<ProWorkorder> proWorkorderPage(ProWorkorderListReqVo reqVo) {
        return proWorkorderMapper.proWorkorderPage(reqVo);
    }

    @Override
    public List<ProWorkorder> proWorkorderErpPage(ProWorkorderErpPageReqVo reqVo) {
        return proWorkorderMapper.proWorkorderErpPage(reqVo);
    }

    /**
     * 校验工单编号是否唯一
     *
     * @param proWorkorder
     * @return
     */
    @Override
    public String checkWorkorderCodeUnique(ProWorkorder proWorkorder) {
        ProWorkorder workorder = proWorkorderMapper.checkWorkorderCodeUnique(proWorkorder);
        Long workorderId = proWorkorder.getWorkorderId() == null ? -1L : proWorkorder.getWorkorderId();
        if (StringUtils.isNotNull(workorder) && workorder.getWorkorderId().longValue() != workorderId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }


    /**
     * 新增生产工单
     *
     * @param proWorkorder 生产工单
     * @return 结果
     */
    @Override
    public int insertProWorkorder(ProWorkorder proWorkorder) {
        if (proWorkorder.getParentId() != null) {
            ProWorkorder parent = proWorkorderMapper.selectProWorkorderByWorkorderId(proWorkorder.getParentId());
            if (StringUtils.isNotNull(parent)) {
                proWorkorder.setAncestors(parent.getAncestors() + "," + parent.getParentId());
            }
        }

        proWorkorder.setCreateTime(DateUtils.getNowDate());
        return proWorkorderMapper.insertProWorkorder(proWorkorder);
    }

    /**
     * 修改生产工单
     *
     * @param proWorkorder 生产工单
     * @return 结果
     */
    @Override
    public int updateProWorkorder(ProWorkorder proWorkorder) {
        proWorkorder.setUpdateTime(DateUtils.getNowDate());
        return proWorkorderMapper.updateProWorkorder(proWorkorder);
    }

    /**
     * 批量删除生产工单
     *
     * @param workorderIds 需要删除的生产工单主键
     * @return 结果
     */
    @Override
    public int deleteProWorkorderByWorkorderIds(Long[] workorderIds) {
        return proWorkorderMapper.deleteProWorkorderByWorkorderIds(workorderIds);
    }

    /**
     * 删除生产工单信息
     *
     * @param workorderId 生产工单主键
     * @return 结果
     */
    @Override
    public int deleteProWorkorderByWorkorderId(Long workorderId) {
        return proWorkorderMapper.deleteProWorkorderByWorkorderId(workorderId);
    }

    @Override
    public ProWorkorderOverViewResVo overView(Long workorderId) throws Exception {
        ProWorkorder proWorkorder = this.selectProWorkorderByWorkorderId(workorderId);
        if (Objects.isNull(proWorkorder)) {
            throw new Exception("生产工单不存在！");
        }
        //获取bom关系
        List<ProWorkorderBom> proWorkorderBoms = proWorkorderBomService.selectProWorkorderBomListByWorkorderId(workorderId);
        Map<String, ProWorkorderBom> itemCodeWorkorderBomMap = proWorkorderBoms.stream().collect(Collectors.toMap(ProWorkorderBom::getItemCode, Function.identity(), (v1, v2) -> v1));
        List<String> bomItemCodeList = proWorkorderBoms.stream().map(ProWorkorderBom::getItemCode).distinct().collect(Collectors.toList());
        //获取任务列表
        List<ProTask> proTaskList = proTaskService.selectProTaskListByWorkorderId(workorderId);
        //获取任务bom记录列表
        List<ProTaskBom> proTaskBomList = proTaskBomService.selectProTaskBomListByWorkorderId(workorderId);
        Map<Long, List<ProTaskBom>> taskIdBomMap = proTaskBomList.stream().collect(Collectors.groupingBy(ProTaskBom::getTaskId));
        List<ProFeedback> proFeedbackList = proFeedbackService.selectProFeedbackByWorkorderId(workorderId);
        Map<Long, List<ProFeedback>> taskIdFeedMap = proFeedbackList.stream().collect(Collectors.groupingBy(ProFeedback::getTaskId));

        ProWorkorderOverViewResVo resVo = new ProWorkorderOverViewResVo();
        List<ProWorkorderOverViewTaskResVo> taskResVoList = new ArrayList<>();
        for (ProTask proTask : proTaskList) {
            ProWorkorderOverViewTaskResVo taskResVo = new ProWorkorderOverViewTaskResVo();
            Long taskId = proTask.getTaskId();
            taskResVo.setTaskId(taskId);
            taskResVo.setTaskCode(proTask.getTaskCode());
            taskResVo.setQuantity(proTask.getQuantity());
            taskResVo.setQuantityProduced(proTask.getQuantityProduced());
            taskResVo.setWorkstationName(proTask.getWorkstationName());

            // 设置单位信息，取值为对应proworkorder的unitOfMeasure
            if (StringUtils.isNotEmpty(proWorkorder.getUnitOfMeasure())) {
                taskResVo.setUnitOfMeasure(proWorkorder.getUnitOfMeasure());
                // 查询单位描述
                MdUnitMeasure mdUnitMeasure = mdUnitMeasureService.selectMdUnitByCode(proWorkorder.getUnitOfMeasure());
                if (Objects.nonNull(mdUnitMeasure)) {
                    taskResVo.setUnitOfMeasureDesc(mdUnitMeasure.getMeasureName());
                }
            }

            if (Objects.nonNull(proTask.getStatus())) {
                OrderStatusEnum statusEnum = OrderStatusEnum.getByCode(proTask.getStatus());
                if (Objects.nonNull(statusEnum)) {
                    taskResVo.setTaskStatusDesc(statusEnum.getInfo());
                }
            }
            taskResVo.setStartTime(proTask.getStartTime());
            taskResVo.setEndTime(proTask.getEndTime());
            taskResVo.setProduceBatchCode(proTask.getProduceBatchCode());

            BigDecimal quantityUncheck = BigDecimal.ZERO;
//            BigDecimal quantityUnquanlified = BigDecimal.ZERO;
            BigDecimal lossQuantity = BigDecimal.ZERO;
            BigDecimal leaveSampleQuantity = BigDecimal.ZERO;
            BigDecimal inspectionQuantity = BigDecimal.ZERO;

            List<ProFeedback> feedbackList = taskIdFeedMap.get(taskId);
            if (!CollectionUtils.isEmpty(feedbackList)) {
                feedbackList = feedbackList.stream().filter(f -> Objects.nonNull(f.getStatus()) && Objects.equals(f.getStatus(), UserConstants.ORDER_STATUS_FINISHED)).collect(Collectors.toList());
                for (ProFeedback proFeedback : feedbackList) {
                    quantityUncheck = quantityUncheck.add(proFeedback.getQuantityUncheck());
//                    quantityUnquanlified = quantityUnquanlified.add(proFeedback.getQuantityUnquanlified());
                    lossQuantity = lossQuantity.add(proFeedback.getLossQuantity());
                    leaveSampleQuantity = leaveSampleQuantity.add(proFeedback.getLeaveSampleQuantity());
                    inspectionQuantity = inspectionQuantity.add(proFeedback.getInspectionQuantity());
                }
            }
//            taskResVo.setQuantityUncheck(quantityUncheck);
//            taskResVo.setQuantityUnquanlified(quantityUnquanlified);
//            taskResVo.setLossQuantity(lossQuantity);
//            taskResVo.setLeaveSampleQuantity(leaveSampleQuantity);
//            String countDetail = "待检品" + quantityUncheck + "|不合格品：" + quantityUnquanlified + "|留样：" + leaveSampleQuantity + "|送检：" + inspectionQuantity;
            String countDetail = "待检品" + quantityUncheck +"|留样：" + leaveSampleQuantity + "|送检：" + inspectionQuantity;
            taskResVo.setCountDetail(countDetail);

            List<ProTaskBom> proTaskBoms = taskIdBomMap.get(taskId);
            Map<String, ProTaskBom> itemCodeBomMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(proTaskBoms)) {
                itemCodeBomMap = proTaskBoms.stream().collect(Collectors.toMap(ProTaskBom::getItemCode, Function.identity(), (v1, v2) -> v1));
            }
            int i = 1;
            List<ProWorkorderOverViewBomResVo> resVoList = new ArrayList<>();
            for (String bomItemCode : bomItemCodeList) {
                ProTaskBom proTaskBom = itemCodeBomMap.get(bomItemCode);
                if (Objects.isNull(proTaskBom)) {
                    continue;
                }
                ProWorkorderOverViewBomResVo bomResVo = new ProWorkorderOverViewBomResVo();
                bomResVo.setNumber(i++);
                bomResVo.setItemCode(proTaskBom.getItemCode());
                bomResVo.setItemName(proTaskBom.getItemName());

                ProWorkorderBom proWorkorderBom = itemCodeWorkorderBomMap.get(bomItemCode);
                if (Objects.nonNull(proWorkorderBom)){
                    MdUnitMeasure mdUnitMeasure = mdUnitMeasureService.selectMdUnitByCode(proWorkorderBom.getUnitOfMeasure());
                    if (Objects.nonNull(mdUnitMeasure)){
                        bomResVo.setUnitOfMeasure(mdUnitMeasure.getMeasureCode());
                        bomResVo.setUnitOfMeasureDesc(mdUnitMeasure.getMeasureName());
                    }
                }
                bomResVo.setActualUsage(proTaskBom.getActualUsage());
//                bomResVo.setTheoreticalUsage(proTaskBom.getTheoreticalUsage());
                bomResVo.setLossQuantity(proTaskBom.getLossQuantity());
                bomResVo.setLossPercentage(proTaskBom.getLossPercentage());
                bomResVo.setLossDetail(proTaskBom.getLossDetail());
                bomResVo.setProductionLoss(proTaskBom.getProductionLoss());
                resVoList.add(bomResVo);
            }
            taskResVo.setBomResVoList(resVoList);
            taskResVoList.add(taskResVo);
        }
        resVo.setTaskResVoList(taskResVoList);
        return resVo;
    }

    @Override
    public List<ProWorkorderBomViewResVo> bomView(Long workorderId) throws Exception {
        log.info("获取BOM信息 workorderId={}", workorderId);
        ProWorkorderBom param = new ProWorkorderBom();
        param.setWorkorderId(workorderId);
        ProWorkorder proWorkorder = this.selectProWorkorderByWorkorderId(workorderId);
        if (Objects.isNull(proWorkorder)) {
            throw new Exception("生产工单不存在");
        }
        //2.查询仓库为MES建立绑定关系且的仓库，要求为[启用状态]。
        String warehouseCode = proWorkorder.getWarehouseCode();
        WmWarehouse wmWarehouse = wmWarehouseService.selectWmWarehouseByWarehouseCode(warehouseCode);
        if (Objects.isNull(wmWarehouse)) {
            throw new Exception("仓库不存在");
        }
        if (Objects.equals(wmWarehouse.getFrozenFlag(), UserConstants.YES)) {
            throw new Exception("仓库不为启用状态");
        }
        StockLocationQueryRequest queryRequest = new StockLocationQueryRequest();
        //1.查询货主为生产工单所选货主
        queryRequest.setWarehouseCode(proWorkorder.getWarehouseCode());
        queryRequest.setCargoCode(proWorkorder.getOwnerCode());
        //3.基于[产品代码]获取对应BOM清单，查询BOM清单下的原材料料号作为此处物料的查询料号。
        List<ProWorkorderBom> boms = proWorkorderBomService.selectProWorkorderBomList(param);
        log.info("查询BOM信息 workorderId={} boms={}", workorderId, JSON.toJSONString(boms));
        List<String> itemCodeList = boms.stream().map(ProWorkorderBom::getItemCode).distinct().collect(Collectors.toList());
        queryRequest.setSkuCodeList(itemCodeList);
        queryRequest.setLocationTypeList(ListUtil.toList(LocationTypeEnum.LOCATION_TYPE_PICK.getType(), LocationTypeEnum.LOCATION_TYPE_STORE.getType()));
        queryRequest.setSkuQuality(SkuQualityEnum.SKU_QUALITY_AVL.getLevel());
        log.info("查询库存位置信息请求参数：{}", JSON.toJSONString(queryRequest));
        List<StockLocationQueryResponse> stockLocationQueryResponses = iStockLocationMesQuery.list(queryRequest);
        log.info("库存查询结果：{}", JSON.toJSONString(stockLocationQueryResponses));
        //4.仅查询[正品库区]库位的商品，WMS中[次品库区]、[暂存区]过滤；MES系统中定义为[成品待检区]、[成品留样区]、[线边库区(正次品)]的库区过滤。
        Map<String, List<StockLocationQueryResponse>> itemCodeStockMap = stockLocationQueryResponses.stream()
                .filter(stock -> Objects.isNull(stock.getZoneAttr()) || StringUtils.isEmpty(stock.getZoneAttr()))
                .collect(Collectors.groupingBy(StockLocationQueryResponse::getSkuCode));
        log.info("过滤库区属性后的数据：{}", JSON.toJSONString(itemCodeStockMap));
        Map<String, List<ProWorkOrderKeyMdItemResVo>> keyMdItemResVoMap;
        String keyItems = proWorkorder.getKeyItems();
        if (Objects.nonNull(keyItems)) {
            List<ProWorkOrderKeyMdItemResVo> mdItemResVos = JSON.parseArray(proWorkorder.getKeyItems(), ProWorkOrderKeyMdItemResVo.class);
            keyMdItemResVoMap = mdItemResVos.stream().collect(Collectors.groupingBy(ProWorkOrderKeyMdItemResVo::getItemCode));
        } else {
            keyMdItemResVoMap = new HashMap<>(8);
        }
        List<ProWorkorderBomViewResVo> resVoList = boms.stream().map(bom -> {
            String itemCode = bom.getItemCode();
            ProWorkorderBomViewResVo resVo = new ProWorkorderBomViewResVo();
            resVo.setItemName(bom.getItemName());
            resVo.setItemCode(bom.getItemCode());
            resVo.setWarehouseName(proWorkorder.getWarehouseName());
            BigDecimal totalAvailableQty;
            BigDecimal issueQty = BigDecimal.ZERO;
            BigDecimal stockQty = BigDecimal.ZERO;
            if (itemCodeStockMap.containsKey(itemCode)) {
                List<StockLocationQueryResponse> stockList = itemCodeStockMap.get(itemCode);
                //6.基于查询结果，对关键原材料要求进行过滤。目前关键原材料要求只有一种，即为指定BOM清单中某种物料，仅查询满足其[生产批次号]为指定值的部分库存。
                if (keyMdItemResVoMap.containsKey(itemCode)) {
                    List<ProWorkOrderKeyMdItemResVo> proWorkOrderKeyMdItemResVos = keyMdItemResVoMap.get(itemCode);
                    for (ProWorkOrderKeyMdItemResVo keyMdItemResVo : proWorkOrderKeyMdItemResVos) {
                        log.info("关键原材料信息 itemCode={} keyMdItemResVo={}", itemCode, JSON.toJSONString(keyMdItemResVo));
                        if (Objects.equals(keyMdItemResVo.getProduceRequirement(), ProductRequirementEnum.PRODUCT_BATCH_CODE.getCode())) {
                            stockList = stockList.stream().filter(c -> Objects.equals(c.getSkuCode(), keyMdItemResVo.getItemCode())
                                            && Objects.equals(c.getProductionNo(), keyMdItemResVo.getSpecificInformation()))
                                    .collect(Collectors.toList());
                        }
                        if (Objects.equals(keyMdItemResVo.getProduceRequirement(), ProductRequirementEnum.PRODUCT_DATE.getCode())) {
                            stockList = stockList.stream().filter(c -> Objects.equals(c.getSkuCode(), keyMdItemResVo.getItemCode())
                                            && Objects.equals(c.getManufDate(), Long.valueOf(keyMdItemResVo.getSpecificInformation())))
                                    .collect(Collectors.toList());
                        }
                        if (Objects.equals(keyMdItemResVo.getProduceRequirement(), ProductRequirementEnum.EXTERNAL_LINK_BILL_NO.getCode())) {
                            stockList = stockList.stream().filter(c -> Objects.equals(c.getSkuCode(), keyMdItemResVo.getItemCode())
                                            && Objects.equals(c.getExternalLinkBillNo(), keyMdItemResVo.getSpecificInformation()))
                                    .collect(Collectors.toList());
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(stockList)) {
                    stockQty = stockList.stream().map(StockLocationQueryResponse::getAvailableQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    //取第一个吧
                    resVo.setWarehouseName(stockList.get(0).getWarehouseName());
                }
            }
            totalAvailableQty = stockQty.subtract(issueQty);
            resVo.setPlanQty(bom.getQuantity());
            resVo.setAvailableQty(totalAvailableQty);
            return resVo;
        }).collect(Collectors.toList());
        return resVoList;
    }

    @Override
    public List<ProWorkorder> selectByExternalLinkBillNo(String externalLinkBillNo) {
        if (Objects.isNull(externalLinkBillNo)) {
            return new ArrayList<>();
        }
        ProWorkorder proWorkorder = new ProWorkorder();
        proWorkorder.setExternalLinkBillNo(externalLinkBillNo);
        return proWorkorderMapper.selectProWorkorderList(proWorkorder);
    }

    @Override
    public List<ProWorkorder> selectByProductSkuList(List<String> productSkuList) {
        if (CollectionUtils.isEmpty(productSkuList)) {
            return new ArrayList<>();
        }
        return proWorkorderMapper.selectByProductSkuList(productSkuList);
    }

    @Override
    public List<ProWorkorder> queryByCondition(List<String> proWorkorderCodeList, String proWorkorderStatus, Long finishDateBegin, Long finishDateEnd, List<String> productSkuList, List<Long> productSkuLotWorkorderList) {
        if (CollectionUtils.isEmpty(proWorkorderCodeList)
                && Objects.isNull(proWorkorderStatus)
                && (Objects.isNull(finishDateBegin) || Objects.isNull(finishDateEnd))
                && CollectionUtils.isEmpty(productSkuList)
                && CollectionUtils.isEmpty(productSkuLotWorkorderList)
        ) {
            return new ArrayList<>();
        }
        return proWorkorderMapper.selectByCondition(proWorkorderCodeList, proWorkorderStatus, finishDateBegin, finishDateEnd,productSkuList,productSkuLotWorkorderList);

    }
}
