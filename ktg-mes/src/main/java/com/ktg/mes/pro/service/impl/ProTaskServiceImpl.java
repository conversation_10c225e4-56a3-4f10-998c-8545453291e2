package com.ktg.mes.pro.service.impl;

import com.alibaba.fastjson.JSON;
import com.dt.component.common.enums.bill.AdjustBusinessTypeEnum;
import com.dt.component.common.enums.bill.AdjustDetailReasonEnum;
import com.dt.component.common.enums.bill.AdjustReasonEnum;
import com.dt.component.common.enums.bill.AdjustTypeEnum;
import com.dt.platform.wms.rpc.client.mes.adjust.AdjustAddRequest;
import com.dt.platform.wms.rpc.client.mes.adjust.AdjustAddResponse;
import com.dt.platform.wms.rpc.client.mes.adjust.AdjustDetail;
import com.dt.platform.wms.rpc.client.mes.adjust.IAdjustMesClient;
import com.dt.platform.wms.rpc.client.mes.common.Result;
import com.dt.platform.wms.rpc.client.mes.stock.IStockLocationMesQuery;
import com.dt.platform.wms.rpc.client.mes.stock.StockLocationQueryRequest;
import com.dt.platform.wms.rpc.client.mes.stock.StockLocationQueryResponse;
import com.ktg.common.constant.UserConstants;
import com.ktg.common.enums.*;
import com.ktg.common.utils.ConvertUtil;
import com.ktg.common.utils.DateUtils;
import com.ktg.common.utils.StringUtils;
import com.ktg.mes.md.domain.MdWorkshop;
import com.ktg.mes.md.service.IMdItemService;
import com.ktg.mes.md.service.IMdProductBomService;
import com.ktg.mes.md.service.IMdWorkshopService;
import com.ktg.mes.pro.domain.*;
import com.ktg.mes.pro.domain.dto.ProTaskLeftItemSubmitDTO;
import com.ktg.mes.pro.domain.req.ProTaskLeftItemSubmitReqVo;
import com.ktg.mes.pro.domain.req.ProTaskLeftSubmitReqVo;
import com.ktg.mes.pro.domain.req.ProTaskListReqVo;
import com.ktg.mes.pro.domain.req.TaskListQueryReqVo;
import com.ktg.mes.pro.domain.res.*;
import com.ktg.mes.pro.mapper.ProTaskMapper;
import com.ktg.mes.pro.service.*;
import com.ktg.mes.qc.service.IQcOqcService;
import com.ktg.mes.wm.domain.WmIssueHeader;
import com.ktg.mes.wm.domain.WmIssueLine;
import com.ktg.mes.wm.domain.tx.IssueTxBean;
import com.ktg.mes.wm.service.IWmIssueHeaderService;
import com.ktg.mes.wm.service.IWmIssueLineService;
import com.ktg.mes.wm.service.IWmRtIssueService;
import com.ktg.system.service.ISysDictDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 生产任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-05-14
 */
@Slf4j
@Service
public class ProTaskServiceImpl implements IProTaskService {
    @Autowired
    private ProTaskMapper proTaskMapper;
    @Autowired
    private IMdProductBomService iMdProductBomService;
    @Autowired
    private IProTaskBomService proTaskBomService;
    @Autowired
    private IWmIssueHeaderService wmIssueHeaderService;
    @Autowired
    private IWmIssueLineService wmIssueLineService;
    @Autowired
    private IWmRtIssueService iWmRtIssueService;
    @Autowired
    private IProFeedbackService proFeedbackService;
    @Autowired
    private IMdItemService mdItemService;
    @Autowired
    private IMdProductBomService mdProductBomService;
    @Autowired
    private IQcOqcService qcOqcService;
    @Autowired
    private IProWorkorderService proWorkorderService;
    @DubboReference
    private IAdjustMesClient adjustMesClient;
    @DubboReference
    private IStockLocationMesQuery iStockLocationMesQuery;
    @Autowired
    private IProWorkorderBomService proWorkorderBomService;
    @Autowired
    private IMdWorkshopService mdWorkshopService;
    @Autowired
    private ISysDictDataService sysDictDataService;
    @Autowired
    private IProTaskBomSkuLotRecordService proTaskBomSkuLotRecordService;
    @Autowired
    private IProTaskExceptionService proTaskExceptionService;

    /**
     * 查询生产任务
     *
     * @param taskId 生产任务主键
     * @return 生产任务
     */
    @Override
    public ProTask selectProTaskByTaskId(Long taskId) {
        return proTaskMapper.selectProTaskByTaskId(taskId);
    }

    @Override
    public List<ProTask> selectProTaskByTaskId(List<Long> taskIdList) {
        if (CollectionUtils.isEmpty(taskIdList)) {
            return new ArrayList<>();
        }
        return proTaskMapper.selectProTaskByTaskIdList(taskIdList);
    }

    /**
     * 查询生产任务列表
     *
     * @param proTask 生产任务
     * @return 生产任务
     */
    @Override
    public List<ProTask> selectProTaskList(ProTask proTask) {
        return proTaskMapper.selectProTaskList(proTask);
    }

    @Override
    public List<ProTask> proTaskPage(ProTaskListReqVo reqVo) {
        return proTaskMapper.proTaskPage(reqVo);
    }

    @Override
    public List<ProTask> selectProTaskListByWorkorderId(Long workorderId) {
        return selectProTaskListByWorkorderId(Arrays.asList(workorderId));
    }

    @Override
    public List<ProTask> selectProTaskListByWorkorderId(List<Long> workorderIdList) {
        return proTaskMapper.selectProTaskListByWorkorderId(workorderIdList);
    }

    /**
     * 查询某个工单的各个工序生产进度
     *
     * @param workorderId
     * @return
     */
    @Override
    public List<ProTask> selectProTaskProcessViewByWorkorder(Long workorderId) {
        return proTaskMapper.selectProTaskProcessViewByWorkorder(workorderId);
    }

    /**
     * 新增生产任务
     *
     * @param proTask 生产任务
     * @return 结果
     */
    @Override
    public int insertProTask(ProTask proTask) {
        proTask.setCreateTime(DateUtils.getNowDate());
        return proTaskMapper.insertProTask(proTask);
    }

    /**
     * 修改生产任务
     *
     * @param proTask 生产任务
     * @return 结果
     */
    @Override
    public int updateProTask(ProTask proTask) {
        proTask.setUpdateTime(DateUtils.getNowDate());
        return proTaskMapper.updateProTask(proTask);
    }

    /**
     * 批量删除生产任务
     *
     * @param taskIds 需要删除的生产任务主键
     * @return 结果
     */
    @Override
    public int deleteProTaskByTaskIds(Long[] taskIds) {
        return proTaskMapper.deleteProTaskByTaskIds(taskIds);
    }

    /**
     * 删除生产任务信息
     *
     * @param taskId 生产任务主键
     * @return 结果
     */
    @Override
    public int deleteProTaskByTaskId(Long taskId) {
        return proTaskMapper.deleteProTaskByTaskId(taskId);
    }

    @Override
    public ProTaskLeftViewResVo leftView(Long taskId) {
        ProTaskLeftViewResVo resVo = new ProTaskLeftViewResVo();
        ProTask proTask = this.selectProTaskByTaskId(taskId);
        if (Objects.isNull(proTask)) {
            return resVo;
        }
        Long workorderId = proTask.getWorkorderId();
        List<ProWorkorderBom> proWorkorderBoms = proWorkorderBomService.selectProWorkorderBomListByWorkorderId(workorderId);
        List<String> bomItemCodeList = proWorkorderBoms.stream().map(ProWorkorderBom::getItemCode).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(bomItemCodeList)) {
            return resVo;
        }
        List<ProFeedback> proFeedbackList = proFeedbackService.selectProFeedbackByTaskId(taskId);
        //只使用已完成的报工数据
        List<ProFeedback> finishedProFeedback = proFeedbackList.stream().filter(proFeedback -> Objects.equals(proFeedback.getStatus(), OrderStatusEnum.FINISHED.getCode())).collect(Collectors.toList());
        //查询领料单
        List<IssueTxBean> txBeans = wmIssueHeaderService.getTxBeansByTaskIdAndItemCode(taskId, bomItemCodeList);
        Map<String, List<IssueTxBean>> itemIdIssueTxBeanMap = txBeans.stream().collect(Collectors.groupingBy(IssueTxBean::getItemCode));
        //查询退料单
//        List<RtIssueTxBean> rtTxBeans = iWmRtIssueService.getTxBeansByTaskIdAndItemCode(taskId, bomItemCodeList);
//        Map<String, List<RtIssueTxBean>> itemIdRtTxBeanMap = rtTxBeans.stream().collect(Collectors.groupingBy(RtIssueTxBean::getItemCode));
        //已报工数量
        BigDecimal quantityProduced = proTask.getQuantityProduced() == null ? BigDecimal.ZERO : proTask.getQuantityProduced();
        resVo.setQuantityProduced(quantityProduced);
        List<ProFeedbackResVo> proFeedbackResVoList = new ArrayList<>();
        Map<String, BigDecimal> skuLossMap = new HashMap<>();
        for (ProFeedback proFeedback : finishedProFeedback) {
            ProFeedbackResVo proFeedbackResVo = ConvertUtil.beanConvert(proFeedback, ProFeedbackResVo.class);
            if (Objects.nonNull(proFeedback.getFeedbackType())) {
                FeedbackTypeEnum feedbackTypeEnum = FeedbackTypeEnum.getByCode(proFeedback.getFeedbackType());
                if (Objects.nonNull(feedbackTypeEnum)) {
                    proFeedbackResVo.setFeedbackTypeDesc(feedbackTypeEnum.getDesc());
                }
            }
            String issueLossDetail = proFeedback.getIssueLossDetail();
            if (StringUtils.isEmpty(issueLossDetail)) {
                proFeedbackResVo.setLossQuantity(BigDecimal.ZERO);
            } else {
                List<ProFeedbackIssueLossDetailResVo> issueLostDetailResVos = JSON.parseArray(issueLossDetail, ProFeedbackIssueLossDetailResVo.class);
                //设置明细
                BigDecimal quantityFeedback = proFeedback.getQuantityFeedback();
                BigDecimal quantity = proFeedback.getQuantity();
                Map<String, ProWorkorderBom> itemCodeBomMap = proWorkorderBoms.stream().collect(Collectors.toMap(ProWorkorderBom::getItemCode, Function.identity(), (v1, v2) -> v1));
                for (ProFeedbackIssueLossDetailResVo issueLostDetailResVo : issueLostDetailResVos) {
                    ProWorkorderBom proWorkorderBom = itemCodeBomMap.get(issueLostDetailResVo.getItemCode());
                    if (Objects.nonNull(proWorkorderBom)) {
                        BigDecimal useRatio = proWorkorderBom.getUseRatio();
                        BigDecimal productQuantity = quantity;
                        //如果保税品
                        if (Objects.equals(proFeedback.getFeedbackType(), FeedbackTypeEnum.EXTRA.getCode())) {
                            if (Objects.equals(proWorkorderBom.getTradeType(), BomGoodsTradeTypeEnum.BONDED.getCode())) {
                                //如果报工数量大于0，则用总生产数减去报工数=保税粒子实际应计算数量
                                if (quantityFeedback.compareTo(BigDecimal.ZERO) > 0) {
                                    productQuantity = quantityFeedback;
                                } else {
                                    productQuantity = BigDecimal.ZERO;
                                }
                            }
                        }
                        BigDecimal productionUsage = productQuantity.multiply(useRatio);
                        issueLostDetailResVo.setProductionUsage(productionUsage);
                        BigDecimal lossTotal = skuLossMap.getOrDefault(proWorkorderBom.getItemCode(), BigDecimal.ZERO);
                        lossTotal = lossTotal.add(issueLostDetailResVo.getQuantity());
                        skuLossMap.put(proWorkorderBom.getItemCode(), lossTotal);
                    }
                }
                proFeedbackResVo.setIssueLossDetailResVoList(issueLostDetailResVos);
                //统计总损耗
                BigDecimal totalLoss = issueLostDetailResVos.stream().map(ProFeedbackIssueLossDetailResVo::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                proFeedbackResVo.setLossQuantity(totalLoss);
            }
            proFeedbackResVoList.add(proFeedbackResVo);
        }

//        List<SysDictData> sysDictDataList = sysDictDataService.selectByType(MesDictEnums.FEEDBACK_LOSS_REASON.getDictType());
//        Map<String, String> dictMap = sysDictDataList.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));

        List<ProTaskExceptionResVo> proTaskExceptionResVos = proTaskExceptionService.selectProTaskExceptionByTaskId(taskId);
        Map<String, List<ProTaskExceptionResVo>> itemCodeExceptionMap = proTaskExceptionResVos.stream().collect(Collectors.groupingBy(ProTaskExceptionResVo::getItemCode));
        //找到额外报工的一条
        ProFeedback extraFeedback = finishedProFeedback.stream().filter(p -> Objects.equals(p.getFeedbackType(), FeedbackTypeEnum.EXTRA.getCode())).findFirst().orElse(null);
        List<ProTaskItemUsageViewResVo> proTaskItemUsageViewResVos = new ArrayList<>();
        proWorkorderBoms.forEach(b -> {
            ProTaskItemUsageViewResVo proTaskLeftViewResVo = new ProTaskItemUsageViewResVo();
            proTaskLeftViewResVo.setItemCode(b.getItemCode());
            proTaskLeftViewResVo.setItemName(b.getItemName());
            //统计总领料
            List<IssueTxBean> issueTxBeans = itemIdIssueTxBeanMap.get(b.getItemCode());
            BigDecimal itemTotalIssue;
            if (!CollectionUtils.isEmpty(issueTxBeans)) {
                itemTotalIssue = issueTxBeans.stream().map(IssueTxBean::getTransactionQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            } else {
                itemTotalIssue = BigDecimal.ZERO;
            }
            proTaskLeftViewResVo.setItemTotalIssue(itemTotalIssue);

            BigDecimal finalQuantityProduced = proTask.getQuantityProduced();
            //如果是额外报工
            if (Objects.nonNull(extraFeedback)) {
                //如果保税品
                if (Objects.equals(b.getTradeType(), BomGoodsTradeTypeEnum.BONDED.getCode())) {
                    //如果报工数量大于0，则用总生产数减去报工数=保税粒子实际应计算数量
                    if (extraFeedback.getQuantityFeedback().compareTo(BigDecimal.ZERO) > 0) {
                        finalQuantityProduced = finalQuantityProduced.subtract(extraFeedback.getQuantityFeedback());
                    }
                } else if (Objects.equals(b.getTradeType(), BomGoodsTradeTypeEnum.DUTY_PAID.getCode())) {
                    //如果是完税品
                    //如果报工数量是0，则用总生产数加上已生产数量=完税物料实际应计算数量
                    if (extraFeedback.getQuantityFeedback().compareTo(BigDecimal.ZERO) == 0) {
                        finalQuantityProduced = finalQuantityProduced.add(extraFeedback.getQuantity());
                    }
                }
            }
            BigDecimal actualUsage = finalQuantityProduced.multiply(b.getUseRatio());
            proTaskLeftViewResVo.setActualUsage(actualUsage);
            //系统总领料 - 生产使用，系统计算，不可编辑 计算结果允许为负数
            BigDecimal systemRequiredMaterialReturn = itemTotalIssue.subtract(actualUsage);
            proTaskLeftViewResVo.setSystemRequiredMaterialReturn(systemRequiredMaterialReturn);

            //取每个proFeedback任务下sku的损耗总和
            BigDecimal skuLoss = skuLossMap.getOrDefault(b.getItemCode(), BigDecimal.ZERO);
            proTaskLeftViewResVo.setProductionLoss(skuLoss);

            if (itemCodeExceptionMap.containsKey(b.getItemCode())) {
                List<ProTaskExceptionResVo> resVos = itemCodeExceptionMap.get(b.getItemCode());
                log.info("itemCodeExceptionMap resVos:{}", JSON.toJSONString(resVos));
                if (!CollectionUtils.isEmpty(resVos)) {
                    ProTaskExceptionResVo firstException = resVos.stream().filter(r -> StringUtils.isNotEmpty(r.getRemark())).findFirst().orElse(null);
                    if (Objects.nonNull(firstException)) {
                        String remark = firstException.getRemark();
//                        if (StringUtils.isEmpty(remark)) {
//                            remark = "";
//                        } else {
//                            remark += "异常原因:"+firstException.getExceptionReasonDesc() + ":" + firstException.getExceptionQuantity() + "件";
//                        }
                        proTaskLeftViewResVo.setRemark(remark);
                    }
                }

                //获取每个损耗原因的总和
                BigDecimal total = resVos.stream().map(ProTaskExceptionResVo::getExceptionQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                proTaskLeftViewResVo.setLossQuantity(total);

                // 按损耗原因分组并计算每组的损耗数量总和
                Map<String, BigDecimal> lossReasonSumMap = resVos.stream()
                        .collect(Collectors.groupingBy(
                                ProTaskExceptionResVo::getExceptionReasonDesc,
                                Collectors.reducing(
                                        BigDecimal.ZERO,
                                        ProTaskExceptionResVo::getExceptionQuantity,
                                        BigDecimal::add
                                )
                        ));
                // 将统计结果转换为字符串格式
                String lossSummary = lossReasonSumMap.entrySet().stream()
                        .map(entry -> {
                            String reason = entry.getKey();
                            BigDecimal quantity = entry.getValue();
                            // 从字典中获取原因的描述
                            return reason + ":" + quantity + "件";
                        })
                        .collect(Collectors.joining("|"));
                proTaskLeftViewResVo.setLossDetail(lossSummary);
            }
            proTaskItemUsageViewResVos.add(proTaskLeftViewResVo);
        });

        resVo.setProTaskItemUsageViewResVoList(proTaskItemUsageViewResVos);
        resVo.setProFeedbackList(proFeedbackResVoList);
        return resVo;
    }

//    public static void main(String[] args) {
//        ProTaskLeftSubmitReqVo reqVo = new ProTaskLeftSubmitReqVo();
//        reqVo.setTaskId(1L);
//        List<ProTaskLeftItemSubmitReqVo> reqVoList = new ArrayList<>();
//        ProTaskLeftItemSubmitReqVo submitReqVo = new ProTaskLeftItemSubmitReqVo();
//        submitReqVo.setNumber(1);
//        submitReqVo.setItemCode("010015723120700001-8");
//        submitReqVo.setItemName("nimm2二宝棒棒糖200g-bom-1");
//        submitReqVo.setItemTotalIssue(BigDecimal.TEN);
//        submitReqVo.setItemTotalRt(BigDecimal.ONE);
//        submitReqVo.setLineSideWarehouseLeft(BigDecimal.valueOf(0));
//        submitReqVo.setActualUsage(BigDecimal.valueOf(9));
//        submitReqVo.setTheoreticalUsage(BigDecimal.TEN);
//        submitReqVo.setLossQuantity(BigDecimal.ZERO);
//        submitReqVo.setRemark("test");
//        reqVoList.add(submitReqVo);
//
//
//        ProTaskLeftItemSubmitReqVo submitReqVo1 = new ProTaskLeftItemSubmitReqVo();
//        submitReqVo1.setNumber(1);
//        submitReqVo1.setItemCode("010015723120700001-10");
//        submitReqVo1.setItemName("nimm2二宝棒棒糖200g-bom-2");
//        submitReqVo1.setItemTotalIssue(BigDecimal.TEN);
//        submitReqVo1.setItemTotalRt(BigDecimal.ONE);
//        submitReqVo1.setLineSideWarehouseLeft(BigDecimal.valueOf(0));
//        submitReqVo1.setActualUsage(BigDecimal.valueOf(9));
//        submitReqVo1.setTheoreticalUsage(BigDecimal.TEN);
//        submitReqVo1.setLossQuantity(BigDecimal.ZERO);
//        submitReqVo1.setRemark("test1");
//        reqVoList.add(submitReqVo1);
//
//        reqVo.setReqVoList(reqVoList);
//        System.out.println(JSON.toJSONString(reqVo));
//    }

    @Override
    public void leftItemSubmitProcess(ProTaskLeftSubmitReqVo proTaskLeftSubmitReqVo) throws Exception {
        Long taskId = proTaskLeftSubmitReqVo.getTaskId();
        ProTask proTask = this.selectProTaskByTaskId(taskId);
        if (Objects.isNull(proTask)) {
            throw new Exception("生产任务为空");
        }
        if (Objects.equals(proTask.getStatus(), OrderStatusEnum.FINISHED.getCode())) {
            throw new Exception("生产任务已完成");
        }
        BigDecimal requestQuantityProduced = proTaskLeftSubmitReqVo.getQuantityProduced();
        BigDecimal taskQuantityProduced = proTask.getQuantityProduced();
        if (requestQuantityProduced.compareTo(taskQuantityProduced) != 0) {
            throw new Exception("任务已报工数量发生变化,请重新发起余料清点");
        }
        List<ProTaskLeftItemSubmitReqVo> reqVoList = proTaskLeftSubmitReqVo.getReqVoList();
        if (CollectionUtils.isEmpty(reqVoList)) {
            throw new Exception("提交数据为空");
        }

        int i = 1;
        for (ProTaskLeftItemSubmitReqVo proTaskLeftItemSubmitReqVo : reqVoList) {
            BigDecimal lineSideWarehouseLeft = proTaskLeftItemSubmitReqVo.getLineSideWarehouseLeft();
            BigDecimal lossQuantity = proTaskLeftItemSubmitReqVo.getLossQuantity();
            if (Objects.nonNull(lossQuantity) && lineSideWarehouseLeft.compareTo(lossQuantity) < 0) {
                throw new Exception("第" + (i) + "条物料线边库实际剩余小于异常物料值，请检查！");
            }
            i++;
        }

        //转换为dto
        List<ProTaskLeftItemSubmitDTO> proTaskLeftItemSubmitDTOS = reqVoList
                .stream()
                .map(req -> ConvertUtil.beanConvert(req, ProTaskLeftItemSubmitDTO.class))
                .collect(Collectors.toList());

        ProWorkorder proWorkorder = proWorkorderService.selectProWorkorderByWorkorderId(proTask.getWorkorderId());
        if (Objects.isNull(proWorkorder)) {
            throw new Exception("工单为空");
        }
        if (Objects.equals(proWorkorder.getStatus(), OrderStatusEnum.FINISHED.getCode())) {
            throw new Exception("工单已完成");
        }
        //过滤实际使用量为空的数据
        Map<String, ProTaskLeftItemSubmitDTO> itemCodeSubmitMap = proTaskLeftItemSubmitDTOS.stream()
                .filter(r -> Objects.nonNull(r.getActualUsage()) && r.getActualUsage().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toMap(ProTaskLeftItemSubmitDTO::getItemCode, Function.identity(), (v1, v2) -> v1));

        List<ProWorkorderBom> proWorkorderBoms = proWorkorderBomService.selectProWorkorderBomListByWorkorderId(proWorkorder.getWorkorderId());
//        List<String> bomItemCodeList = proWorkorderBoms.stream().map(ProWorkorderBom::getItemCode).distinct().collect(Collectors.toList());
        //查询领料单
        List<IssueTxBean> issueTxBeans = wmIssueHeaderService.getTxBeansByTaskId(taskId);
        if (CollectionUtils.isEmpty(issueTxBeans)) {
            throw new Exception("未查询到已完成的领料单");
        }
        List<String> skuLotNoList = issueTxBeans.stream().map(IssueTxBean::getSkuLotNo).distinct().collect(Collectors.toList());
        Map<String, List<IssueTxBean>> skuLotMap = issueTxBeans.stream().collect(Collectors.groupingBy(IssueTxBean::getSkuLotNo));
        Map<String, BigDecimal> skuLotNoAvailableQtyMap = new HashMap<>();
        for (Map.Entry<String, List<IssueTxBean>> skuLotEntry : skuLotMap.entrySet()) {
            String key = skuLotEntry.getKey();
            List<IssueTxBean> value = skuLotEntry.getValue();
            if (CollectionUtils.isEmpty(value)) {
                skuLotNoAvailableQtyMap.put(key, BigDecimal.ZERO);
            }
            BigDecimal availableQty = value.stream().map(IssueTxBean::getTransactionQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            skuLotNoAvailableQtyMap.put(key, availableQty);
        }
        Long workshopId = proTask.getWorkshopId();
        MdWorkshop mdWorkshop = mdWorkshopService.selectMdWorkshopByWorkshopId(workshopId);

        List<ProTaskItemSkuSelectDTO> selectedList = new ArrayList<>();

        Map<String, String> itemCodeLossSkuLotNoMap = new HashMap<>();
        List<StockLocationQueryResponse> responseList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(itemCodeSubmitMap)) {
            StockLocationQueryRequest queryRequest = new StockLocationQueryRequest();
            queryRequest.setWarehouseCode(proWorkorder.getWarehouseCode());
            queryRequest.setCargoCode(proWorkorder.getOwnerCode());
            queryRequest.setSkuCodeList(proWorkorderBoms.stream().map(ProWorkorderBom::getItemCode).distinct().collect(Collectors.toList()));
            queryRequest.setSkuLotNoList(skuLotNoList);
            queryRequest.setLocationCode(mdWorkshop.getAvlLineSideLocationCode());
            log.info("查询库存信息请求参数：{}", JSON.toJSONString(queryRequest));
            responseList = iStockLocationMesQuery.list(queryRequest);
            log.info("库存查询结果：{}", JSON.toJSONString(responseList));
            responseList = responseList.stream()
                    .filter(r -> Objects.nonNull(r.getZoneAttr()) || StringUtils.isEmpty(r.getZoneAttr()))
                    .sorted(Comparator.comparing(StockLocationQueryResponse::getReceiveDate))
                    .collect(Collectors.toList());
            responseList.forEach(r -> {
                BigDecimal avlNum = skuLotNoAvailableQtyMap.get(r.getSkuLotNo());

                //将StockLocationQueryResponse.AvailableQty设置为StockLocationQueryResponse.AvailableQty 与 issueTxBean.getTransactionQuantity中较小的数字
                BigDecimal realAvailableQty = r.getAvailableQty().min(avlNum);
                log.info("realAvailableQty={} available={} issue={}", realAvailableQty, r.getAvailableQty(), avlNum);
                r.setAvailableQty(realAvailableQty);

            });
            Map<String, List<StockLocationQueryResponse>> itemCodeMap = responseList.stream().collect(Collectors.groupingBy(StockLocationQueryResponse::getSkuCode));


            for (ProWorkorderBom proWorkorderBom : proWorkorderBoms) {
                String itemCode = proWorkorderBom.getItemCode();
                ProTaskLeftItemSubmitDTO proTaskLeftItemSubmitDTO = itemCodeSubmitMap.get(itemCode);
                if (Objects.isNull(proTaskLeftItemSubmitDTO)) {
                    continue;
                }
                BigDecimal actualUsage = proTaskLeftItemSubmitDTO.getActualUsage();
                BigDecimal productionLoss = proTaskLeftItemSubmitDTO.getProductionLoss();
                log.info("itemCode={} actualUsage={} productionLoss={}", itemCode, actualUsage, productionLoss);
                if (itemCodeMap.containsKey(itemCode)) {
                    List<StockLocationQueryResponse> itemResponseList = itemCodeMap.get(itemCode);
                    if (!CollectionUtils.isEmpty(itemResponseList)) {
                        if (Objects.nonNull(productionLoss) && productionLoss.compareTo(BigDecimal.ZERO) > 0) {
                            //找到可有库存最多的库存批次id
                            StockLocationQueryResponse maxStockItem = itemResponseList.stream()
                                    .max(Comparator.comparing(StockLocationQueryResponse::getAvailableQty))
                                    .get();
                            log.info("sku:{} 最大库存量的批次id：{}", itemCode, maxStockItem.getSkuLotNo());
                            String skuLotNo = maxStockItem.getSkuLotNo();
                            // 使用skuLotNo进行后续操作
                            itemCodeLossSkuLotNoMap.put(itemCode, skuLotNo);
                        }
                        for (StockLocationQueryResponse r : itemResponseList) {
                            BigDecimal availableQty = r.getAvailableQty();
                            int compared = actualUsage.compareTo(availableQty);
                            if (compared <= 0) {
                                // 如果实际使用小于等于可用，直接使用该批次
                                log.info("生产使用 sku:{} 使用批次id：{} 实际使用小于等于可用，直接使用该批次", itemCode, r.getSkuLotNo());
                                ProTaskItemSkuSelectDTO selectDTO = new ProTaskItemSkuSelectDTO();
                                selectDTO.setSkuCode(r.getSkuCode());
                                selectDTO.setSkuLotNo(r.getSkuLotNo());
                                selectDTO.setAdjustQty(actualUsage);
                                selectDTO.setLocationCode(r.getLocationCode());
                                selectDTO.setExternalLinkBillNo(r.getExternalLinkBillNo());
                                selectDTO.setType(ProTaskAdjustTypeEnum.PRODUCTION_USAGE.getCode());
                                selectDTO.setReceiveDate(new Date(r.getReceiveDate()));
                                selectedList.add(selectDTO);
                                availableQty = availableQty.subtract(actualUsage);
                                log.info("itemCode:{} 批次id:{} 剩余可用库存：{}", itemCode, r.getSkuLotNo(), availableQty);
                                r.setAvailableQty(availableQty);
                                actualUsage = BigDecimal.ZERO;
                                break;
                            } else {
                                // 实际使用大于可用，则使用可用库存，并继续使用下一批次
                                ProTaskItemSkuSelectDTO selectDTO = new ProTaskItemSkuSelectDTO();
                                selectDTO.setSkuCode(r.getSkuCode());
                                selectDTO.setSkuLotNo(r.getSkuLotNo());
                                selectDTO.setAdjustQty(r.getAvailableQty());
                                selectDTO.setLocationCode(r.getLocationCode());
                                selectDTO.setExternalLinkBillNo(r.getExternalLinkBillNo());
                                selectDTO.setType(ProTaskAdjustTypeEnum.PRODUCTION_USAGE.getCode());
                                selectDTO.setReceiveDate(new Date(r.getReceiveDate()));
                                selectedList.add(selectDTO);
                                actualUsage = actualUsage.subtract(availableQty);
                                r.setAvailableQty(BigDecimal.ZERO);
                            }
                        }
                        if (actualUsage.compareTo(BigDecimal.ZERO) > 0) {
                            log.info("生产使用可用库存批次不足");
                            throw new Exception("可用库存批次不足");
                        }
                        if (productionLoss.compareTo(BigDecimal.ZERO) > 0) {
                            BigDecimal originalProductionLoss = productionLoss; // 记录原始损耗数量
                            BigDecimal actualDeductedLoss = BigDecimal.ZERO; // 记录实际扣减的损耗数量

                            for (StockLocationQueryResponse r : itemResponseList) {
                                BigDecimal availableQty = r.getAvailableQty();
                                if (availableQty.compareTo(BigDecimal.ZERO) == 0) {
                                    continue;
                                }
                                int compared = productionLoss.compareTo(availableQty);
                                if (compared <= 0) {
                                    // 如果实际使用小于等于可用，直接使用该批次
                                    log.info("生产损耗 sku:{} 使用批次id：{} 实际使用小于等于可用，直接使用该批次", itemCode, r.getSkuLotNo());
                                    ProTaskItemSkuSelectDTO selectDTO = new ProTaskItemSkuSelectDTO();
                                    selectDTO.setSkuCode(r.getSkuCode());
                                    selectDTO.setSkuLotNo(r.getSkuLotNo());
                                    selectDTO.setAdjustQty(productionLoss);
                                    selectDTO.setLocationCode(r.getLocationCode());
                                    selectDTO.setExternalLinkBillNo(r.getExternalLinkBillNo());
                                    selectDTO.setType(ProTaskAdjustTypeEnum.PRODUCTION_LOSS.getCode());
                                    selectDTO.setReceiveDate(new Date(r.getReceiveDate()));
                                    selectedList.add(selectDTO);
                                    actualDeductedLoss = actualDeductedLoss.add(productionLoss);
                                    productionLoss = BigDecimal.ZERO;
                                    break;
                                } else {
                                    // 实际使用大于可用，则使用可用库存，并继续使用下一批次
                                    ProTaskItemSkuSelectDTO selectDTO = new ProTaskItemSkuSelectDTO();
                                    selectDTO.setSkuCode(r.getSkuCode());
                                    selectDTO.setSkuLotNo(r.getSkuLotNo());
                                    selectDTO.setAdjustQty(r.getAvailableQty());
                                    selectDTO.setLocationCode(r.getLocationCode());
                                    selectDTO.setExternalLinkBillNo(r.getExternalLinkBillNo());
                                    selectDTO.setType(ProTaskAdjustTypeEnum.PRODUCTION_LOSS.getCode());
                                    selectDTO.setReceiveDate(new Date(r.getReceiveDate()));
                                    selectedList.add(selectDTO);
                                    actualDeductedLoss = actualDeductedLoss.add(r.getAvailableQty());
                                    productionLoss = productionLoss.subtract(availableQty);
                                }
                            }
                            if(Objects.equals(proWorkorderBom.getTradeType(),BomGoodsTradeTypeEnum.DUTY_PAID.getCode())){
                                if (productionLoss.compareTo(BigDecimal.ZERO) > 0) {
                                    log.info("生产损耗可用库存批次不足");
                                    throw new Exception("sku:"+proWorkorderBom.getItemCode()+"扣减生产损耗可用库存批次不足");
                                }
                            }

                            //设置实际扣减的损耗数量
                            proTaskLeftItemSubmitDTO.setActualDeductedLoss(actualDeductedLoss);

                            // 记录扣减结果
                            log.info("生产损耗扣减完成 - sku:{} 原始损耗数量:{} 实际扣减数量:{}", itemCode, originalProductionLoss, actualDeductedLoss);

                            if (productionLoss.compareTo(BigDecimal.ZERO) > 0) {
                                // 超出部分不进行扣减，仅记录日志，不抛出异常
                                BigDecimal undeductedLoss = productionLoss;
                                log.info("生产损耗库存不足，无法完全扣减 - sku:{} 原始损耗数量:{} 实际扣减数量:{} 未扣减数量:{}",
                                        itemCode, originalProductionLoss, actualDeductedLoss, undeductedLoss);
                                proTaskLeftItemSubmitDTO.setUndeductedLoss(undeductedLoss);
                            }
                        } else {
                            log.info("生产损耗为0 不进行扣减");
                        }
                    } else {
                        throw new Exception("可用库存不足");
                    }
                }
            }
            log.info("已选择批次信息 selectedList={}", JSON.toJSONString(selectedList));
            Map<String, List<ProTaskItemSkuSelectDTO>> skuLotNoMap = selectedList.stream().collect(Collectors.groupingBy(ProTaskItemSkuSelectDTO::getSkuLotNo));
            // 生产任务 - 【完结】：MES手工创建，与库存调整(调减)1:1关系；对象为原材料调减
            AdjustAddRequest adjustAddRequest = new AdjustAddRequest();
            adjustAddRequest.setWarehouseCode(proWorkorder.getWarehouseCode());
            adjustAddRequest.setCargoCode(proWorkorder.getOwnerCode());
            adjustAddRequest.setReason(AdjustReasonEnum.MES.getCode());
            adjustAddRequest.setType(AdjustTypeEnum.SUBTRACT.getStatus());
            adjustAddRequest.setBusinessType(AdjustBusinessTypeEnum.MES_RAW_DEDUCT.getCode());
            adjustAddRequest.setBillNo(proTask.getTaskCode());
            adjustAddRequest.setNote(proTask.getTaskCode());

            List<AdjustDetail> adjustDetailList = new ArrayList<>();
            skuLotNoMap.keySet().forEach(s -> {
                List<ProTaskItemSkuSelectDTO> proTaskItemSkuSelectDTOS = skuLotNoMap.get(s);
                ProTaskItemSkuSelectDTO selectDTO = proTaskItemSkuSelectDTOS.get(0);
                AdjustDetail adjustDetail = new AdjustDetail();
                adjustDetail.setSkuCode(selectDTO.getSkuCode());
                adjustDetail.setSkuLotNo(selectDTO.getSkuLotNo());
                adjustDetail.setLocationCode(selectDTO.getLocationCode());
                adjustDetail.setReason(AdjustDetailReasonEnum.MES_DEDUCT.getCode());
                if (proTaskItemSkuSelectDTOS.size() > 1) {
                    BigDecimal totalAdjustQty = proTaskItemSkuSelectDTOS.stream().map(ProTaskItemSkuSelectDTO::getAdjustQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    adjustDetail.setAdjustQty(totalAdjustQty);
                    String remark = proTaskItemSkuSelectDTOS
                            .stream()
                            .map(skuSelectDTO -> {
                                ProTaskAdjustTypeEnum adjustTypeEnum = ProTaskAdjustTypeEnum.getByCode(skuSelectDTO.getType());
                                if (Objects.isNull(adjustTypeEnum)) {
                                    return null;
                                }
                                return adjustTypeEnum.getType() + ":" + skuSelectDTO.getAdjustQty();
                            }).filter(Objects::nonNull)
                            .collect(Collectors.joining(";"));
                    adjustDetail.setRemark(remark);
                } else {
                    adjustDetail.setAdjustQty(selectDTO.getAdjustQty());
                    ProTaskAdjustTypeEnum adjustTypeEnum = ProTaskAdjustTypeEnum.getByCode(selectDTO.getType());
                    if (Objects.nonNull(adjustTypeEnum)) {
                        String remark = adjustTypeEnum.getType() + ":" + selectDTO.getAdjustQty();
                        adjustDetail.setRemark(remark);
                    }
                }
                adjustDetailList.add(adjustDetail);
            });

//            adjustDetailList = selectedList.stream().map(bom -> {
//                AdjustDetail adjustDetail = new AdjustDetail();
//                adjustDetail.setSkuCode(bom.getSkuCode());
//                adjustDetail.setSkuLotNo(bom.getSkuLotNo());
//                adjustDetail.setAdjustQty(bom.getAdjustQty());
//                adjustDetail.setLocationCode(bom.getLocationCode());
//                adjustDetail.setReason(AdjustDetailReasonEnum.MES_DEDUCT.getCode());
//                return adjustDetail;
//            }).collect(Collectors.toList());
            adjustAddRequest.setDetailList(adjustDetailList);
            log.info("生成库存调整单 req={}", JSON.toJSONString(adjustDetailList));
            Result<AdjustAddResponse> result = adjustMesClient.add(adjustAddRequest);
            log.info("调用wms接口返回 result={}", JSON.toJSONString(result));
            if (result.checkSuccess()) {
                proTask.setStatus(UserConstants.ORDER_STATUS_FINISHED);
                proTask.setWmsOrderNo(result.getData().getCode());
                proTask.setEndTime(new Date());
                proTaskMapper.updateProTask(proTask);
            } else {
                throw new Exception(result.getMessage());
            }
        } else {
            log.info("所有物料实际使用都是0 任务完成 taskId:{}", taskId);
            proTask.setStatus(UserConstants.ORDER_STATUS_FINISHED);
            proTask.setEndTime(new Date());
            proTaskMapper.updateProTask(proTask);
            return;
        }

        //生成任务bom
        this.generateTaskBomList(proTaskLeftItemSubmitDTOS, selectedList, itemCodeLossSkuLotNoMap, proTask, responseList);

        //生成出货检测单
        try {
            qcOqcService.buildQcOqcByProTask(proWorkorder, proTask);
        } catch (Exception e) {
            log.error("生成出货检测单失败 error={}", e.getMessage(), e);
        }
    }

    @Override
    public List<ProTaskCodeIdResVo> listProTaskByWorkorderId(Long workorderId) {
        List<ProTask> proTaskList = proTaskMapper.selectProTaskListByWorkorderId(Arrays.asList(workorderId));
        return proTaskList.stream().map(proTask -> {
            ProTaskCodeIdResVo resVo = new ProTaskCodeIdResVo();
            resVo.setTaskId(proTask.getTaskId());
            resVo.setTaskCode(proTask.getTaskCode());
            resVo.setProduceBatchCode(proTask.getProduceBatchCode());
            resVo.setItemCode(proTask.getItemCode());
            resVo.setItemName(proTask.getItemName());
            resVo.setWorkshopName(proTask.getWorkshopName());
            resVo.setQuantityProduced(proTask.getQuantityProduced());
            resVo.setEndTime(proTask.getEndTime());
            return resVo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ProTaskCodeIdResVo> listNotFinishProTaskByWorkorderId(Long workorderId) {
        List<ProTask> proTaskList = proTaskMapper.selectProTaskListByWorkorderId(Arrays.asList(workorderId));
        proTaskList = proTaskList.stream().filter(proTask -> !Objects.equals(proTask.getStatus(), UserConstants.ORDER_STATUS_FINISHED)).collect(Collectors.toList());
        return proTaskList.stream().map(proTask -> {
            ProTaskCodeIdResVo resVo = new ProTaskCodeIdResVo();
            resVo.setTaskId(proTask.getTaskId());
            resVo.setTaskCode(proTask.getTaskCode());
            resVo.setProduceBatchCode(proTask.getProduceBatchCode());
            resVo.setItemCode(proTask.getItemCode());
            resVo.setItemName(proTask.getItemName());
            resVo.setWorkshopName(proTask.getWorkshopName());
            resVo.setQuantityProduced(proTask.getQuantityProduced());
            resVo.setEndTime(proTask.getEndTime());
            return resVo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ProTaskCodeIdResVo> listProTaskByWorkorderIdAndStatus(TaskListQueryReqVo reqVo) {
        if (Objects.nonNull(reqVo.getStatus())) {
            if (reqVo.getStatus().contains(",")) {
                reqVo.setStatusList(Arrays.asList(reqVo.getStatus().split(",")));
                reqVo.setStatus(null);
            }
        }
        if (Objects.nonNull(reqVo.getExcludeStatus())) {
            if (reqVo.getExcludeStatus().contains(",")) {
                reqVo.setExcludeStatusList(Arrays.asList(reqVo.getExcludeStatus().split(",")));
                reqVo.setExcludeStatus(null);
            }
        }
        List<ProTask> proTaskList = proTaskMapper.selectProTaskListByWorkorderIdAndStatus(reqVo);
        return proTaskList.stream().map(proTask -> {
            ProTaskCodeIdResVo resVo = new ProTaskCodeIdResVo();
            resVo.setTaskId(proTask.getTaskId());
            resVo.setTaskCode(proTask.getTaskCode());
            resVo.setProduceBatchCode(proTask.getProduceBatchCode());
            resVo.setItemCode(proTask.getItemCode());
            resVo.setItemName(proTask.getItemName());
            resVo.setWorkshopName(proTask.getWorkshopName());
            resVo.setQuantityProduced(proTask.getQuantityProduced());
            resVo.setEndTime(proTask.getEndTime());
            return resVo;
        }).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    public void generateTaskBomList(List<ProTaskLeftItemSubmitDTO> taskLeftItemSubmitDTOList, List<ProTaskItemSkuSelectDTO> adjustDetailList, Map<String, String> itemCodeLossSkuLotNoMap, ProTask proTask, List<StockLocationQueryResponse> responseList) {
        log.info("生成任务bom taskLeftItemSubmitDTOList={} adjustDetailList={} itemCodeLossSkuLotNoMap={}", JSON.toJSONString(taskLeftItemSubmitDTOList), JSON.toJSONString(adjustDetailList), JSON.toJSONString(itemCodeLossSkuLotNoMap));
        Map<String, List<ProTaskItemSkuSelectDTO>> skuAdjustDetailMap = adjustDetailList.stream().collect(Collectors.groupingBy(ProTaskItemSkuSelectDTO::getSkuCode));
        Map<String, List<StockLocationQueryResponse>> skuStockLocationQueryMap = responseList.stream().collect(Collectors.groupingBy(StockLocationQueryResponse::getSkuCode));
        taskLeftItemSubmitDTOList.forEach(req -> {
            ProTaskBom proTaskBom = new ProTaskBom();
            proTaskBom.setWorkorderId(proTask.getWorkorderId());
            proTaskBom.setTaskId(proTask.getTaskId());
            proTaskBom.setItemCode(req.getItemCode());
            proTaskBom.setItemName(req.getItemName());
            proTaskBom.setActualUsage(req.getActualUsage() == null ? BigDecimal.ZERO : req.getActualUsage());
            proTaskBom.setLineSideWarehouseLeft(req.getLineSideWarehouseLeft() == null ? BigDecimal.ZERO : req.getLineSideWarehouseLeft());
            proTaskBom.setTheoreticalUsage(req.getTheoreticalUsage() == null ? BigDecimal.ZERO : req.getTheoreticalUsage());
            proTaskBom.setItemTotalIssue(req.getItemTotalIssue() == null ? BigDecimal.ZERO : req.getItemTotalIssue());
//            proTaskBom.setItemTotalRt(req.getItemTotalRt());
            proTaskBom.setProductionLoss(req.getProductionLoss() == null ? BigDecimal.ZERO : req.getProductionLoss());
            proTaskBom.setLossQuantity(req.getLossQuantity() == null ? BigDecimal.ZERO : req.getLossQuantity());
            //系统应还料计算方式为：系统总领料-生产使用-实际损耗，若计算结果为负数则取值为0。
            BigDecimal systemRequiredMaterialReturn = proTaskBom.getItemTotalIssue().subtract(proTaskBom.getActualUsage()).subtract(proTaskBom.getProductionLoss());
            if (systemRequiredMaterialReturn.compareTo(BigDecimal.ZERO) < 0) {
                systemRequiredMaterialReturn = BigDecimal.ZERO;
            }
            proTaskBom.setSystemRequiredMaterialReturn(systemRequiredMaterialReturn);
            //生产使用＋线边库实际剩余＋生产损耗 - 系统总领料 计算结果允许为负数
            BigDecimal excessShortQuantity = proTaskBom.getActualUsage().add(proTaskBom.getLineSideWarehouseLeft()).add(proTaskBom.getProductionLoss()).subtract(proTaskBom.getItemTotalIssue());
            proTaskBom.setExcessShortQuantity(excessShortQuantity);
            log.info("excessShortQuantity={}", excessShortQuantity);

            BigDecimal actualDeductedLoss = req.getActualDeductedLoss();
            //系统损耗=实际扣减损耗
            proTaskBom.setSystemDisplayLoss(actualDeductedLoss);


//            BigDecimal lossQuantity = req.getActualUsage().subtract(req.getTheoreticalUsage());
//            if (lossQuantity.compareTo(BigDecimal.ZERO) <= 0) {
//                lossQuantity = BigDecimal.ZERO;
//            }
//            BigDecimal lossQuantity = req.getLossQuantity();
//            if (Objects.isNull(lossQuantity)) {
//                lossQuantity = BigDecimal.ZERO;
//            }
//            proTaskBom.setLossQuantity(lossQuantity);
            BigDecimal productionLoss = req.getProductionLoss();
            if (Objects.isNull(productionLoss)) {
                productionLoss = BigDecimal.ZERO;
            }
            proTaskBom.setProductionLoss(productionLoss);
            //损耗量/理论使用量，百分比展示，保留两位小数
//            BigDecimal lossPercent = lossQuantity.divide(req.getTheoreticalUsage(), 2, RoundingMode.HALF_UP);
//            proTaskBom.setLossPercentage(lossPercent);
            proTaskBom.setLossDetail(req.getLossDetail());
            proTaskBom.setRemark(req.getRemark());
            List<ProTaskItemSkuSelectDTO> detailList = skuAdjustDetailMap.get(req.getItemCode());
            if (!CollectionUtils.isEmpty(detailList)) {
                List<ProTaskBomAdjustDetailDTO> detailDTOList = ConvertUtil.listConvert(detailList, ProTaskBomAdjustDetailDTO.class);
                proTaskBom.setAdjustDetailList(JSON.toJSONString(detailDTOList));
            }
            String lossSkuLotNo = itemCodeLossSkuLotNoMap.get(req.getItemCode());
            if (Objects.nonNull(lossSkuLotNo)) {
                proTaskBom.setLossSkuLotNo(lossSkuLotNo);
            }
            log.info("生成任务bom proTaskBom={}", JSON.toJSONString(proTaskBom));
            proTaskBomService.insertProTaskBom(proTaskBom);
            List<StockLocationQueryResponse> queryResponses = skuStockLocationQueryMap.get(proTaskBom.getItemCode());
            if(!CollectionUtils.isEmpty(queryResponses)){
                // 记录对应批次信息
                recordSkuLotInfo(proTask, req, detailList, queryResponses);
            }

        });
    }

    /**
     * 记录批次信息到ProTaskBomSkuLotRecord数据库
     *
     * @param proTask                  生产任务
     * @param proTaskLeftItemSubmitDTO 物料提交信息
     * @param adjustDetailList         调整明细列表
     * @param itemCodeLossSkuLotNoMap  物料损耗批次映射
     * @param queryResponses
     */
    private void recordSkuLotInfo(ProTask proTask, ProTaskLeftItemSubmitDTO proTaskLeftItemSubmitDTO,
                                  List<ProTaskItemSkuSelectDTO> adjustDetailList,
                                  List<StockLocationQueryResponse> queryResponses) {
        String itemCode = proTaskLeftItemSubmitDTO.getItemCode();
        String itemName = proTaskLeftItemSubmitDTO.getItemName();
        BigDecimal excessShortQuantity = proTaskLeftItemSubmitDTO.getExcessShortQuantity();

        List<WmIssueHeader> wmIssueHeaders = wmIssueHeaderService.selectWmIssueHeaderByTaskId(proTask.getTaskId());
        List<Long> finishedIssueList = wmIssueHeaders.stream()
                .filter(w -> Objects.equals(w.getStatus(), OrderStatusEnum.FINISHED.getCode()))
                .map(WmIssueHeader::getIssueId).distinct()
                .collect(Collectors.toList());
        List<WmIssueLine> wmIssueLineList = wmIssueLineService.selectWmIssueLineByIssueId(finishedIssueList);
        Map<String, List<WmIssueLine>> skuLotIssueLineMap = wmIssueLineList.stream().collect(Collectors.groupingBy(WmIssueLine::getSkuLotNo));

        log.info("开始记录批次信息 - itemCode:{} excessShortQuantity:{}", itemCode, excessShortQuantity);

        // 1. 将queryResponses按照入库时间排序（如果入库时间相同，则按照可用库存从小到大排）
        List<StockLocationQueryResponse> sortedOriginQueryResponses = sortQueryResponsesByReceiveDate(queryResponses);
        log.info("排序后的库存数据 - itemCode:{} sortedCount:{}", itemCode, sortedOriginQueryResponses.size());

        Map<String, BigDecimal> skuLotNoAvailableQtyMap = new HashMap<>();
        // 3.1 标记少到货批次：优先记录
        if (excessShortQuantity.compareTo(BigDecimal.ZERO) < 0) {
            recordShortArrivalLots(proTask, proTaskLeftItemSubmitDTO, skuLotIssueLineMap, adjustDetailList, sortedOriginQueryResponses, skuLotNoAvailableQtyMap);
        }

        // 3.2 标记多到货批次：优先记录
        if (excessShortQuantity.compareTo(BigDecimal.ZERO) > 0) {
            recordExcessArrivalLots(proTask, proTaskLeftItemSubmitDTO, excessShortQuantity,
                    sortedOriginQueryResponses);
        }

        // 3.3 标记生产异常
        recordProductionExceptionLots(proTask, proTaskLeftItemSubmitDTO, skuLotIssueLineMap, adjustDetailList, sortedOriginQueryResponses, skuLotNoAvailableQtyMap);
    }

    /**
     * 过滤掉queryResponses中已经被adjustDetailList使用的数据
     * 扣减逻辑：如果queryResponses中批次id存在于adjustDetailList，则扣除对应ProTaskItemSkuSelectDTO使用的数量
     *
     * @param queryResponses   原始库存查询结果
     * @param adjustDetailList 调整明细列表
     * @return 过滤后的库存查询结果
     */
    private List<StockLocationQueryResponse> filterUsedStock(List<StockLocationQueryResponse> queryResponses,
                                                             List<ProTaskItemSkuSelectDTO> adjustDetailList) {
        if (CollectionUtils.isEmpty(queryResponses) || CollectionUtils.isEmpty(adjustDetailList)) {
            return queryResponses;
        }

        // 按批次ID分组计算已使用数量
        Map<String, BigDecimal> usedQuantityMap = adjustDetailList.stream()
                .collect(Collectors.groupingBy(
                        ProTaskItemSkuSelectDTO::getSkuLotNo,
                        Collectors.reducing(BigDecimal.ZERO,
                                ProTaskItemSkuSelectDTO::getAdjustQty,
                                BigDecimal::add)
                ));

        List<StockLocationQueryResponse> filteredResponses = new ArrayList<>();

        for (StockLocationQueryResponse response : queryResponses) {
            String skuLotNo = response.getSkuLotNo();
            BigDecimal availableQty = response.getAvailableQty();
            BigDecimal usedQty = usedQuantityMap.getOrDefault(skuLotNo, BigDecimal.ZERO);

            // 计算剩余可用数量
            BigDecimal remainingQty = availableQty.subtract(usedQty);

            if (remainingQty.compareTo(BigDecimal.ZERO) > 0) {
                // 创建新的响应对象，更新可用数量
                StockLocationQueryResponse filteredResponse = new StockLocationQueryResponse();
                // 复制原有属性
                filteredResponse.setSkuLotNo(response.getSkuLotNo());
                filteredResponse.setSkuCode(response.getSkuCode());
                filteredResponse.setReceiveDate(response.getReceiveDate());
                filteredResponse.setAvailableQty(remainingQty);
                filteredResponse.setSkuQuality(response.getSkuQuality());

                filteredResponses.add(filteredResponse);
            }
        }

        return filteredResponses;
    }

    /**
     * 将queryResponses按照入库时间排序（如果入库时间相同，则按照可用库存从小到大排）
     *
     * @param queryResponses 库存查询结果
     * @return 排序后的库存查询结果
     */
    private List<StockLocationQueryResponse> sortQueryResponsesByReceiveDate(List<StockLocationQueryResponse> queryResponses) {
        if (CollectionUtils.isEmpty(queryResponses)) {
            return queryResponses;
        }

        return queryResponses.stream()
                .sorted((r1, r2) -> {
                    Long date1 = r1.getReceiveDate();
                    Long date2 = r2.getReceiveDate();

                    // 按入库时间从近到远排序
                    if (date1 == null && date2 == null) {
                        // 如果入库时间都为空，按可用库存从小到大排序
                        return r1.getAvailableQty().compareTo(r2.getAvailableQty());
                    }
                    if (date1 == null) return 1;
                    if (date2 == null) return -1;

                    int dateCompare = date2.compareTo(date1); // 从近到远
                    if (dateCompare != 0) {
                        return dateCompare;
                    }

                    // 如果入库时间相同，按可用库存从小到大排序
                    return r1.getAvailableQty().compareTo(r2.getAvailableQty());
                })
                .collect(Collectors.toList());
    }

    /**
     * 记录少到货批次
     */
    private void recordShortArrivalLots(ProTask proTask,
                                        ProTaskLeftItemSubmitDTO req,
                                        Map<String, List<WmIssueLine>> skuLotIssueLineMap, List<ProTaskItemSkuSelectDTO> adjustDetailList,
                                        List<StockLocationQueryResponse> queryResponses, Map<String, BigDecimal> skuLotNoAvailableQtyMap) {
        String itemCode = req.getItemCode();
        String itemName = req.getItemName();
        BigDecimal shortQuantity = req.getExcessShortQuantity().abs();
        log.info("开始记录少到货批次 - itemCode:{} shortQuantity:{}", itemCode, shortQuantity);

        Map<String, List<ProTaskItemSkuSelectDTO>> skuLotNoMap = adjustDetailList.stream().collect(Collectors.groupingBy(ProTaskItemSkuSelectDTO::getSkuLotNo));
        BigDecimal remainingShortQuantity = shortQuantity;

        for (StockLocationQueryResponse stockLocationQueryResponse : queryResponses) {
            if (remainingShortQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }

            String skuLotNo = stockLocationQueryResponse.getSkuLotNo();
            List<ProTaskItemSkuSelectDTO> lotDetails = skuLotNoMap.get(skuLotNo);
            BigDecimal totalIssue = BigDecimal.ZERO;

            // 计算该批次的最大可承担少货数量：领料单数量 - 系统生产使用 - 系统生产消耗
            List<WmIssueLine> wmIssueLineList = skuLotIssueLineMap.get(skuLotNo);
            if (!CollectionUtils.isEmpty(wmIssueLineList)) {
                totalIssue = wmIssueLineList.stream().map(WmIssueLine::getQuantityIssued).reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            BigDecimal actualUsage = BigDecimal.ZERO;
            BigDecimal productionLoss = BigDecimal.ZERO;
            if (!CollectionUtils.isEmpty(lotDetails)) {
                actualUsage = lotDetails.stream()
                        .filter(d -> ProTaskAdjustTypeEnum.PRODUCTION_USAGE.getCode().equals(d.getType()))
                        .map(ProTaskItemSkuSelectDTO::getAdjustQty)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                productionLoss = lotDetails.stream()
                        .filter(d -> ProTaskAdjustTypeEnum.PRODUCTION_LOSS.getCode().equals(d.getType()))
                        .map(ProTaskItemSkuSelectDTO::getAdjustQty)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            // 该批次的最大可承担少货数量
            BigDecimal maxShortCapacity = totalIssue.subtract(actualUsage).subtract(productionLoss);
            BigDecimal allocatedShortQuantity = remainingShortQuantity.min(maxShortCapacity);
            log.info("recordShortArrivalLots totalIssue:{} actualUsage:{} productionLoss:{} maxShortCapacity:{} allocatedShortQuantity:{} remainingShortQuantity:{}",
                    totalIssue, actualUsage, productionLoss, maxShortCapacity, allocatedShortQuantity, remainingShortQuantity);
            if (allocatedShortQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            // 保存少到货记录
            ProTaskBomSkuLotRecord record = new ProTaskBomSkuLotRecord();
            record.setWorkorderId(proTask.getWorkorderId());
            record.setTaskId(proTask.getTaskId());
            record.setItemCode(itemCode);
            record.setItemName(itemName);
            record.setSkuLotNo(skuLotNo);
            record.setType(SkuLotRecordTypeEnums.SHORT_ARRIVAL.getCode());
            record.setQuantity(allocatedShortQuantity);

            proTaskBomSkuLotRecordService.insertProTaskBomSkuLotRecord(record);
            skuLotNoAvailableQtyMap.put(skuLotNo, allocatedShortQuantity);
            remainingShortQuantity = remainingShortQuantity.subtract(allocatedShortQuantity);

            log.info("记录少到货批次 - skuLotNo:{} allocatedQuantity:{} remainingQuantity:{}",
                    skuLotNo, allocatedShortQuantity, remainingShortQuantity);
        }

        if (remainingShortQuantity.compareTo(BigDecimal.ZERO) > 0) {
            log.warn("少到货数量未完全分配 - itemCode:{} remainingQuantity:{}", itemCode, remainingShortQuantity);
        }
    }

    /**
     * 记录多到货批次
     */
    private void recordExcessArrivalLots(ProTask proTask, ProTaskLeftItemSubmitDTO req,
                                         BigDecimal excessShortQuantity,
                                         List<StockLocationQueryResponse> queryResponses) {
        String itemCode = req.getItemCode();
        String itemName = req.getItemName();
        log.info("开始记录多到货批次 - itemCode:{} excessQuantity:{}", itemCode, excessShortQuantity);

        if (!queryResponses.isEmpty()) {
            // 最近的那个批次承担所有多到货数量
            StockLocationQueryResponse stockLocationQueryResponse = queryResponses.get(0);
            String skuLotNo = stockLocationQueryResponse.getSkuLotNo();
            ProTaskBomSkuLotRecord record = new ProTaskBomSkuLotRecord();
            record.setWorkorderId(proTask.getWorkorderId());
            record.setTaskId(proTask.getTaskId());
            record.setItemCode(itemCode);
            record.setItemName(itemName);
            record.setSkuLotNo(skuLotNo);
            record.setType(SkuLotRecordTypeEnums.EXCESS_ARRIVAL.getCode());
            record.setQuantity(excessShortQuantity);

            proTaskBomSkuLotRecordService.insertProTaskBomSkuLotRecord(record);

            log.info("记录多到货批次 - skuLotNo:{} quantity:{}", skuLotNo, excessShortQuantity);
        }
    }

    /**
     * 记录生产异常批次
     */
    private void recordProductionExceptionLots(ProTask proTask, ProTaskLeftItemSubmitDTO proTaskLeftItemSubmitDTO,
                                               Map<String, List<WmIssueLine>> skuLotIssueLineMap, List<ProTaskItemSkuSelectDTO> adjustDetailList,
                                               List<StockLocationQueryResponse> sortedOriginQueryResponses, Map<String, BigDecimal> skuLotNoAvailableQtyMap) {
        String itemCode = proTaskLeftItemSubmitDTO.getItemCode();
        log.info("开始记录生产异常批次 - itemCode:{}", itemCode);

        // 查询该任务的生产异常记录
        List<ProTaskException> exceptionList = proTaskExceptionService.selectProTaskExceptionList(
                new ProTaskException() {{
                    setTaskId(proTask.getTaskId());
                    setItemCode(itemCode);
                    setStatus("0"); // 正常状态
                }}
        );

        if (CollectionUtils.isEmpty(exceptionList)) {
            log.info("物料 {} 没有生产异常记录，跳过异常批次记录", itemCode);
            return;
        }
        BigDecimal excessShortQuantity = proTaskLeftItemSubmitDTO.getExcessShortQuantity();
        BigDecimal shortArrivalQuantity = BigDecimal.ZERO;
        if (Objects.nonNull(excessShortQuantity)) {
            shortArrivalQuantity = excessShortQuantity.compareTo(BigDecimal.ZERO) < 0 ?
                    excessShortQuantity.abs() : BigDecimal.ZERO;
        }

        // 1. 计算每个批次的可还料数量：领料单数量-系统生产使用数量-系统生产消耗-少到货数量
        Map<String, BigDecimal> skuLotReturnableMap = calculateReturnableQuantityForException(
                proTask, proTaskLeftItemSubmitDTO, skuLotIssueLineMap, adjustDetailList, sortedOriginQueryResponses, shortArrivalQuantity, skuLotNoAvailableQtyMap);

        if (skuLotReturnableMap.isEmpty()) {
            log.info("物料 {} 没有可分配异常的批次，但是不跳过异常批次记录", itemCode);
//            return;
        }

        // 3. 优先处理来料破损(llps)异常，然后按异常数量排序
        List<ProTaskException> sortedExceptions = prioritizeAndSortExceptions(exceptionList);

        // 4. 按入库时间从近到远分配异常
        allocateExceptionsToLots(proTask, proTaskLeftItemSubmitDTO,
                skuLotReturnableMap, sortedExceptions, sortedOriginQueryResponses);
    }

    /**
     * 计算每个批次的可还料数量
     */
    private Map<String, BigDecimal> calculateReturnableQuantity(ProTask proTask, ProTaskLeftItemSubmitDTO req,
                                                                Map<String, List<ProTaskItemSkuSelectDTO>> skuLotDetailMap) {
        Map<String, BigDecimal> returnableMap = new HashMap<>();

        for (Map.Entry<String, List<ProTaskItemSkuSelectDTO>> entry : skuLotDetailMap.entrySet()) {
            String skuLotNo = entry.getKey();
            List<ProTaskItemSkuSelectDTO> lotDetails = entry.getValue();

            // 计算该批次的使用量和损耗量
            BigDecimal usageQuantity = lotDetails.stream()
                    .filter(d -> ProTaskAdjustTypeEnum.PRODUCTION_USAGE.getCode().equals(d.getType()))
                    .map(ProTaskItemSkuSelectDTO::getAdjustQty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal lossQuantity = lotDetails.stream()
                    .filter(d -> ProTaskAdjustTypeEnum.PRODUCTION_LOSS.getCode().equals(d.getType()))
                    .map(ProTaskItemSkuSelectDTO::getAdjustQty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 查询该批次的少到货数量
            ProTaskBomSkuLotRecord queryRecord = new ProTaskBomSkuLotRecord();
            queryRecord.setTaskId(proTask.getTaskId());
            queryRecord.setItemCode(req.getItemCode());
            queryRecord.setSkuLotNo(skuLotNo);
            queryRecord.setType(SkuLotRecordTypeEnums.SHORT_ARRIVAL.getCode());

            List<ProTaskBomSkuLotRecord> shortArrivalRecords = proTaskBomSkuLotRecordService
                    .selectProTaskBomSkuLotRecordList(queryRecord);

            BigDecimal shortArrivalQuantity = shortArrivalRecords.stream()
                    .map(ProTaskBomSkuLotRecord::getQuantity)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 可还料数量 = 领料单数量 - 系统生产使用数量 - 系统生产消耗 - 少到货数量
            BigDecimal returnableQuantity = req.getItemTotalIssue()
                    .subtract(usageQuantity)
                    .subtract(lossQuantity)
                    .subtract(shortArrivalQuantity);

            if (returnableQuantity.compareTo(BigDecimal.ZERO) > 0) {
                returnableMap.put(skuLotNo, returnableQuantity);
            }

            log.info("批次可还料数量计算 - skuLotNo:{} totalIssue:{} usage:{} loss:{} shortArrival:{} returnable:{}",
                    skuLotNo, req.getItemTotalIssue(), usageQuantity, lossQuantity, shortArrivalQuantity, returnableQuantity);
        }

        return returnableMap;
    }

    /**
     * 分配异常到批次
     */
    private void allocateExceptions(ProTask proTask, String itemCode, String itemName,
                                    List<String> sortedSkuLotNos,
                                    Map<String, BigDecimal> skuLotReturnableMap,
                                    Map<String, List<ProTaskException>> exceptionReasonMap) {

        // 优先分配【来料不良】
        String materialDefectReason = "llps";
        if (exceptionReasonMap.containsKey(materialDefectReason)) {
            List<ProTaskException> materialDefectExceptions = exceptionReasonMap.get(materialDefectReason);
            allocateExceptionsByReason(proTask, itemCode, itemName, sortedSkuLotNos,
                    skuLotReturnableMap, materialDefectExceptions, materialDefectReason);
            exceptionReasonMap.remove(materialDefectReason);
        }

        // 按照【异常数量】最小的优先满足的原则，进行分配
        List<Map.Entry<String, List<ProTaskException>>> sortedExceptions = exceptionReasonMap.entrySet().stream()
                .sorted((e1, e2) -> {
                    BigDecimal total1 = e1.getValue().stream()
                            .map(ProTaskException::getExceptionQuantity)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal total2 = e2.getValue().stream()
                            .map(ProTaskException::getExceptionQuantity)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    return total1.compareTo(total2);
                })
                .collect(Collectors.toList());

        for (Map.Entry<String, List<ProTaskException>> entry : sortedExceptions) {
            String exceptionReason = entry.getKey();
            List<ProTaskException> exceptions = entry.getValue();
            allocateExceptionsByReason(proTask, itemCode, itemName, sortedSkuLotNos,
                    skuLotReturnableMap, exceptions, exceptionReason);
            exceptionReasonMap.remove(exceptionReason);
        }

        // 处理未分配的异常：挂在入库时间最晚的一个批次ID上
        handleUnallocatedExceptions(proTask, itemCode, itemName, sortedSkuLotNos, exceptionReasonMap);
    }

    /**
     * 按异常原因分配异常到批次
     */
    private void allocateExceptionsByReason(ProTask proTask, String itemCode, String itemName,
                                            List<String> sortedSkuLotNos,
                                            Map<String, BigDecimal> skuLotReturnableMap,
                                            List<ProTaskException> exceptions, String exceptionReason) {

        BigDecimal totalExceptionQuantity = exceptions.stream()
                .map(ProTaskException::getExceptionQuantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal remainingExceptionQuantity = totalExceptionQuantity;

        log.info("开始分配异常 - exceptionReason:{} totalQuantity:{}", exceptionReason, totalExceptionQuantity);

        for (String skuLotNo : sortedSkuLotNos) {
            if (remainingExceptionQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }

            BigDecimal returnableQuantity = skuLotReturnableMap.getOrDefault(skuLotNo, BigDecimal.ZERO);
            if (returnableQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            BigDecimal allocatedQuantity = remainingExceptionQuantity.min(returnableQuantity);

            // 保存生产异常记录
            ProTaskBomSkuLotRecord record = new ProTaskBomSkuLotRecord();
            record.setWorkorderId(proTask.getWorkorderId());
            record.setTaskId(proTask.getTaskId());
            record.setItemCode(itemCode);
            record.setItemName(itemName);
            record.setSkuLotNo(skuLotNo);
            record.setType(SkuLotRecordTypeEnums.PRODUCTION_EXCEPTION.getCode());
            record.setSubCode(exceptionReason);
            record.setQuantity(allocatedQuantity);

            proTaskBomSkuLotRecordService.insertProTaskBomSkuLotRecord(record);

            // 更新剩余可分配数量
            remainingExceptionQuantity = remainingExceptionQuantity.subtract(allocatedQuantity);
            skuLotReturnableMap.put(skuLotNo, returnableQuantity.subtract(allocatedQuantity));

            log.info("分配异常到批次 - skuLotNo:{} exceptionReason:{} allocatedQuantity:{} remainingQuantity:{}",
                    skuLotNo, exceptionReason, allocatedQuantity, remainingExceptionQuantity);
        }
    }

    /**
     * 处理未分配的异常
     */
    private void handleUnallocatedExceptions(ProTask proTask, String itemCode, String itemName,
                                             List<String> sortedSkuLotNos,
                                             Map<String, List<ProTaskException>> exceptionReasonMap) {

        if (sortedSkuLotNos.isEmpty()) {
            log.warn("没有可用批次处理未分配异常 - itemCode:{}", itemCode);
            return;
        }

        // 入库时间最晚的批次（排序后的最后一个）
        String latestSkuLotNo = sortedSkuLotNos.get(sortedSkuLotNos.size() - 1);

        for (Map.Entry<String, List<ProTaskException>> entry : exceptionReasonMap.entrySet()) {
            String exceptionReason = entry.getKey();
            List<ProTaskException> exceptions = entry.getValue();

            BigDecimal totalUnallocatedQuantity = exceptions.stream()
                    .map(ProTaskException::getExceptionQuantity)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (totalUnallocatedQuantity.compareTo(BigDecimal.ZERO) > 0) {
                // 保存未分配异常记录
                ProTaskBomSkuLotRecord record = new ProTaskBomSkuLotRecord();
                record.setWorkorderId(proTask.getWorkorderId());
                record.setTaskId(proTask.getTaskId());
                record.setItemCode(itemCode);
                record.setItemName(itemName);
                record.setSkuLotNo(latestSkuLotNo);
                record.setType(SkuLotRecordTypeEnums.PRODUCTION_EXCEPTION.getCode());
                record.setSubCode(exceptionReason);
                record.setQuantity(totalUnallocatedQuantity);

                proTaskBomSkuLotRecordService.insertProTaskBomSkuLotRecord(record);

                log.info("处理未分配异常 - skuLotNo:{} exceptionReason:{} quantity:{}",
                        latestSkuLotNo, exceptionReason, totalUnallocatedQuantity);
            }
        }
    }

    /**
     * 计算每个批次的可还料数量（用于异常分配）
     * 计算公式：领料单数量-系统生产使用数量-系统生产消耗-少到货数量
     */
    private Map<String, BigDecimal> calculateReturnableQuantityForException(ProTask proTask,
                                                                            ProTaskLeftItemSubmitDTO req,
                                                                            Map<String, List<WmIssueLine>> skuLotIssueLineMap, List<ProTaskItemSkuSelectDTO> adjustDetailList,
                                                                            List<StockLocationQueryResponse> sortedSkuLotNos,
                                                                            BigDecimal shortArrivalQuantity, Map<String, BigDecimal> skuLotNoAvailableQtyMap) {
        Map<String, BigDecimal> returnableMap = new HashMap<>();

        Map<String, List<ProTaskItemSkuSelectDTO>> skuLotNoMap = adjustDetailList.stream().collect(Collectors.groupingBy(ProTaskItemSkuSelectDTO::getSkuLotNo));
        for (StockLocationQueryResponse stockLocationQueryResponse : sortedSkuLotNos) {
            String skuLotNo = stockLocationQueryResponse.getSkuLotNo();
            List<ProTaskItemSkuSelectDTO> lotDetails = skuLotNoMap.getOrDefault(skuLotNo, new ArrayList<>());

            BigDecimal totalIssue = BigDecimal.ZERO;
            BigDecimal shortArrival;
            // 计算该批次的最大可承担少货数量：领料单数量 - 系统生产使用 - 系统生产消耗
            List<WmIssueLine> wmIssueLineList = skuLotIssueLineMap.get(skuLotNo);
            if (!CollectionUtils.isEmpty(wmIssueLineList)) {
                totalIssue = wmIssueLineList.stream().map(WmIssueLine::getQuantityIssued).reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            shortArrival = skuLotNoAvailableQtyMap.getOrDefault(skuLotNo, BigDecimal.ZERO);
            // 计算该批次的系统生产使用数量
            BigDecimal actualUsage = BigDecimal.ZERO;
            BigDecimal productionLoss = BigDecimal.ZERO;
            if (!CollectionUtils.isEmpty(lotDetails)) {
                actualUsage = lotDetails.stream()
                        .filter(d -> ProTaskAdjustTypeEnum.PRODUCTION_USAGE.getCode().equals(d.getType()))
                        .map(ProTaskItemSkuSelectDTO::getAdjustQty)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                productionLoss = lotDetails.stream()
                        .filter(d -> ProTaskAdjustTypeEnum.PRODUCTION_LOSS.getCode().equals(d.getType()))
                        .map(ProTaskItemSkuSelectDTO::getAdjustQty)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            // 可还料数量 = 领料单数量 - 系统生产使用 - 系统生产消耗 - 少到货数量
            BigDecimal returnableQuantity = totalIssue
                    .subtract(actualUsage)
                    .subtract(productionLoss)
                    .subtract(shortArrival);

            if (returnableQuantity.compareTo(BigDecimal.ZERO) > 0) {
                returnableMap.put(skuLotNo, returnableQuantity);
            }

            log.info("批次可还料数量计算 - skuLotNo:{} 总领料:{} 生产使用:{} 生产损耗:{} 少到货:{} 可分配异常:{}",
                    skuLotNo, totalIssue, actualUsage, productionLoss, shortArrival, returnableQuantity);
        }

        return returnableMap;
    }

    /**
     * 计算批次分摊的领料单数量
     */
    private BigDecimal calculateLotIssueQuantity(BigDecimal totalIssueQuantity, String skuLotNo,
                                                 Map<String, List<ProTaskItemSkuSelectDTO>> skuLotDetailMap) {
        // 简化处理：按批次平均分摊
        // 实际业务中可能需要根据具体的分摊规则来计算
        int totalLots = skuLotDetailMap.size();
        if (totalLots == 0) {
            return BigDecimal.ZERO;
        }
        return totalIssueQuantity.divide(BigDecimal.valueOf(totalLots), 4, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 计算批次分摊的少到货数量
     */
    private BigDecimal calculateLotShortQuantity(BigDecimal totalShortQuantity, String skuLotNo,
                                                 Map<String, List<ProTaskItemSkuSelectDTO>> skuLotDetailMap) {
        // 简化处理：按批次平均分摊
        // 实际业务中可能需要根据具体的分摊规则来计算
        int totalLots = skuLotDetailMap.size();
        if (totalLots == 0) {
            return BigDecimal.ZERO;
        }
        return totalShortQuantity.divide(BigDecimal.valueOf(totalLots), 4, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 优先处理来料破损异常，然后按异常数量排序
     *
     * @param exceptionList 异常列表
     * @return 排序后的异常列表
     */
    private List<ProTaskException> prioritizeAndSortExceptions(List<ProTaskException> exceptionList) {
        // 分离来料破损异常和其他异常
        List<ProTaskException> llpsExceptions = new ArrayList<>();
        List<ProTaskException> otherExceptions = new ArrayList<>();

        for (ProTaskException exception : exceptionList) {
            if ("llps".equalsIgnoreCase(exception.getExceptionReason())) {
                llpsExceptions.add(exception);
            } else {
                otherExceptions.add(exception);
            }
        }

        // 对来料破损异常按数量从小到大排序
        llpsExceptions.sort(Comparator.comparing(ProTaskException::getExceptionQuantity));

        // 对其他异常按数量从小到大排序
        otherExceptions.sort(Comparator.comparing(ProTaskException::getExceptionQuantity));

        // 合并列表：来料破损异常优先
        List<ProTaskException> sortedExceptions = new ArrayList<>();
        sortedExceptions.addAll(llpsExceptions);
        sortedExceptions.addAll(otherExceptions);

        log.info("异常排序完成 - 来料破损异常数量:{} 其他异常数量:{}", llpsExceptions.size(), otherExceptions.size());

        return sortedExceptions;
    }

    /**
     * 将异常分配到批次
     * 按照批次ID的入库时间，从近到远进行分配
     * 按照异常数量最小的优先满足的原则进行分配
     */
    private void allocateExceptionsToLots(ProTask proTask, ProTaskLeftItemSubmitDTO proTaskLeftItemSubmitDTO,
                                          Map<String, BigDecimal> skuLotReturnableMap,
                                          List<ProTaskException> sortedExceptions, List<StockLocationQueryResponse> stockLocationQueryResponse) {
        String itemCode = proTaskLeftItemSubmitDTO.getItemCode();
        String itemName = proTaskLeftItemSubmitDTO.getItemName();
        log.info("开始分配异常到批次 - itemCode:{} 可分配数量:{} 参数:{} 异常:{}",
                itemCode, skuLotReturnableMap.size(), JSON.toJSONString(skuLotReturnableMap), sortedExceptions.size());
//        Map<String, List<StockLocationQueryResponse>> skuCodeResponseMap =
//                stockLocationQueryResponse.stream().collect(Collectors.groupingBy(StockLocationQueryResponse::getSkuCode));
        // 复制可还料数量映射，用于分配过程中的扣减
        Map<String, BigDecimal> remainingReturnableMap = new HashMap<>(skuLotReturnableMap);
        List<ProTaskException> unallocatedExceptions = new ArrayList<>();

        for (ProTaskException exception : sortedExceptions) {
            BigDecimal remainingException = exception.getExceptionQuantity();
            for (StockLocationQueryResponse response : stockLocationQueryResponse) {
                String skuLotNo = response.getSkuLotNo();
                BigDecimal availableQuantity = remainingReturnableMap.getOrDefault(skuLotNo, BigDecimal.ZERO);
                log.info("开始计算异常批次 - skuLotNo:{} 可分配异常:{} 异常原因:{} 所需分配数量:{}",
                        response.getSkuLotNo(), availableQuantity, exception.getExceptionReason(), remainingException);
                if (availableQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                    continue; // 该批次已无可分配数量
                }

                if (remainingException.compareTo(BigDecimal.ZERO) <= 0) {
                    break; // 异常已完全分配
                }

                // 计算本批次可分配的数量
                BigDecimal allocatedQuantity = remainingException.min(availableQuantity);

                // 创建批次记录
                createExceptionLotRecord(proTask, itemCode, itemName, skuLotNo,
                        exception.getExceptionReason(), exception.getExceptionReasonDesc(),
                        allocatedQuantity);

                // 更新剩余数量
                remainingException = remainingException.subtract(allocatedQuantity);
                availableQuantity = availableQuantity.subtract(allocatedQuantity);
                remainingReturnableMap.put(skuLotNo, availableQuantity);

                log.info("异常分配成功 - skuLotNo:{} 原因:{} 分配:{} 剩余可分配:{} 异常剩余需分配:{}",
                        skuLotNo, exception.getExceptionReason(), allocatedQuantity, availableQuantity, remainingException);
            }

            // 如果异常未完全分配，记录未分配部分
            if (remainingException.compareTo(BigDecimal.ZERO) > 0) {
                ProTaskException unallocatedException = new ProTaskException();
                unallocatedException.setTaskId(exception.getTaskId());
                unallocatedException.setItemCode(exception.getItemCode());
                unallocatedException.setItemName(exception.getItemName());
                unallocatedException.setExceptionReason(exception.getExceptionReason());
                unallocatedException.setExceptionReasonDesc(exception.getExceptionReasonDesc());
                unallocatedException.setExceptionQuantity(remainingException);
                unallocatedExceptions.add(unallocatedException);
            }
        }

        // 处理未分配的异常：挂在入库时间最晚的批次上
        if (!unallocatedExceptions.isEmpty()) {
            for (ProTaskException unallocatedException : unallocatedExceptions) {
//                List<StockLocationQueryResponse> queryResponses = skuCodeResponseMap.get(unallocatedException.getItemCode());
//                if (!CollectionUtils.isEmpty(queryResponses)) {
                if (CollectionUtils.isEmpty(stockLocationQueryResponse)) {
                    log.info("未分配异常没有可挂载的批次 - itemCode:{} reason:{} quantity:{}",
                            unallocatedException.getItemCode(), unallocatedException.getExceptionReason(),
                            unallocatedException.getExceptionQuantity());
                    continue;
                }
                log.info("未分配异常开始挂载到最晚批次 - itemCode:{} reason:{} quantity:{}",
                        unallocatedException.getItemCode(), unallocatedException.getExceptionReason(),
                        unallocatedException.getExceptionQuantity());
                StockLocationQueryResponse queryResponse = stockLocationQueryResponse.get(0);
                String skuLotNo = queryResponse.getSkuLotNo();
                createExceptionLotRecord(proTask, itemCode, itemName, skuLotNo,
                        unallocatedException.getExceptionReason(),
                        unallocatedException.getExceptionReasonDesc(),
                        unallocatedException.getExceptionQuantity(), 0);

                log.info("未分配异常挂载到最晚批次 - skuLotNo:{} reason:{} quantity:{}",
                        skuLotNo, unallocatedException.getExceptionReason(),
                        unallocatedException.getExceptionQuantity());
//                }
            }
        }
    }

    /**
     * 创建异常批次记录
     */
    private void createExceptionLotRecord(ProTask proTask, String itemCode, String itemName, String skuLotNo,
                                          String exceptionReason, String exceptionReasonDesc, BigDecimal quantity) {
        this.createExceptionLotRecord(proTask, itemCode, itemName, skuLotNo, exceptionReason, exceptionReasonDesc, quantity, 1);
    }

    /**
     * 创建异常批次记录
     */
    private void createExceptionLotRecord(ProTask proTask, String itemCode, String itemName, String skuLotNo,
                                          String exceptionReason, String exceptionReasonDesc, BigDecimal quantity, Integer matchType) {
        ProTaskBomSkuLotRecord record = new ProTaskBomSkuLotRecord();
        record.setWorkorderId(proTask.getWorkorderId());
        record.setTaskId(proTask.getTaskId());
        record.setItemCode(itemCode);
        record.setItemName(itemName);
        record.setSkuLotNo(skuLotNo);
        record.setType(SkuLotRecordTypeEnums.PRODUCTION_EXCEPTION.getCode());
        record.setQuantity(quantity);
        record.setSubCode(exceptionReason);
        record.setMatchType(matchType);
        record.setCreateTime(new Date());

        proTaskBomSkuLotRecordService.insertProTaskBomSkuLotRecord(record);

        log.info("创建异常批次记录成功 - record:{}", JSON.toJSONString(record));
    }
}
