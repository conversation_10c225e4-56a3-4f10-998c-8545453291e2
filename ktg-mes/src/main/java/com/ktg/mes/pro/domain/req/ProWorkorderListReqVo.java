package com.ktg.mes.pro.domain.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 生产工单分页查询接口
 * @date 2025/5/8 16:16
 */
@Data
public class ProWorkorderListReqVo implements Serializable {
    @ApiModelProperty("工单编码")
    private String workorderCode;
    @ApiModelProperty("生产车间")
    private Long workshopId;
    @ApiModelProperty("货主名称")
    private String ownerCode;
    @ApiModelProperty("产品商品编码")
    private String productCode;
    @ApiModelProperty("产品商品名称")
    private String productName;
    @ApiModelProperty("需求时间开始")
    private Date requestDateBegin;
    @ApiModelProperty("需求时间结束")
    private Date requestDateEnd;
    @ApiModelProperty("状态")
    private String status;
    @ApiModelProperty("状态列表")
    private String multipleStatus;
    private List<String> statusList;
    @ApiModelProperty("生产批次号")
    private String produceBatchCode;

    @ApiModelProperty("关联入库单")
    private String externalLinkBillNo;

    @ApiModelProperty("需求来源")
    private String orderSource;

    @ApiModelProperty("是否同步ERP")
    private String pushStatus;

    @ApiModelProperty("生产要求")
    private String requirement;
}
