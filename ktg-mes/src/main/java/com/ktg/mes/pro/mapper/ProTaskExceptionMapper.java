package com.ktg.mes.pro.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ktg.mes.pro.domain.ProTaskException;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 生产异常标记Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
@Mapper
public interface ProTaskExceptionMapper extends BaseMapper<ProTaskException>
{
    /**
     * 查询生产异常标记
     * 
     * @param exceptionId 生产异常标记主键
     * @return 生产异常标记
     */
    public ProTaskException selectProTaskExceptionByExceptionId(Long exceptionId);

    /**
     * 查询生产异常标记列表
     * 
     * @param proTaskException 生产异常标记
     * @return 生产异常标记集合
     */
    public List<ProTaskException> selectProTaskExceptionList(ProTaskException proTaskException);

    /**
     * 根据任务ID查询生产异常标记列表
     * 
     * @param taskId 生产任务ID
     * @return 生产异常标记集合
     */
    public List<ProTaskException> selectProTaskExceptionByTaskId(@Param("taskId") Long taskId);

    /**
     * 根据任务ID列表查询生产异常标记列表
     * 
     * @param taskIdList 生产任务ID列表
     * @return 生产异常标记集合
     */
    public List<ProTaskException> selectProTaskExceptionByTaskIdList(@Param("taskIdList") List<Long> taskIdList);

    /**
     * 根据工单ID查询生产异常标记列表
     * 
     * @param workorderId 生产工单ID
     * @return 生产异常标记集合
     */
    public List<ProTaskException> selectProTaskExceptionByWorkorderId(@Param("workorderId") Long workorderId);

    /**
     * 查询异常原因合并统计信息
     * 
     * @param taskId 生产任务ID
     * @return 合并统计结果
     */
    public List<ProTaskException> selectExceptionSummaryByTaskId(@Param("taskId") Long taskId);

    /**
     * 新增生产异常标记
     * 
     * @param proTaskException 生产异常标记
     * @return 结果
     */
    public int insertProTaskException(ProTaskException proTaskException);

    /**
     * 修改生产异常标记
     * 
     * @param proTaskException 生产异常标记
     * @return 结果
     */
    public int updateProTaskException(ProTaskException proTaskException);

    /**
     * 删除生产异常标记
     * 
     * @param exceptionId 生产异常标记主键
     * @return 结果
     */
    public int deleteProTaskExceptionByExceptionId(Long exceptionId);

    /**
     * 批量删除生产异常标记
     * 
     * @param exceptionIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProTaskExceptionByExceptionIds(Long[] exceptionIds);
}
