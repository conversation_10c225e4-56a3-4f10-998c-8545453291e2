package com.ktg.mes.pro.service.impl;

import com.ktg.common.utils.DateUtils;
import com.ktg.mes.pro.domain.ProTaskBomSkuLotRecord;
import com.ktg.mes.pro.mapper.ProTaskBomSkuLotRecordMapper;
import com.ktg.mes.pro.service.IProTaskBomSkuLotRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 生产任务BOM批次信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
@Service
public class ProTaskBomSkuLotRecordServiceImpl implements IProTaskBomSkuLotRecordService {
    @Autowired
    private ProTaskBomSkuLotRecordMapper proTaskBomSkuLotRecordMapper;

    /**
     * 查询生产任务BOM批次信息
     *
     * @param recordId 生产任务BOM批次信息主键
     * @return 生产任务BOM批次信息
     */
    @Override
    public ProTaskBomSkuLotRecord selectProTaskBomSkuLotRecordByRecordId(Long recordId)
    {
        return proTaskBomSkuLotRecordMapper.selectProTaskBomSkuLotRecordByRecordId(recordId);
    }

    /**
     * 查询生产任务BOM批次信息列表
     *
     * @param proTaskBomSkuLotRecord 生产任务BOM批次信息
     * @return 生产任务BOM批次信息
     */
    @Override
    public List<ProTaskBomSkuLotRecord> selectProTaskBomSkuLotRecordList(ProTaskBomSkuLotRecord proTaskBomSkuLotRecord)
    {
        return proTaskBomSkuLotRecordMapper.selectProTaskBomSkuLotRecordList(proTaskBomSkuLotRecord);
    }

    /**
     * 新增生产任务BOM批次信息
     *
     * @param proTaskBomSkuLotRecord 生产任务BOM批次信息
     * @return 结果
     */
    @Override
    public int insertProTaskBomSkuLotRecord(ProTaskBomSkuLotRecord proTaskBomSkuLotRecord)
    {
        proTaskBomSkuLotRecord.setCreateTime(DateUtils.getNowDate());
        return proTaskBomSkuLotRecordMapper.insertProTaskBomSkuLotRecord(proTaskBomSkuLotRecord);
    }

    /**
     * 修改生产任务BOM批次信息
     *
     * @param proTaskBomSkuLotRecord 生产任务BOM批次信息
     * @return 结果
     */
    @Override
    public int updateProTaskBomSkuLotRecord(ProTaskBomSkuLotRecord proTaskBomSkuLotRecord)
    {
        proTaskBomSkuLotRecord.setUpdateTime(DateUtils.getNowDate());
        return proTaskBomSkuLotRecordMapper.updateProTaskBomSkuLotRecord(proTaskBomSkuLotRecord);
    }

    /**
     * 批量删除生产任务BOM批次信息
     *
     * @param recordIds 需要删除的生产任务BOM批次信息主键
     * @return 结果
     */
    @Override
    public int deleteProTaskBomSkuLotRecordByRecordIds(Long[] recordIds)
    {
        return proTaskBomSkuLotRecordMapper.deleteProTaskBomSkuLotRecordByRecordIds(recordIds);
    }

    /**
     * 删除生产任务BOM批次信息信息
     *
     * @param recordId 生产任务BOM批次信息主键
     * @return 结果
     */
    @Override
    public int deleteProTaskBomSkuLotRecordByRecordId(Long recordId)
    {
        return proTaskBomSkuLotRecordMapper.deleteProTaskBomSkuLotRecordByRecordId(recordId);
    }

    @Override
    public List<ProTaskBomSkuLotRecord> selectByTaskId(Long taskId) {
        if (Objects.isNull(taskId)){
            return Collections.emptyList();
        }
        ProTaskBomSkuLotRecord proTaskBomSkuLotRecord = new ProTaskBomSkuLotRecord();
        proTaskBomSkuLotRecord.setTaskId(taskId);
        return proTaskBomSkuLotRecordMapper.selectProTaskBomSkuLotRecordList(proTaskBomSkuLotRecord);
    }
}