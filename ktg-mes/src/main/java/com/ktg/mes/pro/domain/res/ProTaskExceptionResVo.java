package com.ktg.mes.pro.domain.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 生产异常标记响应对象
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
@Data
public class ProTaskExceptionResVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 异常记录ID */
    @ApiModelProperty(value = "异常记录ID")
    private Long exceptionId;

    /** 生产任务ID */
    @ApiModelProperty(value = "生产任务ID")
    private Long taskId;

    /** 生产任务编号 */
    @ApiModelProperty(value = "生产任务编号")
    private String taskCode;

    /** 生产工单ID */
    @ApiModelProperty(value = "生产工单ID")
    private Long workorderId;

    /** 生产工单编号 */
    @ApiModelProperty(value = "生产工单编号")
    private String workorderCode;

    /** 原材料代码 */
    @ApiModelProperty(value = "原材料代码")
    private String itemCode;

    /** 原材料名称 */
    @ApiModelProperty(value = "原材料名称")
    private String itemName;

    /** 异常原因（字典值） */
    @ApiModelProperty(value = "异常原因")
    private String exceptionReason;

    /** 异常原因描述 */
    @ApiModelProperty(value = "异常原因描述")
    private String exceptionReasonDesc;

    /** 异常数量 */
    @ApiModelProperty(value = "异常数量")
    private BigDecimal exceptionQuantity;

    /** 备注说明 */
    @ApiModelProperty(value = "备注说明")
    private String remark;

    /** 登记人 */
    @ApiModelProperty(value = "登记人")
    private String registrant;

    /** 登记人姓名 */
    @ApiModelProperty(value = "登记人姓名")
    private String registrantName;

    /** 登记时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "登记时间")
    private Date registerTime;

    /** 附件URL */
    @ApiModelProperty(value = "附件URL")
    private String attachmentUrl;

    /** 附件名称 */
    @ApiModelProperty(value = "附件名称")
    private String attachmentName;

    /** 附件大小（字节） */
    @ApiModelProperty(value = "附件大小")
    private Long attachmentSize;

    /** 状态（0正常 1删除） */
    @ApiModelProperty(value = "状态")
    private String status;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
