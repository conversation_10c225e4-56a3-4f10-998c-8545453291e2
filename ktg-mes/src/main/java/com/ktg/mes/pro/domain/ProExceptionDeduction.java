package com.ktg.mes.pro.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ktg.common.annotation.Excel;
import com.ktg.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 异常扣减单对象 pro_exception_deduction
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@TableName("pro_exception_deduction")
public class ProExceptionDeduction extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /** 扣减单ID */
    private Long deductionId;

    /** 扣减单编号 */
    @Excel(name = "扣减单编号")
    private String deductionCode;

    /** 生产工单ID */
    @Excel(name = "生产工单ID")
    private Long workorderId;

    /** 生产工单编号 */
    @Excel(name = "生产工单编号")
    private String workorderCode;

    /** 生产工单名称 */
    @Excel(name = "生产工单名称")
    private String workorderName;

    /** 物料编码 */
    @Excel(name = "物料编码")
    private String itemCode;

    /** 物料名称 */
    @Excel(name = "物料名称")
    private String itemName;

    /** 规格型号 */
    @Excel(name = "规格型号")
    private String specification;

    /** 单位 */
    @Excel(name = "单位")
    private String unitOfMeasure;

    /** 待扣数量 */
    @Excel(name = "待扣数量")
    private BigDecimal deductionQuantity;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** 状态描述 */
    @Excel(name = "状态描述")
    private String statusDesc;

    /** 车间ID */
    @Excel(name = "车间ID")
    private Long workshopId;

    /** 车间名称 */
    @Excel(name = "车间名称")
    private String workshopName;

    /** 仓库编码 */
    @Excel(name = "仓库编码")
    private String warehouseCode;

    /** 仓库名称 */
    @Excel(name = "仓库名称")
    private String warehouseName;

    /** 货主编码 */
    @Excel(name = "货主编码")
    private String ownerCode;

    /** 货主名称 */
    @Excel(name = "货主名称")
    private String ownerName;

    /** 关联领料单ID */
    @Excel(name = "关联领料单ID")
    private Long issueId;

    /** 关联领料单编号 */
    @Excel(name = "关联领料单编号")
    private String issueCode;

    /** WMS调减单号 */
    @Excel(name = "WMS调减单号")
    private String wmsAdjustOrderNo;

    /** 领料时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "领料时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date issueTime;

    /** 推送时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "推送时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date pushTime;

    /** 完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date completeTime;

    /** 关闭时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "关闭时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date closeTime;

    /** 关闭原因 */
    @Excel(name = "关闭原因")
    private String closeReason;
}
