package com.ktg.mes.pro.service;

import com.ktg.mes.pro.domain.ProWorkorder;
import com.ktg.mes.pro.domain.req.ProWorkorderErpPageReqVo;
import com.ktg.mes.pro.domain.req.ProWorkorderListReqVo;
import com.ktg.mes.pro.domain.res.ProWorkorderBomViewResVo;
import com.ktg.mes.pro.domain.res.ProWorkorderOverViewResVo;

import java.util.List;

/**
 * 生产工单Service接口
 *
 * <AUTHOR>
 * @date 2022-05-09
 */
public interface IProWorkorderService {
    /**
     * 查询生产工单
     *
     * @param workorderId 生产工单主键
     * @return 生产工单
     */
    ProWorkorder selectProWorkorderByWorkorderId(Long workorderId);


    ProWorkorder selectProWorkorderByWorkorderCode(String workorderCode);

    /**
     * 查询生产工单列表
     *
     * @param workorderIdList 生产工单ID集合
     * @return 生产工单
     */
    public List<ProWorkorder> selectProWorkorderByWorkorderIds(List<Long> workorderIdList);


    /**
     * 查询生产工单
     *
     * @param workorderId 生产工单主键
     * @return 生产工单
     */
    public List<ProWorkorder> selectProWorkorderListByParentId(Long workorderId);

    /**
     * 查询生产工单列表
     *
     * @param proWorkorder 生产工单
     * @return 生产工单集合
     */
    public List<ProWorkorder> selectProWorkorderList(ProWorkorder proWorkorder);

    List<ProWorkorder> proWorkorderPage(ProWorkorderListReqVo reqVo);
    List<ProWorkorder> proWorkorderErpPage(ProWorkorderErpPageReqVo reqVo);


    public String checkWorkorderCodeUnique(ProWorkorder proWorkorder);

    /**
     * 新增生产工单
     *
     * @param proWorkorder 生产工单
     * @return 结果
     */
    public int insertProWorkorder(ProWorkorder proWorkorder);

    /**
     * 修改生产工单
     *
     * @param proWorkorder 生产工单
     * @return 结果
     */
    public int updateProWorkorder(ProWorkorder proWorkorder);

    /**
     * 批量删除生产工单
     *
     * @param workorderIds 需要删除的生产工单主键集合
     * @return 结果
     */
    public int deleteProWorkorderByWorkorderIds(Long[] workorderIds);

    /**
     * 删除生产工单信息
     *
     * @param workorderId 生产工单主键
     * @return 结果
     */
    public int deleteProWorkorderByWorkorderId(Long workorderId);

    ProWorkorderOverViewResVo overView(Long workorderId) throws Exception;


    List<ProWorkorderBomViewResVo> bomView(Long workorderId) throws Exception;

    List<ProWorkorder> selectByExternalLinkBillNo(String externalLinkBillNo);

    /**
     * 根据成品SKU查询生产工单
     * @param productSkuList 成品SKU列表
     * @return 生产工单列表
     */
    List<ProWorkorder> selectByProductSkuList(List<String> productSkuList);

    List<ProWorkorder> queryByCondition(List<String> proWorkorderCodeList, String proWorkorderStatus, Long finishDateBegin, Long finishDateEnd, List<String> productSkuList, List<Long> productSkuLotWorkorderList);
}
