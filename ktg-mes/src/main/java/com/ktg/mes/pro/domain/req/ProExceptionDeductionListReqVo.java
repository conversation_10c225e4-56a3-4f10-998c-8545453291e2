package com.ktg.mes.pro.domain.req;

import lombok.Data;

import java.io.Serializable;

/**
 * 异常扣减单查询请求VO
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
public class ProExceptionDeductionListReqVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 扣减单编号 */
    private String deductionCode;

    /** 生产工单ID */
    private Long workorderId;

    /** 生产工单编号 */
    private String workorderCode;

    /** 物料编码 */
    private String itemCode;

    /** 物料名称 */
    private String itemName;

    /** 状态 */
    private String status;

    /** 车间ID */
    private Long workshopId;

    /** 仓库编码 */
    private String warehouseCode;

    /** 货主编码 */
    private String ownerCode;

    /** 关联领料单ID */
    private Long issueId;

    /** 页码 */
    private Integer pageNum;

    /** 页面大小 */
    private Integer pageSize;
}
