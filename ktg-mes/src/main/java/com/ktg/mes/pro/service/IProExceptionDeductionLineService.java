package com.ktg.mes.pro.service;

import com.ktg.mes.pro.domain.ProExceptionDeductionLine;

import java.util.List;

/**
 * 异常扣减单表体Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
public interface IProExceptionDeductionLineService {
    
    /**
     * 查询异常扣减单表体
     * 
     * @param lineId 异常扣减单表体主键
     * @return 异常扣减单表体
     */
    ProExceptionDeductionLine selectProExceptionDeductionLineByLineId(Long lineId);

    /**
     * 查询异常扣减单表体列表
     * 
     * @param proExceptionDeductionLine 异常扣减单表体
     * @return 异常扣减单表体集合
     */
    List<ProExceptionDeductionLine> selectProExceptionDeductionLineList(ProExceptionDeductionLine proExceptionDeductionLine);

    /**
     * 根据扣减单ID查询表体列表
     * 
     * @param deductionId 扣减单ID
     * @return 异常扣减单表体集合
     */
    List<ProExceptionDeductionLine> selectByDeductionId(Long deductionId);

    /**
     * 根据工单ID查询表体列表
     * 
     * @param workorderId 工单ID
     * @return 异常扣减单表体集合
     */
    List<ProExceptionDeductionLine> selectByWorkorderId(Long workorderId);

    /**
     * 根据任务ID查询表体列表
     * 
     * @param taskId 任务ID
     * @return 异常扣减单表体集合
     */
    List<ProExceptionDeductionLine> selectByTaskId(Long taskId);

    /**
     * 根据任务ID和物料编码查询表体列表
     * 
     * @param taskId 任务ID
     * @param itemCode 物料编码
     * @return 异常扣减单表体集合
     */
    List<ProExceptionDeductionLine> selectByTaskIdAndItemCode(Long taskId, String itemCode);

    /**
     * 检查是否存在指定任务和物料的表体记录
     * 
     * @param taskId 任务ID
     * @param itemCode 物料编码
     * @return 异常扣减单表体
     */
    ProExceptionDeductionLine checkExistsByTaskIdAndItemCode(Long taskId, String itemCode);

    /**
     * 新增异常扣减单表体
     * 
     * @param proExceptionDeductionLine 异常扣减单表体
     * @return 结果
     */
    int insertProExceptionDeductionLine(ProExceptionDeductionLine proExceptionDeductionLine);

    /**
     * 修改异常扣减单表体
     * 
     * @param proExceptionDeductionLine 异常扣减单表体
     * @return 结果
     */
    int updateProExceptionDeductionLine(ProExceptionDeductionLine proExceptionDeductionLine);

    /**
     * 批量删除异常扣减单表体
     * 
     * @param lineIds 需要删除的异常扣减单表体主键集合
     * @return 结果
     */
    int deleteProExceptionDeductionLineByLineIds(Long[] lineIds);

    /**
     * 删除异常扣减单表体信息
     * 
     * @param lineId 异常扣减单表体主键
     * @return 结果
     */
    int deleteProExceptionDeductionLineByLineId(Long lineId);

    /**
     * 根据扣减单ID删除表体
     * 
     * @param deductionId 扣减单ID
     * @return 结果
     */
    int deleteByDeductionId(Long deductionId);
}
