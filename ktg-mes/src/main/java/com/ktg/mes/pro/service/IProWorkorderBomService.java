package com.ktg.mes.pro.service;

import com.ktg.mes.pro.domain.ProWorkorderBom;

import java.util.List;

/**
 * 生产工单BOM组成Service接口
 * 
 * <AUTHOR>
 * @date 2022-05-09
 */
public interface IProWorkorderBomService 
{
    /**
     * 查询生产工单BOM组成
     * 
     * @param lineId 生产工单BOM组成主键
     * @return 生产工单BOM组成
     */
    public ProWorkorderBom selectProWorkorderBomByLineId(Long lineId);

    /**
     * 查询生产工单BOM组成列表
     * 
     * @param proWorkorderBom 生产工单BOM组成
     * @return 生产工单BOM组成集合
     */
    List<ProWorkorderBom> selectProWorkorderBomList(ProWorkorderBom proWorkorderBom);

    List<ProWorkorderBom> selectProWorkorderBomListByWorkorderId(Long workorderId);

    /**
     * 根据工单ID列表批量查询生产工单BOM组成
     * @param workorderIdList 工单ID列表
     * @return 生产工单BOM组成集合
     */
    List<ProWorkorderBom> selectProWorkorderBomListByWorkorderId(List<Long> workorderIdList);

    /**
     * 新增生产工单BOM组成
     * 
     * @param proWorkorderBom 生产工单BOM组成
     * @return 结果
     */
    int insertProWorkorderBom(ProWorkorderBom proWorkorderBom);

    /**
     * 修改生产工单BOM组成
     * 
     * @param proWorkorderBom 生产工单BOM组成
     * @return 结果
     */
    int updateProWorkorderBom(ProWorkorderBom proWorkorderBom);

    /**
     * 批量删除生产工单BOM组成
     * 
     * @param lineIds 需要删除的生产工单BOM组成主键集合
     * @return 结果
     */
    int deleteProWorkorderBomByLineIds(Long[] lineIds);

    /**
     * 删除生产工单BOM组成信息
     * 
     * @param lineId 生产工单BOM组成主键
     * @return 结果
     */
    int deleteProWorkorderBomByLineId(Long lineId);


    /**
     * 批量删除工单下所有的BOM组成数据
     * @param workorderId
     * @return
     */
    public int deleteProWorkorderBomByWorkorderId(Long workorderId);

}
