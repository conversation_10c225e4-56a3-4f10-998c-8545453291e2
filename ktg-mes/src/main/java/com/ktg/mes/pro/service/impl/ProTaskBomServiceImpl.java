package com.ktg.mes.pro.service.impl;

import com.ktg.common.utils.DateUtils;
import com.ktg.mes.pro.domain.ProTaskBom;
import com.ktg.mes.pro.mapper.ProTaskBomMapper;
import com.ktg.mes.pro.service.IProTaskBomService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 生产任务BOM组成Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-30
 */
@Service
public class ProTaskBomServiceImpl implements IProTaskBomService
{
    @Autowired
    private ProTaskBomMapper proTaskBomMapper;

    /**
     * 查询生产任务BOM组成
     * 
     * @param lineId 生产任务BOM组成主键
     * @return 生产任务BOM组成
     */
    @Override
    public ProTaskBom selectProTaskBomByLineId(Long lineId)
    {
        return proTaskBomMapper.selectProTaskBomByLineId(lineId);
    }

    /**
     * 查询生产任务BOM组成列表
     * 
     * @param proTaskBom 生产任务BOM组成
     * @return 生产任务BOM组成
     */
    @Override
    public List<ProTaskBom> selectProTaskBomList(ProTaskBom proTaskBom)
    {
        return proTaskBomMapper.selectProTaskBomList(proTaskBom);
    }

    @Override
    public List<ProTaskBom> selectProTaskBomListByWorkorderId(Long workorderId) {
        if (Objects.isNull(workorderId)){
            return new ArrayList<>();
        }
        ProTaskBom proTaskBom = new ProTaskBom();
        proTaskBom.setWorkorderId(workorderId);
        return proTaskBomMapper.selectProTaskBomList(proTaskBom);
    }

    /**
     * 新增生产任务BOM组成
     * 
     * @param proTaskBom 生产任务BOM组成
     * @return 结果
     */
    @Override
    public int insertProTaskBom(ProTaskBom proTaskBom)
    {
        proTaskBom.setCreateTime(DateUtils.getNowDate());
        return proTaskBomMapper.insertProTaskBom(proTaskBom);
    }

    /**
     * 修改生产任务BOM组成
     * 
     * @param proTaskBom 生产任务BOM组成
     * @return 结果
     */
    @Override
    public int updateProTaskBom(ProTaskBom proTaskBom)
    {
        proTaskBom.setUpdateTime(DateUtils.getNowDate());
        return proTaskBomMapper.updateProTaskBom(proTaskBom);
    }

    /**
     * 批量删除生产任务BOM组成
     * 
     * @param lineIds 需要删除的生产任务BOM组成主键
     * @return 结果
     */
    @Override
    public int deleteProTaskBomByLineIds(Long[] lineIds)
    {
        return proTaskBomMapper.deleteProTaskBomByLineIds(lineIds);
    }

    /**
     * 删除生产任务BOM组成信息
     * 
     * @param lineId 生产任务BOM组成主键
     * @return 结果
     */
    @Override
    public int deleteProTaskBomByLineId(Long lineId)
    {
        return proTaskBomMapper.deleteProTaskBomByLineId(lineId);
    }

    @Override
    public void insertProTaskBomList(List<ProTaskBom> proTaskBomList) {
        for (ProTaskBom proTaskBom : proTaskBomList) {
            proTaskBomMapper.insertProTaskBom(proTaskBom);
        }
    }

    @Override
    public List<ProTaskBom> selectProTaskBomListByTaskId(Long taskId) {
        if (Objects.isNull(taskId)){
            return new ArrayList<>();
        }
        ProTaskBom proTaskBom = new ProTaskBom();
        proTaskBom.setTaskId(taskId);
        return proTaskBomMapper.selectProTaskBomList(proTaskBom);
    }

    @Override
    public List<ProTaskBom> selectProTaskBomListByWorkorderId(List<Long> workorderIdList) {
        if (CollectionUtils.isEmpty(workorderIdList)){
            return new ArrayList<>();
        }
        return proTaskBomMapper.selectProTaskBomListByWorkorderId(workorderIdList);
    }
}
