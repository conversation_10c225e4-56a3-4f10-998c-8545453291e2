package com.ktg.mes.wm.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.platform.wms.rpc.client.mes.basis.IWarehouseBasisMesRpcClient;
import com.dt.platform.wms.rpc.client.mes.basis.dto.CargoMesRpcDTO;
import com.dt.platform.wms.rpc.client.mes.basis.dto.LocationMesRpcDTO;
import com.dt.platform.wms.rpc.client.mes.basis.dto.WarehouseMesRpcDTO;
import com.dt.platform.wms.rpc.client.mes.basis.dto.ZoneMesRpcDTO;
import com.dt.platform.wms.rpc.client.mes.basis.param.CargoMesRpcParam;
import com.dt.platform.wms.rpc.client.mes.basis.param.LocationMesRpcParam;
import com.dt.platform.wms.rpc.client.mes.basis.param.WarehouseMesRpcParam;
import com.dt.platform.wms.rpc.client.mes.basis.param.ZoneMesRpcParam;
import com.dt.platform.wms.rpc.client.mes.common.Result;
import com.dt.platform.wms.rpc.client.mes.dto.PageDTO;
import com.ktg.common.annotation.Log;
import com.ktg.common.constant.HttpStatus;
import com.ktg.common.constant.UserConstants;
import com.ktg.common.core.controller.BaseController;
import com.ktg.common.core.domain.AjaxResult;
import com.ktg.common.core.page.TableDataInfo;
import com.ktg.common.enums.BusinessType;
import com.ktg.common.utils.ServletUtils;
import com.ktg.common.utils.poi.ExcelUtil;
import com.ktg.mes.wm.domain.WmWarehouse;
import com.ktg.mes.wm.domain.req.WmWarehouseLocationListReqVo;
import com.ktg.mes.wm.domain.req.WmWarehouseZoneListReqVo;
import com.ktg.mes.wm.domain.res.WmOwnerListResVo;
import com.ktg.mes.wm.service.IWmStorageAreaService;
import com.ktg.mes.wm.service.IWmStorageLocationService;
import com.ktg.mes.wm.service.IWmWarehouseService;
import com.ktg.mes.wm.utils.WmBarCodeUtil;
import com.ktg.system.strategy.AutoCodeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

import static com.ktg.common.core.page.TableSupport.PAGE_NUM;
import static com.ktg.common.core.page.TableSupport.PAGE_SIZE;

/**
 * 仓库设置Controller
 *
 * <AUTHOR>
 * @menu 仓库设置
 * @date 2022-05-07
 */
@Slf4j
@RestController
@RequestMapping("/mes/wm/warehouse")
public class WmWarehouseController extends BaseController {
    @Autowired
    private IWmWarehouseService wmWarehouseService;

    @Autowired
    private IWmStorageLocationService wmStorageLocationService;

    @Autowired
    private IWmStorageAreaService wmStorageAreaService;

    @Autowired
    private WmBarCodeUtil wmBarCodeUtil;

    @DubboReference
    private IWarehouseBasisMesRpcClient iWarehouseBasisMesRpcClient;

    @Autowired
    private AutoCodeUtil autoCodeUtil;

    /**
     * 查询仓库设置列表
     */
    @GetMapping("/list")
    public TableDataInfo list(WmWarehouse wmWarehouse) {
        startPage();
        List<WmWarehouse> list = wmWarehouseService.selectWmWarehouseList(wmWarehouse);
        return getDataTable(list);
    }

    /**
     * 查询树型的列表
     *
     * @return
     */
    @GetMapping("/getTreeList")
    public AjaxResult getTreeList() {
        return AjaxResult.success(wmWarehouseService.getTreeList());
    }

    /**
     * 导出仓库设置列表
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:warehouse:export')")
    @Log(title = "仓库设置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WmWarehouse wmWarehouse) {
        List<WmWarehouse> list = wmWarehouseService.selectWmWarehouseList(wmWarehouse);
        ExcelUtil<WmWarehouse> util = new ExcelUtil<WmWarehouse>(WmWarehouse.class);
        util.exportExcel(response, list, "仓库设置数据");
    }

    /**
     * 获取仓库设置详细信息
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:warehouse:query')")
    @GetMapping(value = "/{warehouseId}")
    public AjaxResult getInfo(@PathVariable("warehouseId") Long warehouseId) {
        return AjaxResult.success(wmWarehouseService.selectWmWarehouseByWarehouseId(warehouseId));
    }

    /**
     * 新增仓库设置
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:warehouse:add')")
    @Log(title = "仓库设置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WmWarehouse wmWarehouse) {
        if (UserConstants.NOT_UNIQUE.equals(wmWarehouseService.checkWarehouseCodeUnique(wmWarehouse))) {
            return AjaxResult.error("仓库编码已存在！");
        }
        if (UserConstants.NOT_UNIQUE.equals(wmWarehouseService.checkWarehouseNameUnique(wmWarehouse))) {
            return AjaxResult.error("仓库名称已存在！");
        }

        wmWarehouseService.insertWmWarehouse(wmWarehouse);
        wmBarCodeUtil.generateBarCode(UserConstants.BARCODE_TYPE_WAREHOUSE, wmWarehouse.getWarehouseId(), wmWarehouse.getWarehouseCode(), wmWarehouse.getWarehouseName());

        return AjaxResult.success(wmWarehouse.getWarehouseId());
    }

    /**
     * 修改仓库设置
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:warehouse:edit')")
    @Log(title = "仓库设置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WmWarehouse wmWarehouse) {
        if (UserConstants.NOT_UNIQUE.equals(wmWarehouseService.checkWarehouseCodeUnique(wmWarehouse))) {
            return AjaxResult.error("仓库编码已存在！");
        }
        if (UserConstants.NOT_UNIQUE.equals(wmWarehouseService.checkWarehouseNameUnique(wmWarehouse))) {
            return AjaxResult.error("仓库名称已存在！");
        }
        return toAjax(wmWarehouseService.updateWmWarehouse(wmWarehouse));
    }

    /**
     * 删除仓库设置
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:warehouse:remove')")
    @Log(title = "仓库设置", businessType = BusinessType.DELETE)
    @Transactional
    @DeleteMapping("/{warehouseIds}")
    public AjaxResult remove(@PathVariable Long[] warehouseIds) {

        //TODO:仓库删除之前的逻辑校验

        for (Long wahouseId : warehouseIds
        ) {
            wmStorageLocationService.deleteByWarehouseId(wahouseId);
            wmStorageAreaService.deleteByWarehouseId(wahouseId);
        }

        return toAjax(wmWarehouseService.deleteWmWarehouseByWarehouseIds(warehouseIds));
    }

    /**
     * 同步仓库
     *
     * @param wmsWarehouseCode
     * @return
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:warehouse:sync')")
    @Log(title = "同步仓库", businessType = BusinessType.INSERT)
    @GetMapping("/syncWmsWarehouse/{warehouseCode}")
    public AjaxResult syncWmsWarehouse(@PathVariable("warehouseCode") String wmsWarehouseCode) {
        WmWarehouse existWarehouse = wmWarehouseService.selectWmWarehouseByWarehouseCode(wmsWarehouseCode);
        if (Objects.nonNull(existWarehouse)) {
            return AjaxResult.error("仓库编码已存在！");
        }
        WarehouseMesRpcParam warehouseMesRpcParam = new WarehouseMesRpcParam();
        warehouseMesRpcParam.setWarehouseCode(wmsWarehouseCode);
        try {
            Result<WarehouseMesRpcDTO> result = iWarehouseBasisMesRpcClient.queryWarehouse(warehouseMesRpcParam);
            if (result.checkSuccess()) {
                WarehouseMesRpcDTO data = result.getData();
                if (Objects.nonNull(data)) {
                    WmWarehouse wmWarehouse = new WmWarehouse();
                    wmWarehouse.setCode(autoCodeUtil.genSerialCode("WAREHOUSE_CODE"));
                    wmWarehouse.setWarehouseCode(data.getWarehouseCode());
                    wmWarehouse.setWarehouseName(data.getWarehouseName());
                    wmWarehouse.setFrozenFlag(UserConstants.YES);
                    wmWarehouse.setCreateBy(getUsername());
                    wmWarehouse.setCreateTime(new Date());
                    wmWarehouse.setUpdateBy(getUsername());
                    wmWarehouse.setUpdateTime(new Date());
                    wmWarehouseService.insertWmWarehouse(wmWarehouse);
                    return AjaxResult.success(wmWarehouse.getWarehouseId());
                } else {
                    return AjaxResult.error("未查询到仓库信息");
                }
            } else {
                return AjaxResult.error(result.getMessage());
            }
        } catch (Exception e) {
            logger.error("同步仓库信息失败 error={}", e.getMessage(), e);
        }
        return AjaxResult.error("同步仓库信息失败");
    }


    /**
     * 库区分页
     *
     * @param reqVo
     * @return {@link ZoneMesRpcDTO}
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:warehouse:zone')")
    @PostMapping("/zone")
    public TableDataInfo zonePage(@RequestBody WmWarehouseZoneListReqVo reqVo) {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(-1);
        if (Objects.isNull(reqVo.getWarehouseCode())) {
            rspData.setMsg("仓编码不能为空");
            return rspData;
        }
        Integer currentPage = ServletUtils.getParameterToInt(PAGE_NUM);
        Integer pageSize = ServletUtils.getParameterToInt(PAGE_SIZE);
        ZoneMesRpcParam zoneMesRpcParam = new ZoneMesRpcParam();
        zoneMesRpcParam.setWarehouseCode(reqVo.getWarehouseCode());
        zoneMesRpcParam.setZoneCode(reqVo.getZoneCode());
        zoneMesRpcParam.setZoneNameLike(reqVo.getZoneName());
        zoneMesRpcParam.setCurrent(currentPage);
        zoneMesRpcParam.setSize(pageSize);
        try {
            logger.info("req={}", JSON.toJSONString(zoneMesRpcParam));
            Result<PageDTO<ZoneMesRpcDTO>> result = iWarehouseBasisMesRpcClient.getZonePage(zoneMesRpcParam);
            logger.info("result={}", JSON.toJSONString(result));
            if (result.checkSuccess()) {
                PageDTO<ZoneMesRpcDTO> data = result.getData();
                rspData.setCode(HttpStatus.SUCCESS);
                rspData.setMsg("查询成功");
                rspData.setRows(data.getRecords());
                rspData.setTotal(data.getTotal());
                return rspData;
            } else {
                rspData.setMsg(result.getMessage());
                return rspData;
            }
        } catch (Exception e) {
            logger.error("查询库区信息失败 error={}", e.getMessage(), e);
        }
        rspData.setMsg("查询库区信息失败");
        return rspData;
    }

    /**
     * 正品库区下拉
     *
     * @param reqVo
     * @return {@link ZoneMesRpcDTO}
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:warehouse:zone')")
    @PostMapping("/zoneAvlList")
    public AjaxResult zoneAvlList(@RequestBody WmWarehouseZoneListReqVo reqVo) {
        log.info("zoneAvlList reqVo={}", JSON.toJSONString(reqVo));
        reqVo.setSkuQualityList(Arrays.asList(SkuQualityEnum.SKU_QUALITY_AVL.getLevel()));
        return this.zoneList(reqVo);
    }

    /**
     * 次品库区下拉
     *
     * @param reqVo
     * @return {@link ZoneMesRpcDTO}
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:warehouse:zone')")
    @PostMapping("/zoneDamageList")
    public AjaxResult zoneDamageList(@RequestBody WmWarehouseZoneListReqVo reqVo) {
        log.info("zoneDamageList reqVo={}", JSON.toJSONString(reqVo));
        reqVo.setSkuQualityList(Arrays.asList(SkuQualityEnum.SKU_QUALITY_DAMAGE.getLevel()));
        return this.zoneList(reqVo);
    }

    /**
     * 库区下拉
     *
     * @param reqVo
     * @return {@link ZoneMesRpcDTO}
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:warehouse:zone')")
    @PostMapping("/zoneList")
    public AjaxResult zoneList(@RequestBody WmWarehouseZoneListReqVo reqVo) {
        if (Objects.isNull(reqVo.getWarehouseCode())) {
            return AjaxResult.error("仓编码不能为空");
        }
        ZoneMesRpcParam zoneMesRpcParam = new ZoneMesRpcParam();
        zoneMesRpcParam.setWarehouseCode(reqVo.getWarehouseCode());
        zoneMesRpcParam.setZoneCode(reqVo.getZoneCode());
        zoneMesRpcParam.setZoneNameLike(reqVo.getZoneName());
        if (Objects.nonNull(reqVo.getSkuQualityList())) {
            zoneMesRpcParam.setSkuQualityList(reqVo.getSkuQualityList());
        }
        if (Objects.nonNull(reqVo.getZoneTypeList())) {
            zoneMesRpcParam.setZoneTypeList(reqVo.getZoneTypeList());
        }
        if (Objects.nonNull(reqVo.getZoneAttrList())) {
            zoneMesRpcParam.setZoneAttrList(reqVo.getZoneAttrList());
        }
        try {
            log.info("zoneList req={}", JSON.toJSONString(zoneMesRpcParam));
            Result<List<ZoneMesRpcDTO>> result = iWarehouseBasisMesRpcClient.getZoneList(zoneMesRpcParam);
            log.info("zoneList result={}", JSON.toJSONString(result));
            if (result.checkSuccess()) {
                List<ZoneMesRpcDTO> data = result.getData();
                if (Objects.nonNull(reqVo.getFilterZoneAttr()) && reqVo.getFilterZoneAttr()) {
                    data = data.stream().filter(d -> Objects.isNull(d.getZoneAttr()) || Objects.equals(d.getZoneAttr(), "")).collect(Collectors.toList());
                }
                return AjaxResult.success(data);
            } else {
                log.info("查询库区信息失败 error={}", result.getMessage());
                return AjaxResult.error(result.getMessage());
            }
        } catch (Exception e) {
            log.error("查询库区信息失败 error={}", e.getMessage(), e);
        }
        return AjaxResult.error("查询失败");
    }


    /**
     * 库位分页
     *
     * @param reqVo
     * @return
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:warehouse:location')")
    @PostMapping("/location")
    public TableDataInfo locationPage(@RequestBody WmWarehouseLocationListReqVo reqVo) {
        if (Objects.isNull(reqVo.getWarehouseCode())) {
            return TableDataInfo.error("仓编码不能为空");
        }
        if (Objects.isNull(reqVo.getZoneCode())) {
            return TableDataInfo.error("库位编码不能为空");
        }
        Integer currentPage = ServletUtils.getParameterToInt(PAGE_NUM);
        Integer pageSize = ServletUtils.getParameterToInt(PAGE_SIZE);
        LocationMesRpcParam locationMesRpcParam = new LocationMesRpcParam();
        locationMesRpcParam.setWarehouseCode(reqVo.getWarehouseCode());
        locationMesRpcParam.setZoneCode(reqVo.getZoneCode());
        locationMesRpcParam.setLocationCode(reqVo.getLocationCode());
        locationMesRpcParam.setCurrent(currentPage);
        locationMesRpcParam.setSize(pageSize);
        try {
            logger.debug("req={}", JSON.toJSONString(locationMesRpcParam));
            Result<PageDTO<LocationMesRpcDTO>> result = iWarehouseBasisMesRpcClient.getLocationPage(locationMesRpcParam);
            logger.debug("result={}", JSON.toJSONString(result));
            if (result.checkSuccess()) {
                TableDataInfo rspData = new TableDataInfo();
                PageDTO<LocationMesRpcDTO> data = result.getData();
                rspData.setCode(HttpStatus.SUCCESS);
                rspData.setMsg("查询成功");
                rspData.setRows(data.getRecords());
                rspData.setTotal(data.getTotal());
                return rspData;
            } else {
                return TableDataInfo.error(result.getMessage());
            }
        } catch (Exception e) {
            logger.error("查询库位信息失败 error={}", e.getMessage(), e);
        }
        return TableDataInfo.error("查询库位信息失败");
    }


    /**
     * 库位下拉
     *
     * @param reqVo
     * @return
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:warehouse:location')")
    @PostMapping("/locationList")
    public AjaxResult locationList(@RequestBody WmWarehouseLocationListReqVo reqVo) {
        if (Objects.isNull(reqVo.getWarehouseCode())) {
            return AjaxResult.error("仓编码不能为空");
        }
        if (Objects.isNull(reqVo.getZoneCode())) {
            return AjaxResult.error("库区编码不能为空");
        }
        LocationMesRpcParam locationMesRpcParam = new LocationMesRpcParam();
        locationMesRpcParam.setWarehouseCode(reqVo.getWarehouseCode());
        locationMesRpcParam.setZoneCode(reqVo.getZoneCode());
        try {
            log.info("req={}", JSON.toJSONString(locationMesRpcParam));
            Result<List<LocationMesRpcDTO>> result = iWarehouseBasisMesRpcClient.getLocationList(locationMesRpcParam);
            log.info("result={}", JSON.toJSONString(result));
            if (result.checkSuccess()) {
                List<LocationMesRpcDTO> data = result.getData();
                return AjaxResult.success(data);
            } else {
                log.info("查询库位信息失败 error={}", result.getMessage());
                return AjaxResult.error(result.getMessage());
            }
        } catch (Exception e) {
            log.error("查询库位信息失败 error={}", e.getMessage(), e);
        }
        return AjaxResult.error("查询失败");
    }

    /**
     * 所有货主
     *
     * @return
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:warehouse:owner')")
    @GetMapping("/ownerAll")
    public AjaxResult ownerAll() {
        WmWarehouse wmWarehouse = new WmWarehouse();
        wmWarehouse.setFrozenFlag(UserConstants.NO);
        List<WmWarehouse> wmWarehouseList = wmWarehouseService.selectWmWarehouseList(wmWarehouse);
        List<String> warehouseCodeList = wmWarehouseList.stream().map(WmWarehouse::getWarehouseCode).distinct().collect(Collectors.toList());
        List<WmOwnerListResVo> resVos = new ArrayList<>();
        for (String w : warehouseCodeList) {
            CargoMesRpcParam cargoMesRpcParam = new CargoMesRpcParam();
            cargoMesRpcParam.setWarehouseCode(w);
            Result<List<CargoMesRpcDTO>> cargoList = iWarehouseBasisMesRpcClient.queryCargoList(cargoMesRpcParam);
            if (cargoList.checkSuccess()) {
                List<CargoMesRpcDTO> data = cargoList.getData();
                if (CollectionUtil.isNotEmpty(data)) {
                    List<WmOwnerListResVo> vos = data.stream().map(d -> {
                        WmOwnerListResVo resVo = new WmOwnerListResVo();
                        resVo.setOwnerCode(d.getCargoCode());
                        resVo.setOwnerName(d.getCargoName());
                        resVo.setWarehouseCode(d.getWarehouseCode());
                        resVo.setWarehouseName(d.getWarehouseName());
                        return resVo;
                    }).collect(Collectors.toList());
                    resVos.addAll(vos);
                }
            }
        }
        return AjaxResult.success(resVos);
    }

    /**
     * 仓库下拉列表
     *
     * @return
     */
    @GetMapping("/warehouseEnableAll")
    public AjaxResult warehouseEnableAll() {
        WmWarehouse wmWarehouse = new WmWarehouse();
        wmWarehouse.setFrozenFlag(UserConstants.NO);
        List<WmWarehouse> wmWarehouseList = wmWarehouseService.selectWmWarehouseList(wmWarehouse);
        return AjaxResult.success(wmWarehouseList);
    }


    /**
     * 货主分页
     *
     * @param reqVo
     * @return List<WmOwnerListResVo>
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:warehouse:owner')")
    @GetMapping("/owner")
    public AjaxResult owner(WmWarehouseLocationListReqVo reqVo) {
        if (Objects.isNull(reqVo.getWarehouseCode())) {
            return AjaxResult.error("仓编码不能为空");
        }
        List<WmOwnerListResVo> resVos = new ArrayList<>();
        CargoMesRpcParam cargoMesRpcParam = new CargoMesRpcParam();
        cargoMesRpcParam.setWarehouseCode(reqVo.getWarehouseCode());
        Result<List<CargoMesRpcDTO>> cargoList = iWarehouseBasisMesRpcClient.queryCargoList(cargoMesRpcParam);
        if (cargoList.checkSuccess()) {
            List<CargoMesRpcDTO> data = cargoList.getData();
            if (CollectionUtil.isNotEmpty(data)) {
                List<WmOwnerListResVo> vos = data.stream().map(d -> {
                    WmOwnerListResVo resVo = new WmOwnerListResVo();
                    resVo.setOwnerCode(d.getCargoCode());
                    resVo.setOwnerName(d.getCargoName());
                    resVo.setWarehouseCode(d.getWarehouseCode());
                    resVo.setWarehouseName(d.getWarehouseName());
                    return resVo;
                }).collect(Collectors.toList());
                resVos.addAll(vos);
            }
        }
        return AjaxResult.success(resVos);
    }


}
