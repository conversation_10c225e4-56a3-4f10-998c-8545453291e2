package com.ktg.mes.wm.controller;

import com.alibaba.fastjson.JSON;
import com.danding.business.client.rpc.goods.facade.IMesGoodsRpcFacade;
import com.danding.business.client.rpc.goods.param.MesGoodsQueryRpcParam;
import com.danding.business.client.rpc.goods.result.MesGoodsRpcResult;
import com.danding.component.common.api.common.response.ListVO;
import com.dt.platform.wms.rpc.client.mes.dto.PageDTO;
import com.dt.platform.wms.rpc.client.mes.stock.IStockLocationMesPage;
import com.dt.platform.wms.rpc.client.mes.stock.StockLocationPageRequest;
import com.dt.platform.wms.rpc.client.mes.stock.StockLocationPageResponse;
import com.ktg.common.annotation.Log;
import com.ktg.common.core.controller.BaseController;
import com.ktg.common.core.domain.AjaxResult;
import com.ktg.common.core.page.TableDataInfo;
import com.ktg.common.enums.BusinessType;
import com.ktg.common.utils.ServletUtils;
import com.ktg.common.utils.poi.ExcelUtil;
import com.ktg.mes.wm.domain.WmMaterialStock;
import com.ktg.mes.wm.domain.req.WmMaterialStockListReqVo;
import com.ktg.mes.wm.service.IWmMaterialStockService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.ktg.common.core.page.TableSupport.PAGE_NUM;
import static com.ktg.common.core.page.TableSupport.PAGE_SIZE;

/**
 * 库存记录Controller
 * 
 * <AUTHOR>
 * @date 2022-05-30
 */
@Slf4j
@RestController
@RequestMapping("/mes/wm/wmstock")
public class WmMaterialStockController extends BaseController
{
    @Autowired
    private IWmMaterialStockService wmMaterialStockService;
    @DubboReference
    private IMesGoodsRpcFacade mesGoodsRpcFacade;
    @DubboReference
    private IStockLocationMesPage stockLocationMesPage;

    /**
     * 查询库存记录列表
     */
    @GetMapping("/list")
    public TableDataInfo<StockLocationPageResponse> list(WmMaterialStockListReqVo reqVo) {
        if (Objects.isNull(reqVo)){
            return TableDataInfo.error("请求参数为空");
        }
        if (Objects.isNull(reqVo.getCategoryId())){
            return TableDataInfo.error("请选择商品分类");
        }
        if (Objects.isNull(reqVo.getWarehouseCode())){
            return TableDataInfo.error("请选择仓库");
        }
        List<String> skuCodeList ;


        MesGoodsQueryRpcParam queryRpcParam = new MesGoodsQueryRpcParam();
        queryRpcParam.setCategoryId(reqVo.getCategoryId());
        if (Objects.nonNull(reqVo.getOwnerCode())){
            queryRpcParam.setOwnerCode(reqVo.getOwnerCode());
        }
        if (Objects.nonNull(reqVo.getItemCode())){
            queryRpcParam.setSku(reqVo.getItemCode());
        }
        try {
            log.info("erp req={}", JSON.toJSONString(queryRpcParam));
            ListVO<MesGoodsRpcResult> mesGoodsRpcResultListVO = mesGoodsRpcFacade.pageMesGoodsSelect(queryRpcParam);
            log.info("erp res={}",JSON.toJSONString(mesGoodsRpcResultListVO));

            List<MesGoodsRpcResult> dataList = mesGoodsRpcResultListVO.getDataList();
            if (CollectionUtils.isEmpty(dataList)){
                return TableDataInfo.error("未查询到ERP商品信息");
            }
            skuCodeList = dataList.stream().map(MesGoodsRpcResult::getSku).distinct().collect(Collectors.toList());
        }catch (Exception e){
            log.error("查询ERP商品信息失败 error={}", e.getMessage(), e);
            return TableDataInfo.error("查询ERP商品信息失败");
        }
        StockLocationPageRequest pageRequest = new StockLocationPageRequest();
        pageRequest.setWarehouseCode(reqVo.getWarehouseCode());
        pageRequest.setSkuCodeList(skuCodeList);
        if (Objects.nonNull(reqVo.getExternalLinkBillNo())){
            pageRequest.setExternalLinkBillNo(reqVo.getExternalLinkBillNo());
        }
        if (Objects.nonNull(reqVo.getOwnerCode())){
            pageRequest.setCargoCode(reqVo.getOwnerCode());
        }
        if (Objects.nonNull(reqVo.getItemCode())){
            pageRequest.setSkuCode(reqVo.getItemCode());
        }
        if (Objects.nonNull(reqVo.getSkuQuality())){
            pageRequest.setSkuQuality(reqVo.getSkuQuality());
        }
        if (Objects.nonNull(reqVo.getSkuLotNo())){
            pageRequest.setSkuLotNo(reqVo.getSkuLotNo());
        }
        if (Objects.nonNull(reqVo.getProductionNo())){
            pageRequest.setProductionNo(reqVo.getProductionNo());
        }
        if (Objects.nonNull(reqVo.getZoneCode())){
            pageRequest.setZoneCode(reqVo.getZoneCode());
        }
        if (Objects.nonNull(reqVo.getLocationCode())){
            pageRequest.setLocationCode(reqVo.getLocationCode());
        }
        Integer currentPage = ServletUtils.getParameterToInt(PAGE_NUM);
        Integer pageSize = ServletUtils.getParameterToInt(PAGE_SIZE);
        pageRequest.setCurrentPage(currentPage);
        pageRequest.setPageSize(pageSize);
        try {
            log.info("req={}", JSON.toJSONString(pageRequest));
            PageDTO<StockLocationPageResponse> page = stockLocationMesPage.page(pageRequest);
            log.info("result={}", JSON.toJSONString(page));
            TableDataInfo<StockLocationPageResponse> tableDataInfo = new TableDataInfo<>();
            tableDataInfo.setRows(page.getRecords());
            tableDataInfo.setTotal(page.getTotal());
            tableDataInfo.setPageNum((int) page.getCurrent());
            tableDataInfo.setPageSize((int) page.getSize());
            return tableDataInfo;
        }catch (Exception e){
            log.error("查询WMS库存信息失败 error={}", e.getMessage(), e);
            return TableDataInfo.error("查询WMS库存信息失败");
        }
    }

    /**
     * 导出库存记录列表
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:wmstock:export')")
    @Log(title = "库存记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WmMaterialStock wmMaterialStock)
    {
        List<WmMaterialStock> list = wmMaterialStockService.selectWmMaterialStockList(wmMaterialStock);
        ExcelUtil<WmMaterialStock> util = new ExcelUtil<WmMaterialStock>(WmMaterialStock.class);
        util.exportExcel(response, list, "库存记录数据");
    }

    /**
     * 获取库存记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:wmstock:query')")
    @GetMapping(value = "/{materialStockId}")
    public AjaxResult getInfo(@PathVariable("materialStockId") Long materialStockId)
    {
        return AjaxResult.success(wmMaterialStockService.selectWmMaterialStockByMaterialStockId(materialStockId));
    }

    /**
     * 新增库存记录
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:wmstock:add')")
    @Log(title = "库存记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WmMaterialStock wmMaterialStock)
    {
        return toAjax(wmMaterialStockService.insertWmMaterialStock(wmMaterialStock));
    }

    /**
     * 修改库存记录
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:wmstock:edit')")
    @Log(title = "库存记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WmMaterialStock wmMaterialStock)
    {
        return toAjax(wmMaterialStockService.updateWmMaterialStock(wmMaterialStock));
    }

    /**
     * 删除库存记录
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:wmstock:remove')")
    @Log(title = "库存记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{materialStockIds}")
    public AjaxResult remove(@PathVariable Long[] materialStockIds)
    {
        return toAjax(wmMaterialStockService.deleteWmMaterialStockByMaterialStockIds(materialStockIds));
    }
}
