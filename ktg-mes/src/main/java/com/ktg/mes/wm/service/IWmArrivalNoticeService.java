package com.ktg.mes.wm.service;

import com.ktg.mes.wm.domain.WmArrivalNotice;
import com.ktg.mes.wm.domain.req.WmArrivalNoticeLeftCountReqVo;
import com.ktg.mes.wm.domain.req.WmArrivalNoticeLeftSubmitReqVo;
import com.ktg.mes.wm.domain.req.WmArrivalNoticeListReqVo;
import com.ktg.mes.wm.domain.res.*;

import java.util.List;

/**
 * 到货通知单Service接口
 * 
 * <AUTHOR>
 * @date 2024-11-12
 */
public interface IWmArrivalNoticeService 
{
    /**
     * 查询到货通知单
     * 
     * @param noticeId 到货通知单主键
     * @return 到货通知单
     */
    public WmArrivalNotice selectWmArrivalNoticeByNoticeId(Long noticeId);

    /**
     * 查询到货通知单列表
     * 
     * @param wmArrivalNotice 到货通知单
     * @return 到货通知单集合
     */
    public List<WmArrivalNotice> selectWmArrivalNoticeList(WmArrivalNotice wmArrivalNotice);

    List<WmArrivalNotice> wmArrivalNoticePage(WmArrivalNoticeListReqVo reqVo);



    /**
     * 检查通知单编码是否唯一
     * @return
     */
    public String checkRnCodeUnique(WmArrivalNotice wmArrivalNotice);

    /**
     * 新增到货通知单
     * 
     * @param wmArrivalNotice 到货通知单
     * @return 结果
     */
    public int insertWmArrivalNotice(WmArrivalNotice wmArrivalNotice);

    /**
     * 修改到货通知单
     * 
     * @param wmArrivalNotice 到货通知单
     * @return 结果
     */
    public int updateWmArrivalNotice(WmArrivalNotice wmArrivalNotice);

    /**
     * 根据行上的检测状态，更新当前单据的状态
     * 如果所有需要进行检验的行已经绑定了检验单，则更新为APPROVED
     * @param noticeId
     */
    public void updateStatus(Long noticeId);

    void updateTakeStockStatus(Long noticeId,String takeStockStatus);

    /**
     * 批量删除到货通知单
     * 
     * @param noticeIds 需要删除的到货通知单主键集合
     * @return 结果
     */
    public int deleteWmArrivalNoticeByNoticeIds(Long[] noticeIds);

    /**
     * 删除到货通知单信息
     * 
     * @param noticeId 到货通知单主键
     * @return 结果
     */
    public int deleteWmArrivalNoticeByNoticeId(Long noticeId);

    List<WmArriveNoticeInfoResVo> receiveInfo(Long noticeId) throws Exception;

    WmArrivalNoticeLeftViewResVo leftView(Long noticeId) throws Exception;

    WmArrivalNoticeLeftViewNewResVo leftViewNew(Long noticeId)throws Exception;

    WmArrivalNoticeLeftInfoResVo leftItemProcess(Long noticeId) throws Exception;

    WmArrivalNoticeLeftInfoNewResVo leftItemProcessNew(Long noticeId);


    void leftSubmit(WmArrivalNoticeLeftSubmitReqVo reqVo) throws Exception;

    WmArrivalNotice selectByErpOrderNo(String erpOrderNo);

    WmArrivalNotice selectByNoticeCode(String noticeCode);

    void leftSubmitStart(Long lineId) throws Exception;

    /**
     * 余料清点数量校验
     *
     * @param reqVo 清点数量请求
     * @return 校验结果
     * @throws Exception 异常
     */
    WmArrivalNoticeStockValidationResVo validateLeftCount(WmArrivalNoticeLeftCountReqVo reqVo) throws Exception;

    /**
     * 完成余料清点（新版本）
     *
     * @param reqVo 清点数量请求
     * @throws Exception 异常
     */
    void leftSubmitFinish(WmArrivalNoticeLeftCountReqVo reqVo) throws Exception;

    /**
     * 完成余料清点（旧版本，保持兼容性）
     *
     * @param lineId 行ID
     * @throws Exception 异常
     */
    void leftSubmitFinish(Long lineId) throws Exception;

}
