package com.ktg.mes.wm.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.dt.component.common.enums.sku.InventoryTypeEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.platform.wms.rpc.client.mes.common.Result;
import com.dt.platform.wms.rpc.client.mes.move.*;
import com.dt.platform.wms.rpc.client.mes.stock.IStockLocationMesQuery;
import com.dt.platform.wms.rpc.client.mes.stock.StockLocationQueryRequest;
import com.dt.platform.wms.rpc.client.mes.stock.StockLocationQueryResponse;
import com.ktg.common.annotation.Log;
import com.ktg.common.constant.UserConstants;
import com.ktg.common.core.controller.BaseController;
import com.ktg.common.core.domain.AjaxResult;
import com.ktg.common.core.page.TableDataInfo;
import com.ktg.common.enums.BusinessType;
import com.ktg.common.enums.OrderStatusEnum;
import com.ktg.common.utils.ConvertUtil;
import com.ktg.common.utils.poi.ExcelUtil;
import com.ktg.mes.md.domain.MdWorkshop;
import com.ktg.mes.md.service.IMdWorkshopService;
import com.ktg.mes.pro.domain.ProTask;
import com.ktg.mes.pro.domain.ProWorkorder;
import com.ktg.mes.pro.domain.ProWorkorderBom;
import com.ktg.mes.pro.service.IProTaskService;
import com.ktg.mes.pro.service.IProWorkorderBomService;
import com.ktg.mes.pro.service.IProWorkorderService;
import com.ktg.mes.wm.domain.WmIssueHeader;
import com.ktg.mes.wm.domain.WmIssueLine;
import com.ktg.mes.wm.domain.WmRtIssue;
import com.ktg.mes.wm.domain.WmRtIssueLine;
import com.ktg.mes.wm.domain.req.*;
import com.ktg.mes.wm.domain.res.*;
import com.ktg.mes.wm.domain.tx.RtIssueTxBean;
import com.ktg.mes.wm.service.IWmIssueHeaderService;
import com.ktg.mes.wm.service.IWmIssueLineService;
import com.ktg.mes.wm.service.IWmRtIssueLineService;
import com.ktg.mes.wm.service.IWmRtIssueService;
import com.ktg.system.strategy.AutoCodeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 生产退料单头Controller
 *
 * <AUTHOR>
 * @menu 生产退料
 * @date 2022-09-15
 */
@Slf4j
@RestController
@RequestMapping("/mes/wm/rtissue")
public class WmRtIssueController extends BaseController {
    @Autowired
    private IWmRtIssueService wmRtIssueService;
    @Autowired
    private IWmIssueHeaderService wmIssueHeaderService;
    @Autowired
    private IWmIssueLineService wmIssueLineService;
    @Autowired
    private IWmRtIssueLineService wmRtIssueLineService;

    @Autowired
    private IProWorkorderService proWorkorderService;

    @Autowired
    private IProWorkorderBomService proWorkorderBomService;

    @Autowired
    private IProTaskService proTaskService;

    @Autowired
    private AutoCodeUtil autoCodeUtil;

    @Autowired
    private IMdWorkshopService mdWorkshopService;

    @DubboReference
    private IMoveMesClient iMoveMesClient;

    @DubboReference
    private IMoveMesQuery iMoveMesQuery;

    @DubboReference
    private IStockLocationMesQuery iStockLocationMesQuery;

    /**
     * 查询生产退料单头列表
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:rtissue:list')")
    @GetMapping("/list")
    public TableDataInfo list(WmRtIssueListReqVo reqVo) {
        startPage();
        List<WmRtIssue> list = wmRtIssueService.wmRtIssuePage(reqVo);
        TableDataInfo dataTable = getDataTable(list);
        Map<String, String> orderStatusMap = new HashMap<>(32);
        Map<String, List<WmRtIssue>> warehouseIssueMap = list.stream().filter(issue -> Objects.nonNull(issue.getWarehouseCode())).collect(Collectors.groupingBy(WmRtIssue::getWarehouseCode));
        warehouseIssueMap.forEach((code, issueHeaders) -> {
            MoveQueryRequest moveQueryRequest = new MoveQueryRequest();
            moveQueryRequest.setWarehouseCode(code);
            List<String> wmsOrderList = issueHeaders.stream().map(WmRtIssue::getWmsOrderNo).collect(Collectors.toList());
            moveQueryRequest.setCodeList(wmsOrderList);
            log.info("查询退料单状态 req={}",JSON.toJSONString(moveQueryRequest));
            Result<List<MoveQueryResponse>> listResult = iMoveMesQuery.list(moveQueryRequest);
            log.info("查询退料单状态 res={}",JSON.toJSONString(listResult));
            List<MoveQueryResponse> data = listResult.getData();
            data.forEach(moveQueryResponse -> orderStatusMap.put(moveQueryResponse.getCode(), moveQueryResponse.getStatusDesc()));
        });
        List<WmRtIssueListResVo> resVoList = list.stream().map(issueHeader -> {
            WmRtIssueListResVo wmRtIssueListResVo = ConvertUtil.beanConvert(issueHeader, WmRtIssueListResVo.class);
            if (orderStatusMap.containsKey(wmRtIssueListResVo.getWmsOrderNo())) {
                wmRtIssueListResVo.setWmsOrderStatus(orderStatusMap.get(wmRtIssueListResVo.getWmsOrderNo()));
            }
            return wmRtIssueListResVo;
        }).collect(Collectors.toList());
        dataTable.setRows(resVoList);
        return dataTable;
    }

    /**
     * 导出生产退料单头列表
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:rtissue:export')")
    @Log(title = "生产退料单头", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WmRtIssue wmRtIssue) {
        List<WmRtIssue> list = wmRtIssueService.selectWmRtIssueList(wmRtIssue);
        ExcelUtil<WmRtIssue> util = new ExcelUtil<WmRtIssue>(WmRtIssue.class);
        util.exportExcel(response, list, "生产退料单头数据");
    }

    /**
     * 获取生产退料单头详细信息
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:rtissue:query')")
    @GetMapping(value = "/{rtId}")
    public AjaxResult getInfo(@PathVariable("rtId") Long rtId) {
        WmRtIssue wmRtIssue = wmRtIssueService.selectWmRtIssueByRtId(rtId);
        if (Objects.isNull(wmRtIssue)) {
            return AjaxResult.error("生产退料单头不存在");
        }
        List<WmRtIssueLine> lineList = wmRtIssueLineService.selectWmRtIssueLineListByRtId(rtId);
        WmRtIssueDetailInfoResVo resVo = new WmRtIssueDetailInfoResVo();
        WmRtIssueResVo wmRtIssueResVo = ConvertUtil.beanConvert(wmRtIssue, WmRtIssueResVo.class);
        resVo.setWmRtIssueResVo(wmRtIssueResVo);
        List<WmRtIssueLineResVo> lineResVoList = lineList.stream().map(line -> {
            WmRtIssueLineResVo lineResVo = ConvertUtil.beanConvert(line, WmRtIssueLineResVo.class);
            if (Objects.nonNull(line.getSkuQuality())) {
                lineResVo.setSkuQualityDesc(SkuQualityEnum.getEnum(line.getSkuQuality()).getMessage());
            }
            if (Objects.nonNull(line.getInventoryType())) {
                lineResVo.setInventoryTypeDesc(InventoryTypeEnum.getEnum(line.getInventoryType()).getMessage());
            }
            lineResVo.setOwnerName(wmRtIssue.getOwnerName());
            return lineResVo;
        }).collect(Collectors.toList());
        resVo.setLineResVoList(lineResVoList);
        return AjaxResult.success(resVo);
    }

    /**
     * 查看bomInfo
     *
     * @param reqVo
     * @return
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:issueheader:bomInfo')")
    @GetMapping("/bomInfo")
    public AjaxResult bomInfo(WmRtIssueBomInfoReqVo reqVo) {
        Long workorderId = reqVo.getWorkorderId();
        ProWorkorder proWorkorder = proWorkorderService.selectProWorkorderByWorkorderId(workorderId);
        if (Objects.isNull(proWorkorder)) {
            return AjaxResult.error("生产工单不存在");
        }
        ProWorkorderBom proWorkorderBom = new ProWorkorderBom();
        proWorkorderBom.setWorkorderId(workorderId);
        List<ProWorkorderBom> proWorkorderBoms = proWorkorderBomService.selectProWorkorderBomList(proWorkorderBom);
        if (CollectionUtils.isEmpty(proWorkorderBoms)) {
            return AjaxResult.error("生产工单BOM不存在");
        }
        Long workshopId = proWorkorder.getWorkshopId();
        MdWorkshop mdWorkshop = mdWorkshopService.selectMdWorkshopByWorkshopId(workshopId);
        Long taskId = reqVo.getTaskId();
        List<WmIssueHeader> issueHeaderList = wmIssueHeaderService.selectWmIssueHeaderByTaskId(taskId);
        issueHeaderList = issueHeaderList.stream().filter(i->Objects.equals(i.getStatus(), OrderStatusEnum.FINISHED.getCode())).collect(Collectors.toList());
        List<String> skuLotNoList = new ArrayList<>();
        if (!CollectionUtil.isEmpty(issueHeaderList)) {
            List<WmIssueLine> wmIssueLineList = wmIssueLineService.selectWmIssueLineByIssueId(issueHeaderList.stream().map(WmIssueHeader::getIssueId).collect(Collectors.toList()));
            skuLotNoList = wmIssueLineList.stream().map(WmIssueLine::getSkuLotNo).collect(Collectors.toList());
        }else{
            return AjaxResult.error("该生产任务暂无领料记录");
        }
        Map<String, WmRtIssueLine> wmIssueLineMap;
        if (Objects.nonNull(reqVo.getRtId())) {
            Long rtId = reqVo.getRtId();
            List<WmRtIssueLine> wmRtIssueLines = wmRtIssueLineService.selectWmRtIssueLineListByRtId(rtId);
            wmIssueLineMap = wmRtIssueLines.stream().collect(Collectors.toMap(line -> (line.getSkuLotNo() + line.getFromLocationCode()), Function.identity(), (v1, v2) -> v1));
        } else {
            wmIssueLineMap = new HashMap<>();
        }
        StockLocationQueryRequest queryRequest = new StockLocationQueryRequest();
        queryRequest.setWarehouseCode(proWorkorder.getWarehouseCode());
        queryRequest.setCargoCode(proWorkorder.getOwnerCode());
        queryRequest.setLocationCodeList(Arrays.asList(mdWorkshop.getAvlLineSideLocationCode(), mdWorkshop.getDamageLineSideLocationCode()));
        queryRequest.setSkuLotNoList(skuLotNoList);
        log.info("查询库存位置信息请求参数：{}", JSON.toJSONString(queryRequest));
        List<StockLocationQueryResponse> responseList = iStockLocationMesQuery.list(queryRequest);
        log.info("库存查询结果：{}", JSON.toJSONString(responseList));
        List<WmRtIssueBomInfoResVo> bomInfoResVoList = responseList.stream().map(response -> {
            WmRtIssueBomInfoResVo resVo = ConvertUtil.beanConvert(response, WmRtIssueBomInfoResVo.class);
            resVo.setItemCode(response.getSkuCode());
            resVo.setItemName(response.getSkuName());
            resVo.setOwnerCode(response.getCargoCode());
            resVo.setOwnerName(response.getCargoName());
            resVo.setWarehouseCode(null);
            resVo.setWarehouseName(null);
            resVo.setZoneCode(null);
            resVo.setZoneName(null);
            resVo.setLocationCode(null);
            resVo.setLocationType(response.getLocationType());
            resVo.setFromLocationCode(response.getLocationCode());
            resVo.setSkuQuality(response.getSkuQuality());
            resVo.setSkuQualityDesc(resVo.getSkuQualityDesc());
            resVo.setInventoryType(response.getInventoryType());
            resVo.setInventoryTypeDesc(response.getInventoryTypeDesc());
            resVo.setSkuLotNo(response.getSkuLotNo());
            resVo.setProduceBatchCode(response.getProductionNo());
            resVo.setPhysicalQty(response.getPhysicalQty());
            resVo.setAvailableQty(response.getAvailableQty());
            String key = resVo.getSkuLotNo() + resVo.getFromLocationCode();
            if (wmIssueLineMap.containsKey(key)) {
                WmRtIssueLine wmRtIssueLine = wmIssueLineMap.get(key);
                resVo.setWarehouseCode(wmRtIssueLine.getWarehouseCode());
                resVo.setWarehouseName(wmRtIssueLine.getWarehouseName());
                resVo.setZoneCode(wmRtIssueLine.getZoneCode());
                resVo.setZoneName(wmRtIssueLine.getZoneName());
                resVo.setLocationCode(wmRtIssueLine.getLocationCode());
                resVo.setQuantityRt(wmRtIssueLine.getQuantityRt());
            }
            return resVo;
        }).collect(Collectors.toList());
        return AjaxResult.success(bomInfoResVoList);
    }

    /**
     * 新增生产退料单头
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:rtissue:add')")
    @Log(title = "生产退料单头", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WmRtIssueAddReqVo reqVo) {
        Long workorderId = reqVo.getWorkorderId();
        Long taskId = reqVo.getTaskId();
        ProWorkorder proWorkorder = proWorkorderService.selectProWorkorderByWorkorderId(workorderId);
        ProTask proTask = proTaskService.selectProTaskByTaskId(taskId);

        WmRtIssue wmRtIssue = ConvertUtil.beanConvert(reqVo, WmRtIssue.class);
        wmRtIssue.setRtCode(autoCodeUtil.genSerialCode(UserConstants.WM_RT_ISSUE_CODE));
        wmRtIssue.setWorkorderId(workorderId);
        wmRtIssue.setWorkorderCode(proWorkorder.getWorkorderCode());
        wmRtIssue.setOwnerCode(proWorkorder.getOwnerCode());
        wmRtIssue.setOwnerName(proWorkorder.getOwnerName());
        wmRtIssue.setWarehouseCode(proWorkorder.getWarehouseCode());
        wmRtIssue.setWarehouseName(proWorkorder.getWarehouseName());
        wmRtIssue.setWorkshopId(proWorkorder.getWorkshopId());
        wmRtIssue.setWorkshopName(proWorkorder.getWorkshopName());
        wmRtIssue.setTaskId(taskId);
        wmRtIssue.setTaskCode(proTask.getTaskCode());
        wmRtIssue.setCreateBy(getUsername());
        int i = wmRtIssueService.insertWmRtIssue(wmRtIssue);

        //插入表体
        List<WmRtIssueLineAddReqVo> lineList = reqVo.getLineList();
        lineList.forEach(l -> {
            WmRtIssueLine wmRtIssueLine = ConvertUtil.beanConvert(l, WmRtIssueLine.class);
            wmRtIssueLine.setRtId(wmRtIssue.getRtId());
            wmRtIssueLineService.insertWmRtIssueLine(wmRtIssueLine);
        });
        return toAjax(i);
    }

    public static void main(String[] args) {
        WmRtIssueAddReqVo wmRtIssueAddReqVo = new WmRtIssueAddReqVo();
        List<WmRtIssueLineAddReqVo> lineList = new ArrayList<>();
        WmRtIssueLineAddReqVo addReqVo = new WmRtIssueLineAddReqVo();

        addReqVo.setItemCode("010015723120700001-8");
        addReqVo.setItemName("nimm2二宝棒棒糖200g-bom-1");
        addReqVo.setQuantityRt(BigDecimal.valueOf(100));
        addReqVo.setWarehouseCode("DT_JYWMS1230");
        addReqVo.setWarehouseName("金义完税仓");
        addReqVo.setLocationCode("tidy-finance-998");
        addReqVo.setLocationName("tidy-finance-998");
        addReqVo.setZoneCode("tidy-finance-998");
        addReqVo.setZoneName("tidy-finance-998");
        lineList.add(addReqVo);

        WmRtIssueLineAddReqVo addReqVo1 = new WmRtIssueLineAddReqVo();
        addReqVo1.setItemCode("010015723120700001-10");
        addReqVo1.setItemName("nimm2二宝棒棒糖200g-bom-2");
        addReqVo1.setQuantityRt(BigDecimal.valueOf(100));
        addReqVo1.setWarehouseCode("DT_JYWMS1230");
        addReqVo1.setWarehouseName("金义完税仓");
        addReqVo1.setLocationCode("tidy-finance-998");
        addReqVo1.setLocationName("tidy-finance-998");
        addReqVo1.setZoneCode("tidy-finance-998");
        addReqVo1.setZoneName("tidy-finance-998");
        lineList.add(addReqVo1);
        wmRtIssueAddReqVo.setLineList(lineList);
        String jsonString = JSON.toJSONString(wmRtIssueAddReqVo);
        System.out.println(jsonString);
    }

    /**
     * 修改生产退料单头
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:rtissue:edit')")
    @Log(title = "生产退料单头", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WmRtIssueEditReqVo reqVo) {
        Long workorderId = reqVo.getWorkorderId();
        Long taskId = reqVo.getTaskId();
        ProWorkorder proWorkorder = proWorkorderService.selectProWorkorderByWorkorderId(workorderId);
        ProTask proTask = proTaskService.selectProTaskByTaskId(taskId);

        WmRtIssue wmRtIssue = new WmRtIssue();
        wmRtIssue.setRtId(reqVo.getRtId());
        wmRtIssue.setWorkorderId(workorderId);
        wmRtIssue.setWorkorderCode(proWorkorder.getWorkorderCode());
        wmRtIssue.setTaskId(taskId);
        wmRtIssue.setTaskCode(proTask.getTaskCode());
        int i = wmRtIssueService.updateWmRtIssue(wmRtIssue);
        wmRtIssueLineService.deleteByRtId(reqVo.getRtId());
        List<WmRtIssueLineAddReqVo> lineList = reqVo.getLineList();
        lineList.forEach(l -> {
            WmRtIssueLine wmRtIssueLine = ConvertUtil.beanConvert(l, WmRtIssueLine.class);
            wmRtIssueLine.setRtId(wmRtIssue.getRtId());
            wmRtIssueLineService.insertWmRtIssueLine(wmRtIssueLine);
        });
        return toAjax(i);
    }

    /**
     * 删除生产退料单头
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:rtissue:remove')")
    @Log(title = "生产退料单头", businessType = BusinessType.DELETE)
    @Transactional
    @DeleteMapping("/{rtIds}")
    public AjaxResult remove(@PathVariable Long[] rtIds) {
        for (Long rtId : rtIds) {
            wmRtIssueLineService.deleteByRtId(rtId);
        }
        return toAjax(wmRtIssueService.deleteWmRtIssueByRtIds(rtIds));
    }

    /**
     * 执行退料
     *
     * @param rtId
     * @return
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:rtissue:edit')")
    @Log(title = "生产退料单头", businessType = BusinessType.UPDATE)
    @Transactional
    @PutMapping("/{rtId}")
    public AjaxResult execute(@PathVariable Long rtId) {
        WmRtIssue rtIssue = wmRtIssueService.selectWmRtIssueByRtId(rtId);
        WmRtIssueLine param = new WmRtIssueLine();
        param.setRtId(rtId);
        List<WmRtIssueLine> lines = wmRtIssueLineService.selectWmRtIssueLineList(param);
        if (CollUtil.isEmpty(lines)) {
            return AjaxResult.error("请选择要退料的物资");
        }
        List<RtIssueTxBean> beans = wmRtIssueService.getTxBeans(rtId);
        for (RtIssueTxBean issueTxBean : beans) {
            if (Objects.isNull(issueTxBean.getTransactionQuantity())) {
                return AjaxResult.error("退料数量未填写");
            }
        }
        beans = beans.stream().filter(bean -> bean.getTransactionQuantity().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        if (!CollectionUtil.isEmpty(beans)) {
            Long workorderId = rtIssue.getWorkorderId();
            ProWorkorder proWorkorder = proWorkorderService.selectProWorkorderByWorkorderId(workorderId);
            Long workShopId = proWorkorder.getWorkshopId();
            MdWorkshop mdWorkshop = mdWorkshopService.selectMdWorkshopByWorkshopId(workShopId);
            MoveAddRequest moveAddRequest = new MoveAddRequest();
            moveAddRequest.setWarehouseCode(proWorkorder.getWarehouseCode());
            moveAddRequest.setCargoCode(proWorkorder.getOwnerCode());
            moveAddRequest.setBillNo(rtIssue.getRtCode());
            List<MoveDetail> moveDetailList = beans.stream().map(bean -> {
                MoveDetail moveDetail = new MoveDetail();
                moveDetail.setSkuCode(bean.getItemCode());
                moveDetail.setSkuLotNo(bean.getSkuLotNo());
                if (Objects.equals(bean.getSkuQuality(), SkuQualityEnum.SKU_QUALITY_AVL.getLevel())) {
                    moveDetail.setOriginLocationCode(mdWorkshop.getAvlLineSideLocationCode());
                } else {
                    moveDetail.setOriginLocationCode(mdWorkshop.getDamageLineSideLocationCode());
                }
                moveDetail.setTargetLocationCode(bean.getLocationCode());
                moveDetail.setMoveQty(bean.getTransactionQuantity());
                return moveDetail;
            }).collect(Collectors.toList());
            moveAddRequest.setDetailList(moveDetailList);
            log.info("moveAddRequest:{}", JSON.toJSONString(moveAddRequest));
            Result<String> result = iMoveMesClient.add(moveAddRequest);
            log.info("move result:{}", JSON.toJSONString(result));
            if (result.checkSuccess()) {
                rtIssue.setRtDate(new Date());
                rtIssue.setWmsOrderNo(result.getData());
            } else {
                String message = result.getMessage();
                log.info("退料失败：{}", message);
                return AjaxResult.error("退料失败：{}" + message);
            }
        }

        rtIssue.setStatus(UserConstants.ORDER_STATUS_FINISHED);
        wmRtIssueService.updateWmRtIssue(rtIssue);
        return AjaxResult.success();
    }

}
