package com.ktg.mes.wm.controller;

import com.alibaba.fastjson.JSON;
import com.ktg.common.annotation.Log;
import com.ktg.common.constant.UserConstants;
import com.ktg.common.core.controller.BaseController;
import com.ktg.common.core.domain.AjaxResult;
import com.ktg.common.core.page.TableDataInfo;
import com.ktg.common.enums.BusinessType;
import com.ktg.common.utils.StringUtils;
import com.ktg.common.utils.poi.ExcelUtil;
import com.ktg.mes.wm.domain.WmArrivalNotice;
import com.ktg.mes.wm.domain.WmArrivalNoticeLine;
import com.ktg.mes.wm.domain.req.WmArrivalNoticeLineBatchEditReqVo;
import com.ktg.mes.wm.domain.req.WmArrivalNoticeLineEditReqVo;
import com.ktg.mes.wm.service.IWmArrivalNoticeLineService;
import com.ktg.mes.wm.service.IWmArrivalNoticeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;

/**
 * 到货通知单行Controller
 * 
 * <AUTHOR>
 * @date 2024-11-12
 */
@Slf4j
@RestController
@RequestMapping("/mes/wm/arrivalnoticeline")
public class WmArrivalNoticeLineController extends BaseController {
    @Autowired
    private IWmArrivalNoticeLineService wmArrivalNoticeLineService;
    @Autowired
    private IWmArrivalNoticeService wmArrivalNoticeService;

    /**
     * 查询到货通知单行列表
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:arrivalnotice:list')")
    @GetMapping("/list")
    public TableDataInfo list(WmArrivalNoticeLine wmArrivalNoticeLine)
    {
        startPage();
        List<WmArrivalNoticeLine> list = wmArrivalNoticeLineService.selectWmArrivalNoticeLineList(wmArrivalNoticeLine);
        return getDataTable(list);
    }

    /**
     * 导出到货通知单行列表
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:arrivalnotice:export')")
    @Log(title = "到货通知单行", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WmArrivalNoticeLine wmArrivalNoticeLine)
    {
        List<WmArrivalNoticeLine> list = wmArrivalNoticeLineService.selectWmArrivalNoticeLineList(wmArrivalNoticeLine);
        ExcelUtil<WmArrivalNoticeLine> util = new ExcelUtil<WmArrivalNoticeLine>(WmArrivalNoticeLine.class);
        util.exportExcel(response, list, "到货通知单行数据");
    }

    /**
     * 获取到货通知单行详细信息
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:arrivalnotice:query')")
    @GetMapping(value = "/{lineId}")
    public AjaxResult getInfo(@PathVariable("lineId") Long lineId)
    {
        return AjaxResult.success(wmArrivalNoticeLineService.selectWmArrivalNoticeLineByLineId(lineId));
    }

    /**
     * 新增到货通知单行
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:arrivalnotice:add')")
    @Log(title = "到货通知单行", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WmArrivalNoticeLine wmArrivalNoticeLine)
    {
        //如果不需要检验，则合格品数量直接=接收数量;否则合格品数量就由对应的检验单负责更新
        if(UserConstants.NO.equals(wmArrivalNoticeLine.getIqcCheck())){
            wmArrivalNoticeLine.setQuantityQuanlified(wmArrivalNoticeLine.getQuantityArrival());
        }
        return toAjax(wmArrivalNoticeLineService.insertWmArrivalNoticeLine(wmArrivalNoticeLine));
    }

    /**
     * 修改到货通知单行
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:arrivalnotice:edit')")
    @Log(title = "到货通知单行", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WmArrivalNoticeLine wmArrivalNoticeLine)
    {
        //如果不需要检验，则合格品数量直接=接收数量;否则合格品数量就由对应的检验单负责更新
        if(UserConstants.NO.equals(wmArrivalNoticeLine.getIqcCheck())){
            wmArrivalNoticeLine.setQuantityQuanlified(wmArrivalNoticeLine.getQuantityArrival());
        }
        return toAjax(wmArrivalNoticeLineService.updateWmArrivalNoticeLine(wmArrivalNoticeLine));
    }

    /**
     * 修改到货通知单行
     */
    @Transactional(rollbackFor = Exception.class)
    @PreAuthorize("@ss.hasPermi('mes:wm:arrivalnotice:edit')")
    @Log(title = "到货通知单表体批量修改", businessType = BusinessType.UPDATE)
    @PostMapping("/editBatch")
    public AjaxResult editBatch(@RequestBody WmArrivalNoticeLineBatchEditReqVo batchEditReqVo) {
        log.info("到货通知单表体批量修改 batchEditReqVo={}", JSON.toJSONString(batchEditReqVo));
        try {
            Long arrivalNoticeId = batchEditReqVo.getArrivalNoticeId();
            WmArrivalNotice wmArrivalNotice = wmArrivalNoticeService.selectWmArrivalNoticeByNoticeId(arrivalNoticeId);
            if (Objects.isNull(wmArrivalNotice)){
                log.info("没有找到对应的来料通知单");
                return AjaxResult.error("没有找到对应的来料通知单");
            }

            List<WmArrivalNoticeLineEditReqVo> list = batchEditReqVo.getList();
            list.forEach(l->{
                WmArrivalNoticeLine wmArrivalNoticeLine = wmArrivalNoticeLineService.selectWmArrivalNoticeLineByLineId(l.getLineId());
                wmArrivalNoticeLine.setTorr(l.getTorr());
                wmArrivalNoticeLine.setTotalWeight(l.getTotalWeight());
                wmArrivalNoticeLine.setSkuWarp(l.getSkuWarp());
                wmArrivalNoticeLine.setErrorRemark(l.getErrorRemark());
                wmArrivalNoticeLine.setErrorAttach(l.getErrorAttach());
                if (!StringUtils.isEmpty(l.getAttachList())){
                    wmArrivalNoticeLine.setAttachListJson(l.getAttachList());
                }
                wmArrivalNoticeLineService.updateWmArrivalNoticeLine(wmArrivalNoticeLine);
            });
            return AjaxResult.success("到货通知单表体批量修改成功");
        }catch (Exception e){
            log.error("到货通知单表体批量修改失败", e);
            return AjaxResult.error("到货通知单表体批量修改失败");
        }

    }

    /**
     * 删除到货通知单行
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:arrivalnotice:remove')")
    @Log(title = "到货通知单行", businessType = BusinessType.DELETE)
	@DeleteMapping("/{lineIds}")
    public AjaxResult remove(@PathVariable Long[] lineIds)
    {
        return toAjax(wmArrivalNoticeLineService.deleteWmArrivalNoticeLineByLineIds(lineIds));
    }
}
