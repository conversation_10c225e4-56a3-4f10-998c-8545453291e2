package com.ktg.mes.wm.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.dt.component.common.enums.sku.InventoryTypeEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.platform.wms.rpc.client.mes.basis.IWarehouseBasisMesRpcClient;
import com.dt.platform.wms.rpc.client.mes.basis.param.LocationMesRpcParam;
import com.dt.platform.wms.rpc.client.mes.common.Result;
import com.dt.platform.wms.rpc.client.mes.move.*;
import com.dt.platform.wms.rpc.client.mes.stock.IStockLocationMesQuery;
import com.dt.platform.wms.rpc.client.mes.stock.StockLocationQueryRequest;
import com.dt.platform.wms.rpc.client.mes.stock.StockLocationQueryResponse;
import com.ktg.common.annotation.Log;
import com.ktg.common.constant.UserConstants;
import com.ktg.common.core.controller.BaseController;
import com.ktg.common.core.domain.AjaxResult;
import com.ktg.common.core.page.TableDataInfo;
import com.ktg.common.enums.BusinessType;
import com.ktg.common.utils.ConvertUtil;
import com.ktg.common.utils.poi.ExcelUtil;
import com.ktg.mes.md.domain.MdWorkshop;
import com.ktg.mes.md.service.IMdWorkshopService;
import com.ktg.mes.pro.domain.ProTask;
import com.ktg.mes.pro.domain.ProWorkorder;
import com.ktg.mes.pro.service.IProTaskService;
import com.ktg.mes.pro.service.IProWorkorderService;
import com.ktg.mes.wm.domain.WmProductRecpt;
import com.ktg.mes.wm.domain.WmProductRecptLine;
import com.ktg.mes.wm.domain.req.*;
import com.ktg.mes.wm.domain.res.*;
import com.ktg.mes.wm.domain.tx.ProductRecptTxBean;
import com.ktg.mes.wm.service.*;
import com.ktg.system.strategy.AutoCodeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 产品入库录Controller
 *
 * <AUTHOR>
 * @menu 生产入库
 * @date 2022-09-22
 */
@Slf4j
@RestController
@RequestMapping("/mes/wm/productrecpt")
public class WmProductRecptController extends BaseController {
    @Autowired
    private IWmProductRecptService wmProductRecptService;

    @Autowired
    private IWmProductRecptLineService wmProductRecptLineService;

    @Autowired
    private IWmWarehouseService wmWarehouseService;

    @Autowired
    private IWmStorageLocationService wmStorageLocationService;

    @Autowired
    private IWmStorageAreaService wmStorageAreaService;

    @Autowired
    private IStorageCoreService storageCoreService;

    @Autowired
    private AutoCodeUtil autoCodeUtil;

    @Autowired
    private IProWorkorderService proWorkorderService;

    @Autowired
    private IProTaskService proTaskService;

    @Autowired
    private IMdWorkshopService mdWorkshopService;

    @DubboReference
    private IMoveMesClient iMoveMesClient;

    @DubboReference
    private IMoveMesQuery iMoveMesQuery;

    @DubboReference
    private IStockLocationMesQuery iStockLocationMesQuery;

    @DubboReference
    private IWarehouseBasisMesRpcClient iWarehouseBasisMesRpcClient;

    /**
     * 查询产品入库录列表
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:productrecpt:list')")
    @GetMapping("/list")
    public TableDataInfo list(WmProductRecpReqVo wmProductRecpt) {
        startPage();
        List<WmProductRecpt> list = wmProductRecptService.selectWmProductRecptListEx(wmProductRecpt);
        TableDataInfo dataTable = getDataTable(list);
        Map<String, String> orderStatusMap = new HashMap<>(32);
        Map<String, List<WmProductRecpt>> warehouseRecptMap = list.stream().filter(wpr->Objects.nonNull(wpr.getWarehouseCode()))
                .collect(Collectors.groupingBy(WmProductRecpt::getWarehouseCode));
        warehouseRecptMap.forEach((code, issueHeaders) -> {
            MoveQueryRequest moveQueryRequest = new MoveQueryRequest();
            moveQueryRequest.setWarehouseCode(code);
            List<String> wmsOrderList = issueHeaders.stream().map(WmProductRecpt::getWmsOrderNo).collect(Collectors.toList());
            moveQueryRequest.setCodeList(wmsOrderList);
            log.info("查询移位单参数：{}", JSON.toJSONString(moveQueryRequest));
            Result<List<MoveQueryResponse>> listResult = iMoveMesQuery.list(moveQueryRequest);
            log.info("查询移位单返回结果：{}", JSON.toJSONString(listResult));
            List<MoveQueryResponse> data = listResult.getData();
            data.forEach(moveQueryResponse -> orderStatusMap.put(moveQueryResponse.getCode(), moveQueryResponse.getStatusDesc()));
        });
        List<WmProductRecptListResVo> resVoList = list.stream().map(issueHeader -> {
            WmProductRecptListResVo wmRtIssueListResVo = ConvertUtil.beanConvert(issueHeader, WmProductRecptListResVo.class);
            if (orderStatusMap.containsKey(wmRtIssueListResVo.getWmsOrderNo())) {
                wmRtIssueListResVo.setWmsOrderStatus(orderStatusMap.get(wmRtIssueListResVo.getWmsOrderNo()));
            }
            return wmRtIssueListResVo;
        }).collect(Collectors.toList());

        Map<Long, String> workshopNameMap = new HashMap<>();
        Map<Long, BigDecimal> recptIdMap = new HashMap<>();
        if (CollUtil.isNotEmpty(resVoList)) {
            // 车间名称复制
            List<Long> workShopIds = list.stream().map(WmProductRecpt::getWorkorderId).collect(Collectors.toList());
            List<ProWorkorder> workorderList = proWorkorderService.selectProWorkorderByWorkorderIds(workShopIds);
            if (CollUtil.isNotEmpty(workorderList)) {
                workshopNameMap = workorderList.stream().collect(Collectors.toMap(ProWorkorder::getWorkorderId, ProWorkorder::getWorkshopName));
            }

            // 入库数量入库数量
            List<Long> recptIdList = resVoList.stream().map(WmProductRecptListResVo::getRecptId).collect(Collectors.toList());
            List<WmProductRecptLine> recptLineList = wmProductRecptLineService.selectWmProductRecptLineByRecptIds(recptIdList);
            if (CollUtil.isNotEmpty(recptLineList)) {
                recptIdMap = recptLineList.stream()
                        .collect(Collectors.groupingBy(WmProductRecptLine::getRecptId,
                                Collectors.reducing(
                                        BigDecimal.ZERO,
                                        WmProductRecptLine::getQuantityRecived,
                                        BigDecimal::add
                                )));
            }
        }

        // 填充数据
        for (WmProductRecptListResVo resVo : resVoList) {
            resVo.setWorkshopName(workshopNameMap.get(resVo.getWorkorderId()));
            BigDecimal opsNum = recptIdMap.get(resVo.getRecptId());
            if (opsNum != null) {
                resVo.setOpsNum(opsNum.longValue());
            }
        }
        dataTable.setRows(resVoList);
        return dataTable;
    }

    /**
     * 导出产品入库录列表
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:productrecpt:export')")
    @Log(title = "产品入库记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WmProductRecpt wmProductRecpt) {
        List<WmProductRecpt> list = wmProductRecptService.selectWmProductRecptList(wmProductRecpt);
        ExcelUtil<WmProductRecpt> util = new ExcelUtil<WmProductRecpt>(WmProductRecpt.class);
        util.exportExcel(response, list, "产品入库录数据");
    }

    /**
     * 获取产品入库录详细信息
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:productrecpt:query')")
    @GetMapping(value = "/{recptId}")
    public AjaxResult getInfo(@PathVariable("recptId") Long recptId) {
        WmProductRecpt wmProductRecpt = wmProductRecptService.selectWmProductRecptByRecptId(recptId);
        List<WmProductRecptLine> wmProductRecptLineList = wmProductRecptLineService.selectWmProductRecptLineListByRecptId(recptId);
        WmProductRecptResVo recptResVo = ConvertUtil.beanConvert(wmProductRecpt, WmProductRecptResVo.class);
        WmProductRecptDetailInfoResVo wmProductRecptDetailInfoResVo = new WmProductRecptDetailInfoResVo();
        wmProductRecptDetailInfoResVo.setWmProductRecptResVo(recptResVo);
        List<WmPrProductRecptLineResVo> lineResVos = wmProductRecptLineList.stream().map(line -> {
            WmPrProductRecptLineResVo lineResVo = ConvertUtil.beanConvert(line, WmPrProductRecptLineResVo.class);
            if (Objects.nonNull(line.getSkuQuality())) {
                lineResVo.setSkuQualityDesc(SkuQualityEnum.getEnum(line.getSkuQuality()).getMessage());
            }
            if (Objects.nonNull(line.getInventoryType())) {
                lineResVo.setInventoryTypeDesc(InventoryTypeEnum.getEnum(line.getInventoryType()).getMessage());
            }
            return lineResVo;
        }).collect(Collectors.toList());
        wmProductRecptDetailInfoResVo.setLineResVoList(lineResVos);
        return AjaxResult.success(wmProductRecptDetailInfoResVo);
    }

    /**
     * 查询库存
     *
     * @param workorderId
     * @return
     */
    @GetMapping("/getStockInfo")
    public AjaxResult getStockInfo(WmProductRecptStockInfoQueryReqVo reqVo)  {
        Long taskId = reqVo.getTaskId();
        ProTask proTask = proTaskService.selectProTaskByTaskId(taskId);
        ProWorkorder proWorkorder = proWorkorderService.selectProWorkorderByWorkorderId(proTask.getWorkorderId());
        Long workShopId = proWorkorder.getWorkshopId();
        MdWorkshop mdWorkshop = mdWorkshopService.selectMdWorkshopByWorkshopId(workShopId);
        Long recptId = reqVo.getRecptId();
        Map<String, WmProductRecptLine> keyRecptMap ;
        if (Objects.nonNull(recptId)){
            List<WmProductRecptLine> wmProductRecptLineList = wmProductRecptLineService.selectWmProductRecptLineListByRecptId(recptId);
            keyRecptMap= wmProductRecptLineList.stream().collect(Collectors.toMap(line -> (line.getSkuLotNo() + line.getFromLocationCode()), Function.identity(), (v1, v2) -> v1));
        }else{
            keyRecptMap = new HashMap<>();
        }
        StockLocationQueryRequest queryRequest = new StockLocationQueryRequest();
        queryRequest.setWarehouseCode(proWorkorder.getWarehouseCode());
        // 编码code
        queryRequest.setSkuCode(proWorkorder.getProductCode());
        // 生产批号
        queryRequest.setProductionNo(proTask.getProduceBatchCode());
        // 正次品线边库区
        queryRequest.setLocationCodeList(Arrays.asList(mdWorkshop.getAvlLineSideLocationCode(), mdWorkshop.getDamageLineSideLocationCode()));
        queryRequest.setExternalLinkBillNo(proTask.getTaskCode());
        log.info("查询参数：{}", JSON.toJSONString(queryRequest));
        List<StockLocationQueryResponse> responseList = iStockLocationMesQuery.list(queryRequest);
        log.info("查询结果：{}", JSON.toJSONString(responseList));
        //获取库位
        LocationMesRpcParam mesRpcParam = new LocationMesRpcParam();
        mesRpcParam.setWarehouseCode(proWorkorder.getWarehouseCode());
        List<WmProductRecptStockResVo> resVoList = responseList.stream().map(r -> {
            WmProductRecptStockResVo resVo = new WmProductRecptStockResVo();
            resVo.setTaskCode(proTask.getTaskCode());
            resVo.setSkuCode(r.getSkuCode());
            resVo.setSkuName(r.getSkuName());
            resVo.setSkuLotNo(r.getSkuLotNo());
            resVo.setSkuQuality(r.getSkuQuality());
            resVo.setSkuQualityDesc(r.getSkuQualityDesc());
            resVo.setInventoryType(r.getInventoryType());
            resVo.setInventoryTypeDesc(r.getInventoryTypeDesc());
            resVo.setProduceBatchCode(r.getProductionNo());
            resVo.setFromLocationCode(r.getLocationCode());
            resVo.setAvailableQty(r.getAvailableQty());
            String key = resVo.getSkuLotNo() + resVo.getFromLocationCode();
            if (keyRecptMap.containsKey(key)){
                WmProductRecptLine line = keyRecptMap.get(key);
                resVo.setQuantityRecived(line.getQuantityRecived());
                resVo.setWarehouseCode(line.getWarehouseCode());
                resVo.setWarehouseName(line.getWarehouseName());
                resVo.setZoneCode(line.getZoneCode());
                resVo.setZoneName(line.getZoneName());
                resVo.setLocationCode(line.getLocationCode());
            }
            return resVo;
        }).collect(Collectors.toList());
        return AjaxResult.success(resVoList);
    }

    /**
     * 新增产品入库录
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:productrecpt:add')")
    @Log(title = "产品入库记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WmProductRecptAddReqVo reqVo) {
        WmProductRecpt wmProductRecpt = ConvertUtil.beanConvert(reqVo, WmProductRecpt.class);
//        if(UserConstants.NOT_UNIQUE.equals(wmProductRecptService.checkUnique(wmProductRecpt))){
//            return AjaxResult.error("入库单编号已存在！");
//        }
        ProWorkorder proWorkorder = proWorkorderService.selectProWorkorderByWorkorderId(reqVo.getWorkorderId());
        if (Objects.isNull(proWorkorder)){
            return AjaxResult.error("生产工单不存在");
        }
        ProTask proTask = proTaskService.selectProTaskByTaskId(reqVo.getTaskId());
        if (Objects.isNull(proTask)){
            return AjaxResult.error("生产任务不存在");
        }
        wmProductRecpt.setWorkorderCode(proWorkorder.getWorkorderCode());
        wmProductRecpt.setTaskCode(proTask.getTaskCode());
        wmProductRecpt.setRecptCode(autoCodeUtil.genSerialCode(UserConstants.WM_PRODUCT_RECPT_CODE));
        wmProductRecpt.setCreateBy(getUsername());
        wmProductRecpt.setItemCode(proTask.getItemCode());
        wmProductRecpt.setItemId(proTask.getItemId());
        wmProductRecpt.setItemName(proTask.getItemName());
        int i = wmProductRecptService.insertWmProductRecpt(wmProductRecpt);
        List<WmProductRecptLineAddReqVo> lineList = reqVo.getLineList();
        lineList.forEach(l -> {
            WmProductRecptLine wmProductRecptLine = ConvertUtil.beanConvert(l, WmProductRecptLine.class);
            wmProductRecptLine.setItemCode(l.getSkuCode());
            wmProductRecptLine.setItemName(l.getSkuName());
            wmProductRecptLine.setRecptId(wmProductRecpt.getRecptId());
            wmProductRecptLine.setQuantityRecived(l.getQuantityRecived());
            wmProductRecptLine.setFromLocationCode(l.getFromLocationCode());
            wmProductRecptLineService.insertWmProductRecptLine(wmProductRecptLine);
        });
        return toAjax(i);
    }

    /**
     * 修改产品入库录
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:productrecpt:edit')")
    @Log(title = "产品入库记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WmProductRecptEditReqVo reqVo) {
//        WmProductRecpt wmProductRecpt = ConvertUtil.beanConvert(reqVo, WmProductRecpt.class);
        Long recptId = reqVo.getRecptId();
        WmProductRecpt wmProductRecpt = wmProductRecptService.selectWmProductRecptByRecptId(recptId);
        wmProductRecpt.setWorkorderId(reqVo.getWorkorderId());
        ProWorkorder proWorkorder = proWorkorderService.selectProWorkorderByWorkorderId(reqVo.getWorkorderId());
//        wmProductRecpt.setWorkorderCode(proWorkorder.getWorkorderCode());
//        wmProductRecpt.setWorkorderName(proWorkorder.getWorkorderName());
        ProTask proTask = proTaskService.selectProTaskByTaskId(reqVo.getTaskId());
        wmProductRecpt.setTaskId(proTask.getTaskId());
        wmProductRecpt.setTaskCode(proTask.getTaskCode());
        wmProductRecpt.setItemCode(proWorkorder.getProductCode());
        wmProductRecpt.setItemName(proWorkorder.getProductName());
        wmProductRecpt.setUpdateBy(getUsername());
        wmProductRecpt.setUpdateTime(new Date());
        int i = wmProductRecptService.updateWmProductRecpt(wmProductRecpt);
        wmProductRecptLineService.deleteByRecptId(recptId);
        List<WmProductRecptLineAddReqVo> lineList = reqVo.getLineList();
        lineList.forEach(l -> {
            WmProductRecptLine wmProductRecptLine = ConvertUtil.beanConvert(l, WmProductRecptLine.class);
            wmProductRecptLine.setItemCode(l.getSkuCode());
            wmProductRecptLine.setItemName(l.getSkuName());
            wmProductRecptLine.setRecptId(wmProductRecpt.getRecptId());
            wmProductRecptLine.setQuantityRecived(l.getQuantityRecived());
            wmProductRecptLineService.insertWmProductRecptLine(wmProductRecptLine);
        });
        return toAjax(i);
    }

    /**
     * 删除产品入库录
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:productrecpt:remove')")
    @Log(title = "产品入库记录", businessType = BusinessType.DELETE)
    @Transactional
    @DeleteMapping("/{recptIds}")
    public AjaxResult remove(@PathVariable Long[] recptIds) {
        for (Long recptId : recptIds) {
            wmProductRecptLineService.deleteByRecptId(recptId);
        }
        return toAjax(wmProductRecptService.deleteWmProductRecptByRecptIds(recptIds));
    }

    /**
     * 执行入库
     *
     * @return
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:productrecpt:edit')")
    @Log(title = "产品入库记录", businessType = BusinessType.UPDATE)
    @Transactional
    @PutMapping("/{recptId}")
    public AjaxResult execute(@PathVariable Long recptId) {
        WmProductRecpt recpt = wmProductRecptService.selectWmProductRecptByRecptId(recptId);

        WmProductRecptLine param = new WmProductRecptLine();
        param.setRecptId(recptId);
        List<WmProductRecptLine> lines = wmProductRecptLineService.selectWmProductRecptLineList(param);
        if (CollUtil.isEmpty(lines)) {
            return AjaxResult.error("请添加要入库的产品");
        }
        ProTask proTask = proTaskService.selectProTaskByTaskId(recpt.getTaskId());

        List<WmProductRecpt> wmProductRecpts = wmProductRecptService.selectWmProductRecptListByTaskId(Collections.singletonList(recpt.getTaskId()));
        if (!CollectionUtils.isEmpty(wmProductRecpts)){
            List<Long> recptIdList = wmProductRecpts.stream().map(WmProductRecpt::getRecptId).filter(id ->!Objects.equals(id,recptId)).distinct().collect(Collectors.toList());
            List<WmProductRecptLine> wmProductRecptLineList = wmProductRecptLineService.selectWmProductRecptLineByRecptIds(recptIdList);
            if (!CollectionUtils.isEmpty(wmProductRecptLineList)){
                BigDecimal totalReceived = wmProductRecptLineList.stream().map(WmProductRecptLine::getQuantityRecived).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal thisTimeReceived = lines.stream().map(WmProductRecptLine::getQuantityRecived).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal allReceived = totalReceived.add(thisTimeReceived);
                if (allReceived.compareTo(proTask.getQuantityProduced())>0){
                    return AjaxResult.error("实际可转移数量无法满足，请检查！");
                }
            }
        }
        Long workorderId = recpt.getWorkorderId();
        ProWorkorder proWorkorder = proWorkorderService.selectProWorkorderByWorkorderId(workorderId);
        MdWorkshop mdWorkshop = mdWorkshopService.selectMdWorkshopByWorkshopId(proWorkorder.getWorkshopId());
        List<ProductRecptTxBean> beans = wmProductRecptService.getTxBean(recptId);
        MoveAddRequest moveAddRequest = new MoveAddRequest();
        moveAddRequest.setWarehouseCode(proWorkorder.getWarehouseCode());
        moveAddRequest.setCargoCode(proWorkorder.getOwnerCode());
        moveAddRequest.setBillNo(recpt.getRecptCode());
        List<MoveDetail> moveDetailList = beans.stream().map(bean -> {
            MoveDetail moveDetail = new MoveDetail();
            moveDetail.setSkuCode(bean.getItemCode());
            moveDetail.setSkuLotNo(bean.getSkuLotNo());
//            moveDetail.setOriginLocationCode();
//            if (Objects.equals(bean.get())
            if (Objects.equals(bean.getSkuQuality(), SkuQualityEnum.SKU_QUALITY_AVL.getLevel())) {
                moveDetail.setOriginLocationCode(mdWorkshop.getAvlLineSideLocationCode());
            } else {
                moveDetail.setOriginLocationCode(mdWorkshop.getDamageLineSideLocationCode());
            }
            moveDetail.setTargetLocationCode(bean.getLocationCode());
            moveDetail.setMoveQty(bean.getTransactionQuantity());
            return moveDetail;
        }).collect(Collectors.toList());
        moveAddRequest.setDetailList(moveDetailList);
        log.info("moveAddRequest:{}", JSON.toJSONString(moveAddRequest));
        try {
            Result<String> result = iMoveMesClient.add(moveAddRequest);
            log.info("move result:{}", JSON.toJSONString(result));
            if (result.checkSuccess()) {
                recpt.setWmsOrderNo(result.getData());
            } else {
                return AjaxResult.error("转移单创建失败：{}" + result.getMessage());
            }
        } catch (Exception e) {
            log.error("转移单创建失败：{}", e.getMessage(), e);
            return AjaxResult.error("转移单创建失败：" + e.getMessage());
        }
        recpt.setStatus(UserConstants.ORDER_STATUS_FINISHED);
        wmProductRecptService.updateWmProductRecpt(recpt);

        return AjaxResult.success();
    }

}
