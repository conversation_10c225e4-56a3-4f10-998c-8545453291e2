package com.ktg.mes.wm.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.dt.component.common.enums.base.LocationTypeEnum;
import com.dt.component.common.enums.sku.InventoryTypeEnum;
import com.dt.component.common.enums.sku.SkuQualityEnum;
import com.dt.platform.wms.rpc.client.mes.common.Result;
import com.dt.platform.wms.rpc.client.mes.move.*;
import com.dt.platform.wms.rpc.client.mes.stock.IStockLocationMesQuery;
import com.dt.platform.wms.rpc.client.mes.stock.StockLocationQueryRequest;
import com.dt.platform.wms.rpc.client.mes.stock.StockLocationQueryResponse;
import com.ktg.common.annotation.Log;
import com.ktg.common.constant.UserConstants;
import com.ktg.common.core.controller.BaseController;
import com.ktg.common.core.domain.AjaxResult;
import com.ktg.common.core.page.TableDataInfo;
import com.ktg.common.enums.BusinessType;
import com.ktg.common.enums.OrderStatusEnum;
import com.ktg.common.enums.ProductRequirementEnum;
import com.ktg.common.utils.ConvertUtil;
import com.ktg.common.utils.StringUtils;
import com.ktg.common.utils.poi.ExcelUtil;
import com.ktg.mes.md.domain.MdWorkshop;
import com.ktg.mes.md.service.IMdWorkshopService;
import com.ktg.mes.pro.domain.ProTask;
import com.ktg.mes.pro.domain.ProWorkorder;
import com.ktg.mes.pro.domain.ProWorkorderBom;
import com.ktg.mes.pro.domain.res.ProWorkOrderKeyMdItemResVo;
import com.ktg.mes.pro.service.IProTaskService;
import com.ktg.mes.pro.service.IProWorkorderBomService;
import com.ktg.mes.pro.service.IProWorkorderService;
import com.ktg.mes.wm.domain.WmIssueHeader;
import com.ktg.mes.wm.domain.WmIssueLine;
import com.ktg.mes.wm.domain.req.*;
import com.ktg.mes.wm.domain.res.*;
import com.ktg.mes.wm.domain.tx.IssueTxBean;
import com.ktg.mes.wm.service.IWmIssueHeaderService;
import com.ktg.mes.wm.service.IWmIssueLineService;
import com.ktg.system.strategy.AutoCodeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 生产领料单头Controller
 *
 * <AUTHOR>
 * @menu 生产领料
 * @date 2022-07-14
 */
@Slf4j
@RestController
@RequestMapping("/mes/wm/issueheader")
public class WmIssueHeaderController extends BaseController {
    @Autowired
    private IWmIssueHeaderService wmIssueHeaderService;

    @Autowired
    private IWmIssueLineService wmIssueLineService;

    @Autowired
    private IProWorkorderService proWorkorderService;

    @Autowired
    private IProWorkorderBomService proWorkorderBomService;

    @Autowired
    private IProTaskService proTaskService;

    @Autowired
    private AutoCodeUtil autoCodeUtil;

    @DubboReference
    private IMoveMesClient iMoveMesClient;
    @DubboReference
    private IMoveMesQuery iMoveMesQuery;

    @Autowired
    private IMdWorkshopService mdWorkshopService;

    @DubboReference
    private IStockLocationMesQuery iStockLocationMesQuery;

    /**
     * 查询生产领料单头列表
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:issueheader:list')")
    @GetMapping("/list")
    public TableDataInfo list(WmIssueListReqVo reqVo) {
        startPage();
        List<WmIssueHeader> list = wmIssueHeaderService.wmIssueHeaderPage(reqVo);
        TableDataInfo dataTable = getDataTable(list);
        Map<String, String> orderStatusMap = new HashMap<>(32);
        Map<String, List<WmIssueHeader>> warehouseIssueMap = list.stream().filter(issue -> Objects.nonNull(issue.getWarehouseCode())).collect(Collectors.groupingBy(WmIssueHeader::getWarehouseCode));
        warehouseIssueMap.forEach((code, issueHeaders) -> {
            MoveQueryRequest moveQueryRequest = new MoveQueryRequest();
            moveQueryRequest.setWarehouseCode(code);
            List<String> wmsOrderList = issueHeaders.stream().map(WmIssueHeader::getWmsOrderNo).collect(Collectors.toList());
            moveQueryRequest.setCodeList(wmsOrderList);
            Result<List<MoveQueryResponse>> listResult = iMoveMesQuery.list(moveQueryRequest);
            List<MoveQueryResponse> data = listResult.getData();
            data.forEach(moveQueryResponse -> orderStatusMap.put(moveQueryResponse.getCode(), moveQueryResponse.getStatusDesc()));
        });
        List<WmIssueListResVo> resVoList = list.stream().map(issueHeader -> {
            WmIssueListResVo wmIssueListResVo = ConvertUtil.beanConvert(issueHeader, WmIssueListResVo.class);
            if (orderStatusMap.containsKey(wmIssueListResVo.getWmsOrderNo())) {
                wmIssueListResVo.setWmsOrderStatus(orderStatusMap.get(wmIssueListResVo.getWmsOrderNo()));
            }
            return wmIssueListResVo;
        }).collect(Collectors.toList());
        dataTable.setRows(resVoList);
        return dataTable;
    }

    /**
     * 导出生产领料单头列表
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:issueheader:export')")
    @Log(title = "生产领料单头", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WmIssueHeader wmIssueHeader) {
        List<WmIssueHeader> list = wmIssueHeaderService.selectWmIssueHeaderList(wmIssueHeader);
        ExcelUtil<WmIssueHeader> util = new ExcelUtil<WmIssueHeader>(WmIssueHeader.class);
        util.exportExcel(response, list, "生产领料单头数据");
    }

    /**
     * 获取生产领料单头详细信息
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:issueheader:query')")
    @GetMapping(value = "/{issueId}")
    public AjaxResult getInfo(@PathVariable("issueId") Long issueId) {
        WmIssueHeader wmIssueHeader = wmIssueHeaderService.selectWmIssueHeaderByIssueId(issueId);
        if (Objects.isNull(wmIssueHeader)) {
            return AjaxResult.error("未找到对应数据");
        }
        WmIssueResVo wmIssueResVo = ConvertUtil.beanConvert(wmIssueHeader, WmIssueResVo.class);
        List<WmIssueLine> lineList = wmIssueLineService.selectWmIssueLineByIssueId(issueId);
        List<WmIssueLineResVo> wmIssueLineResVos = lineList.stream().map(line -> {
            WmIssueLineResVo wmIssueLineResVo = ConvertUtil.beanConvert(line, WmIssueLineResVo.class);
            if (Objects.nonNull(line.getSkuQuality())) {
                wmIssueLineResVo.setSkuQualityDesc(SkuQualityEnum.getEnum(line.getSkuQuality()).getMessage());
            }
            if (Objects.nonNull(line.getInventoryType())) {
                wmIssueLineResVo.setInventoryTypeDesc(InventoryTypeEnum.getEnum(line.getInventoryType()).getMessage());
            }
            return wmIssueLineResVo;
        }).collect(Collectors.toList());
        WmIssueDetailInfoResVo wmIssueDetailInfoResVo = new WmIssueDetailInfoResVo();
        wmIssueDetailInfoResVo.setWmIssueResVo(wmIssueResVo);
        wmIssueDetailInfoResVo.setLineResVoList(wmIssueLineResVos);
        return AjaxResult.success(wmIssueDetailInfoResVo);
    }

    /**
     * 查看bomInfo
     *
     * @param reqVo
     * @return
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:issueheader:bomInfo')")
    @GetMapping("/bomInfo")
    public AjaxResult bomInfo(WmIssueBomInfoReqVo reqVo) {
        Long workorderId = reqVo.getWorkorderId();
        ProWorkorder proWorkorder = proWorkorderService.selectProWorkorderByWorkorderId(workorderId);
        if (Objects.isNull(proWorkorder)) {
            return AjaxResult.error("生产工单不存在");
        }
        ProWorkorderBom proWorkorderBom = new ProWorkorderBom();
        proWorkorderBom.setWorkorderId(workorderId);
        List<ProWorkorderBom> proWorkorderBoms = proWorkorderBomService.selectProWorkorderBomList(proWorkorderBom);
        if (CollectionUtils.isEmpty(proWorkorderBoms)) {
            return AjaxResult.error("生产工单BOM不存在");
        }
        Map<String, WmIssueLine> wmIssueLineMap;
        if (Objects.nonNull(reqVo.getIssueId())) {
            Long issueId = reqVo.getIssueId();
            List<WmIssueLine> wmIssueLines = wmIssueLineService.selectWmIssueLineByIssueId(issueId);
            wmIssueLineMap = wmIssueLines.stream().collect(Collectors.toMap(line -> (line.getSkuLotNo() + line.getLocationCode()), Function.identity(), (v1, v2) -> v1));
        } else {
            wmIssueLineMap = new HashMap<>();
        }
        Map<String, List<ProWorkOrderKeyMdItemResVo>> keyMdItemResVoMap;
        String keyItems = proWorkorder.getKeyItems();
        if (Objects.nonNull(keyItems)) {
            List<ProWorkOrderKeyMdItemResVo> mdItemResVos = JSON.parseArray(proWorkorder.getKeyItems(), ProWorkOrderKeyMdItemResVo.class);
            keyMdItemResVoMap = mdItemResVos.stream().collect(Collectors.groupingBy(ProWorkOrderKeyMdItemResVo::getItemCode));
        } else {
            keyMdItemResVoMap = new HashMap<>(8);
        }
        StockLocationQueryRequest queryRequest = new StockLocationQueryRequest();
        queryRequest.setWarehouseCode(proWorkorder.getWarehouseCode());
        queryRequest.setCargoCode(proWorkorder.getOwnerCode());
        queryRequest.setSkuCodeList(proWorkorderBoms.stream().map(ProWorkorderBom::getItemCode).distinct().collect(Collectors.toList()));
        queryRequest.setSkuQuality(SkuQualityEnum.SKU_QUALITY_AVL.getLevel());
        queryRequest.setLocationTypeList(ListUtil.toList(LocationTypeEnum.LOCATION_TYPE_PICK.getType(), LocationTypeEnum.LOCATION_TYPE_STORE.getType()));
        log.info("查询库存位置信息请求参数：{}", JSON.toJSONString(queryRequest));
        List<StockLocationQueryResponse> responseList = iStockLocationMesQuery.list(queryRequest);
        log.info("库存查询结果：{}", JSON.toJSONString(responseList));
//        Map<String, List<StockLocationQueryResponse>> itemCodeStockMap = responseList.stream()
//                .filter(stock -> Objects.nonNull(stock.getZoneAttr()))
//                .collect(Collectors.groupingBy(StockLocationQueryResponse::getSkuCode));
        List<WmIssueBomInfoResVo> bomInfoResVoList = responseList.stream()
                .filter(stock -> Objects.isNull(stock.getZoneAttr()) || StringUtils.isEmpty(stock.getZoneAttr()))
                .filter(stock ->{
                    if (keyMdItemResVoMap.containsKey(stock.getSkuCode())) {
                        List<ProWorkOrderKeyMdItemResVo> proWorkOrderKeyMdItemResVos = keyMdItemResVoMap.get(stock.getSkuCode());
                        for (ProWorkOrderKeyMdItemResVo keyMdItemResVo : proWorkOrderKeyMdItemResVos) {
                            log.info("关键原材料信息 itemCode={} keyMdItemResVo={}", stock.getSkuCode(), JSON.toJSONString(keyMdItemResVo));
                            if (Objects.equals(keyMdItemResVo.getProduceRequirement(), ProductRequirementEnum.PRODUCT_BATCH_CODE.getCode())) {
                                String produceBatchCode = keyMdItemResVo.getSpecificInformation();
                                if (!Objects.equals(produceBatchCode, stock.getProductionNo())) {
                                    log.info("sku:{} 指定生产批次号：{} 与库存批次号不一致：{}", stock.getSkuCode(), keyMdItemResVo.getSpecificInformation(), stock.getProductionNo());
                                    return false;
                                }
                            }
                            if (Objects.equals(keyMdItemResVo.getProduceRequirement(), ProductRequirementEnum.PRODUCT_DATE.getCode())) {
                                Long produceDate = Long.valueOf(keyMdItemResVo.getSpecificInformation());
                                if (!Objects.equals(produceDate, stock.getManufDate())) {
                                    log.info("sku:{} 创建时间：{} 与库存创建时间不一致：{}", stock.getSkuCode(), keyMdItemResVo.getSpecificInformation(), stock.getManufDate());
                                    return false;
                                }
                            }
                            if (Objects.equals(keyMdItemResVo.getProduceRequirement(), ProductRequirementEnum.EXTERNAL_LINK_BILL_NO.getCode())) {
                                String externalLinkBillNo = keyMdItemResVo.getSpecificInformation();
                                if (!Objects.equals(externalLinkBillNo, stock.getExternalLinkBillNo())) {
                                    log.info("sku:{} 入库关联单号：{} 与库存入库关联单号不一致：{}", stock.getSkuCode(), keyMdItemResVo.getSpecificInformation(), stock.getExternalLinkBillNo());
                                    return false;
                                }
                            }
                        }

                    }
                    return true;
                })
                .map(response -> {
                    WmIssueBomInfoResVo resVo = ConvertUtil.beanConvert(response, WmIssueBomInfoResVo.class);
                    resVo.setItemCode(response.getSkuCode());
                    resVo.setItemName(response.getSkuName());
                    resVo.setOwnerCode(response.getCargoCode());
                    resVo.setOwnerName(response.getCargoName());
                    resVo.setWarehouseCode(response.getWarehouseCode());
                    resVo.setWarehouseName(response.getWarehouseName());
                    resVo.setZoneCode(response.getZoneCode());
                    resVo.setZoneName(response.getZoneName());
                    resVo.setLocationCode(response.getLocationCode());
                    resVo.setLocationType(response.getLocationType());
                    resVo.setSkuQuality(response.getSkuQuality());
                    resVo.setSkuQualityDesc(resVo.getSkuQualityDesc());
                    resVo.setInventoryType(response.getInventoryType());
                    resVo.setInventoryTypeDesc(response.getInventoryTypeDesc());
                    resVo.setSkuLotNo(response.getSkuLotNo());
                    resVo.setProduceBatchCode(response.getProductionNo());
                    resVo.setPhysicalQty(response.getPhysicalQty());
                    resVo.setAvailableQty(response.getAvailableQty());
                    resVo.setExternalLinkBillNo(response.getExternalLinkBillNo());
                    String key = resVo.getSkuLotNo() + resVo.getLocationCode();
                    if (wmIssueLineMap.containsKey(key)) {
                        WmIssueLine wmIssueLine = wmIssueLineMap.get(key);
                        resVo.setQuantityIssued(wmIssueLine.getQuantityIssued());
                    }
                    return resVo;
                }).collect(Collectors.toList());
        return AjaxResult.success(bomInfoResVoList);
    }


    /**
     * 新增生产领料单头
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:issueheader:add')")
    @Log(title = "生产领料单头", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WmIssueAddReqVo reqVo) {
        log.info("新增生产领料单 reqVo={}", JSON.toJSONString(reqVo));
        List<WmIssueLineAddReqVo> lineList = reqVo.getLineList();
        if (CollectionUtils.isEmpty(lineList)) {
            return AjaxResult.error("至少添加一行领料数据");
        }
        if (lineList.stream().anyMatch(line -> Objects.isNull(line.getQuantityIssued()))) {
            return AjaxResult.error("领料数量不能为空");
        }

        Long workorderId = reqVo.getWorkorderId();
        ProWorkorder proWorkorder = proWorkorderService.selectProWorkorderByWorkorderId(workorderId);
        if (Objects.isNull(proWorkorder)) {
            return AjaxResult.error("生产工单不存在");
        }
        if (!Objects.equals(proWorkorder.getStatus(), OrderStatusEnum.CONFIRMED.getCode())) {
            return AjaxResult.error("生产工单状态不为[已确认]");
        }
        Long taskId = reqVo.getTaskId();
        ProTask proTask = proTaskService.selectProTaskByTaskId(taskId);
        if (!Objects.equals(proTask.getStatus(), OrderStatusEnum.PRODUCING.getCode())) {
            return AjaxResult.error("生产任务状态不为[生产中]");
        }
        WmIssueHeader wmIssueHeader = new WmIssueHeader();
        wmIssueHeader.setIssueCode(autoCodeUtil.genSerialCode(UserConstants.WM_ISSUE_HEAD_CODE));
        wmIssueHeader.setWorkorderId(workorderId);
        wmIssueHeader.setWorkorderCode(proWorkorder.getWorkorderCode());
        wmIssueHeader.setTaskId(taskId);
        wmIssueHeader.setTaskCode(proTask.getTaskCode());
        wmIssueHeader.setWarehouseCode(proWorkorder.getWarehouseCode());
        wmIssueHeader.setWarehouseName(proWorkorder.getWarehouseName());
        wmIssueHeader.setOwnerCode(proWorkorder.getOwnerCode());
        wmIssueHeader.setOwnerName(proWorkorder.getOwnerName());
//        wmIssueHeader.setItemCode(proWorkorder.getProductCode());
//        wmIssueHeader.setItemName(proWorkorder.getProductName());
        wmIssueHeader.setWorkshopId(proWorkorder.getWorkshopId());
        wmIssueHeader.setWorkshopName(proWorkorder.getWorkshopName());
        wmIssueHeader.setCreateBy(getUsername());
        int i = wmIssueHeaderService.insertWmIssueHeader(wmIssueHeader);

        //插入表体
        lineList.forEach(l -> {
            WmIssueLine wmIssueLine = ConvertUtil.beanConvert(l, WmIssueLine.class);
            wmIssueLine.setWorkorderId(workorderId);
            wmIssueLine.setTaskId(taskId);
            wmIssueLine.setIssueId(wmIssueHeader.getIssueId());
            wmIssueLine.setLocationName(l.getLocationCode());
            wmIssueLineService.insertWmIssueLine(wmIssueLine);
        });
        return toAjax(i);
    }

    /**
     * 修改生产领料单头
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:issueheader:edit')")
    @Log(title = "生产领料单头", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WmIssueEditReqVo reqVo) {
        Long workorderId = reqVo.getWorkorderId();
        Long taskId = reqVo.getTaskId();
        ProWorkorder proWorkorder = proWorkorderService.selectProWorkorderByWorkorderId(workorderId);
        ProTask proTask = proTaskService.selectProTaskByTaskId(taskId);

        WmIssueHeader wmIssueHeader = wmIssueHeaderService.selectWmIssueHeaderByIssueId(reqVo.getIssueId());
        wmIssueHeader.setWorkorderId(workorderId);
        wmIssueHeader.setWorkorderCode(proWorkorder.getWorkorderCode());
        wmIssueHeader.setTaskId(taskId);
        wmIssueHeader.setTaskCode(proTask.getTaskCode());
//        wmIssueHeader.setItemCode(proWorkorder.getProductCode());
//        wmIssueHeader.setItemName(proWorkorder.getProductName());
//        wmIssueHeader.setWorkShopId(proWorkorder.getWorkshopId());
//        wmIssueHeader.setWorkShopName(proWorkorder.getWorkshopName());
        wmIssueHeader.setCreateBy(getUsername());
        int i = wmIssueHeaderService.updateWmIssueHeader(wmIssueHeader);
        wmIssueLineService.deleteByIssueHeaderId(wmIssueHeader.getIssueId());
        //插入表体
        List<WmIssueLineAddReqVo> lineList = reqVo.getLineList();
        lineList.forEach(l -> {
            WmIssueLine wmIssueLine = ConvertUtil.beanConvert(l, WmIssueLine.class);
            wmIssueLine.setWorkorderId(workorderId);
            wmIssueLine.setTaskId(taskId);
            wmIssueLine.setIssueId(wmIssueHeader.getIssueId());
            wmIssueLineService.insertWmIssueLine(wmIssueLine);
        });
        return toAjax(i);
    }

    /**
     * 删除生产领料单头
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:issueheader:remove')")
    @Log(title = "生产领料单头", businessType = BusinessType.DELETE)
    @Transactional
    @DeleteMapping("/{issueIds}")
    public AjaxResult remove(@PathVariable Long[] issueIds) {
        for (long issueId : issueIds) {
            WmIssueHeader header = wmIssueHeaderService.selectWmIssueHeaderByIssueId(issueId);
            if (!UserConstants.ORDER_STATUS_PREPARE.equals(header.getStatus())) {
                return AjaxResult.error("只能删除草稿状态的单据!");
            }

            wmIssueLineService.deleteByIssueHeaderId(issueId);
        }

        return toAjax(wmIssueHeaderService.deleteWmIssueHeaderByIssueIds(issueIds));
    }

    /**
     * 执行出库
     *
     * @return
     */
    @PreAuthorize("@ss.hasPermi('mes:wm:issueheader:edit')")
    @Log(title = "生产领料单头", businessType = BusinessType.UPDATE)
    @Transactional
    @PutMapping("/{issueId}")
    public AjaxResult execute(@PathVariable Long issueId) {
        WmIssueHeader header = wmIssueHeaderService.selectWmIssueHeaderByIssueId(issueId);
        WmIssueLine param = new WmIssueLine();
        param.setIssueId(issueId);
        List<WmIssueLine> lines = wmIssueLineService.selectWmIssueLineList(param);
        if (CollUtil.isEmpty(lines)) {
            return AjaxResult.error("请指定领出的物资");
        }

        List<IssueTxBean> beans = wmIssueHeaderService.getTxBeans(issueId);
        for (IssueTxBean issueTxBean : beans) {
            if (Objects.isNull(issueTxBean.getTransactionQuantity())) {
                return AjaxResult.error("领出数量未填写");
            }
        }
        Long workorderId = header.getWorkorderId();
        ProWorkorder proWorkorder = proWorkorderService.selectProWorkorderByWorkorderId(workorderId);
        Long workShopId = proWorkorder.getWorkshopId();
        MdWorkshop mdWorkshop = mdWorkshopService.selectMdWorkshopByWorkshopId(workShopId);

        MoveAddRequest moveAddRequest = new MoveAddRequest();
        moveAddRequest.setWarehouseCode(proWorkorder.getWarehouseCode());
        moveAddRequest.setCargoCode(proWorkorder.getOwnerCode());
        moveAddRequest.setBillNo(header.getIssueCode());
        List<MoveDetail> moveDetailList = beans.stream().map(bean -> {
            MoveDetail moveDetail = new MoveDetail();
            moveDetail.setSkuCode(bean.getItemCode());
            moveDetail.setSkuLotNo(bean.getSkuLotNo());
//            moveDetail.setOriginLocationCode();
//            if (Objects.equals(bean.get()))
            moveDetail.setOriginLocationCode(bean.getLocationCode());
            moveDetail.setTargetLocationCode(mdWorkshop.getAvlLineSideLocationCode());
            moveDetail.setMoveQty(bean.getTransactionQuantity());
            return moveDetail;
        }).collect(Collectors.toList());
        moveAddRequest.setDetailList(moveDetailList);
        log.info("moveAddRequest:{}", JSON.toJSONString(moveAddRequest));
        Result<String> result = iMoveMesClient.add(moveAddRequest);
        log.info("move result:{}", JSON.toJSONString(result));
        if (result.checkSuccess()) {
            header.setWmsOrderNo(result.getData());
            //更新单据状态
            header.setIssueDate(new Date());
            header.setStatus(UserConstants.ORDER_STATUS_FINISHED);
            wmIssueHeaderService.updateWmIssueHeader(header);
            return AjaxResult.success();
        } else {
            return AjaxResult.error("领料失败：{}" + result.getMessage());
        }

    }


}
