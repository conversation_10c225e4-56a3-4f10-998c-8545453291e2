<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.wm.mapper.WmProductSalseMapper">
    
    <resultMap type="WmProductSalse" id="WmProductSalseResult">
        <result property="salseId"    column="salse_id"    />
        <result property="salseCode"    column="salse_code"    />
        <result property="salseName"    column="salse_name"    />
        <result property="oqcId"    column="oqc_id"    />
        <result property="oqcCode"    column="oqc_code"    />
        <result property="soCode"    column="so_code"    />
        <result property="clientId"    column="client_id"    />
        <result property="clientCode"    column="client_code"    />
        <result property="clientName"    column="client_name"    />
        <result property="clientNick"    column="client_nick"    />
        <result property="warehouseId"    column="warehouse_id"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="locationId"    column="location_id"    />
        <result property="locationCode"    column="location_code"    />
        <result property="locationName"    column="location_name"    />
        <result property="areaId"    column="area_id"    />
        <result property="areaCode"    column="area_code"    />
        <result property="areaName"    column="area_name"    />
        <result property="salseDate"    column="salse_date"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>


    <resultMap id="ProductSalseTxBeanResult" type="ProductSalseTxBean">
        <result property="materialStockId" column="material_stock_id"></result>
        <result property="itemId" column="item_id"></result>
        <result property="itemCode" column="item_code"></result>
        <result property="itemName" column="item_name"></result>
        <result property="specification" column="specification"></result>
        <result property="unitOfMeasure" column="unit_of_measure"></result>
        <result property="batchCode" column="batch_code"></result>
        <result property="warehouseId" column="warehouse_id"></result>
        <result property="warehouseCode" column="warehouse_code"></result>
        <result property="warehouseName" column="warehouse_name"></result>
        <result property="locationId" column="location_id"></result>
        <result property="locationCode" column="location_code"></result>
        <result property="locationName" column="location_name"></result>
        <result property="areaId" column="area_id"></result>
        <result property="areaCode" column="area_code"></result>
        <result property="areaName" column="area_name"></result>
        <result property="clientId" column="client_id"></result>
        <result property="clientCode" column="client_code"></result>
        <result property="clientName" column="client_name"></result>
        <result property="clientNick" column="client_nick"></result>
        <result property="sourceDocType" column="source_doc_type"></result>
        <result property="sourceDocId" column="source_doc_id"></result>
        <result property="sourceDocCode" column="source_doc_code"></result>
        <result property="sourceDocLineId" column="source_doc_line_id"></result>
        <result property="transactionQuantity" column="transaction_quantity"></result>
        <result property="workorderId" column="workorder_id"></result>
        <result property="workorderCode" column="workorder_code"></result>
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWmProductSalseVo">
        select salse_id, salse_code, salse_name, oqc_id, oqc_code, so_code, client_id, client_code, client_name, client_nick, warehouse_id, warehouse_code, warehouse_name, location_id, location_code, location_name, area_id, area_code, area_name, salse_date, status, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from wm_product_salse
    </sql>

    <select id="selectWmProductSalseList" parameterType="WmProductSalse" resultMap="WmProductSalseResult">
        <include refid="selectWmProductSalseVo"/>
        <where>  
            <if test="salseCode != null  and salseCode != ''"> and salse_code = #{salseCode}</if>
            <if test="salseName != null  and salseName != ''"> and salse_name like concat('%', #{salseName}, '%')</if>
            <if test="oqcId != null "> and oqc_id = #{oqcId}</if>
            <if test="oqcCode != null  and oqcCode != ''"> and oqc_code = #{oqcCode}</if>
            <if test="soCode != null  and soCode != ''"> and so_code = #{soCode}</if>
            <if test="clientId != null "> and client_id = #{clientId}</if>
            <if test="clientCode != null  and clientCode != ''"> and client_code = #{clientCode}</if>
            <if test="clientName != null  and clientName != ''"> and client_name like concat('%', #{clientName}, '%')</if>
            <if test="clientNick != null  and clientNick != ''"> and client_nick = #{clientNick}</if>
            <if test="warehouseId != null "> and warehouse_id = #{warehouseId}</if>
            <if test="warehouseCode != null  and warehouseCode != ''"> and warehouse_code = #{warehouseCode}</if>
            <if test="warehouseName != null  and warehouseName != ''"> and warehouse_name like concat('%', #{warehouseName}, '%')</if>
            <if test="locationId != null "> and location_id = #{locationId}</if>
            <if test="locationCode != null  and locationCode != ''"> and location_code = #{locationCode}</if>
            <if test="locationName != null  and locationName != ''"> and location_name like concat('%', #{locationName}, '%')</if>
            <if test="areaId != null "> and area_id = #{areaId}</if>
            <if test="areaCode != null  and areaCode != ''"> and area_code = #{areaCode}</if>
            <if test="areaName != null  and areaName != ''"> and area_name like concat('%', #{areaName}, '%')</if>
            <if test="salseDate != null "> and salse_date = #{salseDate}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectWmProductSalseBySalseId" parameterType="Long" resultMap="WmProductSalseResult">
        <include refid="selectWmProductSalseVo"/>
        where salse_id = #{salseId}
    </select>

    <select id="checkUnique" parameterType="WmProductSalse" resultMap="WmProductSalseResult">
        <include refid="selectWmProductSalseVo"/>
        where salse_code = #{salseCode}
    </select>

    <select id="getTxBeans" parameterType="Long" resultMap="ProductSalseTxBeanResult">
        SELECT irl.material_stock_id, irl.`item_id`,irl.`item_code`,irl.`item_name`,irl.`specification`,irl.`unit_of_measure`,irl.`batch_code`,
               irl.`warehouse_id`,irl.`warehouse_code`,irl.`warehouse_name`,irl.`location_id`,irl.`location_code`,irl.`location_name`,
               irl.`area_id`,irl.`area_code`,irl.`area_name`,
               'PSALSE' AS source_doc_type,ir.`salse_id` AS source_doc_id,ir.`salse_code` AS source_doc_code,irl.`line_id` AS source_doc_line_id,
               irl.`quantity_salse` AS transaction_quantity,
               ir.`create_by`,ir.`create_time`,ir.`update_by`,ir.`update_time`
        FROM wm_product_salse ir
                 LEFT JOIN wm_product_salse_line irl
                           ON ir.salse_id = irl.`salse_id`
        WHERE ir.`salse_id` = #{salseId}
    </select>

    <insert id="insertWmProductSalse" parameterType="WmProductSalse" useGeneratedKeys="true" keyProperty="salseId">
        insert into wm_product_salse
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="salseCode != null and salseCode != ''">salse_code,</if>
            <if test="salseName != null and salseName != ''">salse_name,</if>
            <if test="oqcId != null">oqc_id,</if>
            <if test="oqcCode != null">oqc_code,</if>
            <if test="soCode != null">so_code,</if>
            <if test="clientId != null">client_id,</if>
            <if test="clientCode != null">client_code,</if>
            <if test="clientName != null">client_name,</if>
            <if test="clientNick != null">client_nick,</if>
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="warehouseCode != null">warehouse_code,</if>
            <if test="warehouseName != null">warehouse_name,</if>
            <if test="locationId != null">location_id,</if>
            <if test="locationCode != null">location_code,</if>
            <if test="locationName != null">location_name,</if>
            <if test="areaId != null">area_id,</if>
            <if test="areaCode != null">area_code,</if>
            <if test="areaName != null">area_name,</if>
            <if test="salseDate != null">salse_date,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="salseCode != null and salseCode != ''">#{salseCode},</if>
            <if test="salseName != null and salseName != ''">#{salseName},</if>
            <if test="oqcId != null">#{oqcId},</if>
            <if test="oqcCode != null">#{oqcCode},</if>
            <if test="soCode != null">#{soCode},</if>
            <if test="clientId != null">#{clientId},</if>
            <if test="clientCode != null">#{clientCode},</if>
            <if test="clientName != null">#{clientName},</if>
            <if test="clientNick != null">#{clientNick},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="warehouseCode != null">#{warehouseCode},</if>
            <if test="warehouseName != null">#{warehouseName},</if>
            <if test="locationId != null">#{locationId},</if>
            <if test="locationCode != null">#{locationCode},</if>
            <if test="locationName != null">#{locationName},</if>
            <if test="areaId != null">#{areaId},</if>
            <if test="areaCode != null">#{areaCode},</if>
            <if test="areaName != null">#{areaName},</if>
            <if test="salseDate != null">#{salseDate},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWmProductSalse" parameterType="WmProductSalse">
        update wm_product_salse
        <trim prefix="SET" suffixOverrides=",">
            <if test="salseCode != null and salseCode != ''">salse_code = #{salseCode},</if>
            <if test="salseName != null and salseName != ''">salse_name = #{salseName},</if>
            <if test="oqcId != null">oqc_id = #{oqcId},</if>
            <if test="oqcCode != null">oqc_code = #{oqcCode},</if>
            <if test="soCode != null">so_code = #{soCode},</if>
            <if test="clientId != null">client_id = #{clientId},</if>
            <if test="clientCode != null">client_code = #{clientCode},</if>
            <if test="clientName != null">client_name = #{clientName},</if>
            <if test="clientNick != null">client_nick = #{clientNick},</if>
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="warehouseCode != null">warehouse_code = #{warehouseCode},</if>
            <if test="warehouseName != null">warehouse_name = #{warehouseName},</if>
            <if test="locationId != null">location_id = #{locationId},</if>
            <if test="locationCode != null">location_code = #{locationCode},</if>
            <if test="locationName != null">location_name = #{locationName},</if>
            <if test="areaId != null">area_id = #{areaId},</if>
            <if test="areaCode != null">area_code = #{areaCode},</if>
            <if test="areaName != null">area_name = #{areaName},</if>
            <if test="salseDate != null">salse_date = #{salseDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where salse_id = #{salseId}
    </update>

    <delete id="deleteWmProductSalseBySalseId" parameterType="Long">
        delete from wm_product_salse where salse_id = #{salseId}
    </delete>

    <delete id="deleteWmProductSalseBySalseIds" parameterType="String">
        delete from wm_product_salse where salse_id in 
        <foreach item="salseId" collection="array" open="(" separator="," close=")">
            #{salseId}
        </foreach>
    </delete>
</mapper>