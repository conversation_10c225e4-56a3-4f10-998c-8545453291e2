<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.wm.mapper.WmProductRecptMapper">
    <resultMap type="WmProductRecpt" id="WmProductRecptResult">
        <result property="recptId"    column="recpt_id"    />
        <result property="recptCode"    column="recpt_code"    />
        <result property="recptName"    column="recpt_name"    />
        <result property="workorderId"    column="workorder_id"    />
        <result property="workorderCode"    column="workorder_code"    />
        <result property="taskId"    column="task_id"    />
        <result property="taskCode"    column="task_code"    />
        <result property="itemId"    column="item_id"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemName"    column="item_name"    />
        <result property="specification"    column="specification"    />
        <result property="unitOfMeasure"    column="unit_of_measure"    />
        <result property="warehouseId"    column="warehouse_id"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="locationId"    column="location_id"    />
        <result property="locationCode"    column="location_code"    />
        <result property="locationName"    column="location_name"    />
        <result property="areaId"    column="area_id"    />
        <result property="areaCode"    column="area_code"    />
        <result property="areaName"    column="area_name"    />
        <result property="recptDate"    column="recpt_date"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="wmsOrderNo"    column="wms_order_no"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWmProductRecptVo">
        select recpt_id, recpt_code, recpt_name, workorder_id, workorder_code, task_id, task_code, item_id, item_code, item_name, specification, unit_of_measure, warehouse_id, warehouse_code, warehouse_name, location_id, location_code, location_name, area_id, area_code, area_name, recpt_date, status, remark, attr1, attr2, attr3, attr4, wms_order_no, create_by, create_time, update_by, update_time from wm_product_recpt
    </sql>

    <select id="selectWmProductRecptList" parameterType="WmProductRecpt" resultMap="WmProductRecptResult">
        <include refid="selectWmProductRecptVo"/>
        <where>
            <if test="recptCode != null  and recptCode != ''"> and recpt_code = #{recptCode}</if>
            <if test="recptName != null  and recptName != ''"> and recpt_name like concat('%', #{recptName}, '%')</if>
            <if test="workorderId != null "> and workorder_id = #{workorderId}</if>
            <if test="workorderCode != null  and workorderCode != ''"> and workorder_code = #{workorderCode}</if>
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="taskCode != null  and taskCode != ''"> and task_code = #{taskCode}</if>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code = #{itemCode}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="specification != null  and specification != ''"> and specification = #{specification}</if>
            <if test="unitOfMeasure != null  and unitOfMeasure != ''"> and unit_of_measure = #{unitOfMeasure}</if>
            <if test="warehouseId != null "> and warehouse_id = #{warehouseId}</if>
            <if test="warehouseCode != null  and warehouseCode != ''"> and warehouse_code = #{warehouseCode}</if>
            <if test="warehouseName != null  and warehouseName != ''"> and warehouse_name like concat('%', #{warehouseName}, '%')</if>
            <if test="locationId != null "> and location_id = #{locationId}</if>
            <if test="locationCode != null  and locationCode != ''"> and location_code = #{locationCode}</if>
            <if test="locationName != null  and locationName != ''"> and location_name like concat('%', #{locationName}, '%')</if>
            <if test="areaId != null "> and area_id = #{areaId}</if>
            <if test="areaCode != null  and areaCode != ''"> and area_code = #{areaCode}</if>
            <if test="areaName != null  and areaName != ''"> and area_name like concat('%', #{areaName}, '%')</if>
            <if test="recptDate != null "> and recpt_date = #{recptDate}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="attr1 != null  and attr1 != ''"> and attr1 = #{attr1}</if>
            <if test="attr2 != null  and attr2 != ''"> and attr2 = #{attr2}</if>
            <if test="attr3 != null "> and attr3 = #{attr3}</if>
            <if test="attr4 != null "> and attr4 = #{attr4}</if>
            <if test="wmsOrderNo != null  and wmsOrderNo != ''"> and wms_order_no = #{wmsOrderNo}</if>
        </where>
    </select>

    <select id="selectWmProductRecptByRecptId" parameterType="Long" resultMap="WmProductRecptResult">
        <include refid="selectWmProductRecptVo"/>
        where recpt_id = #{recptId}
    </select>

    <insert id="insertWmProductRecpt" parameterType="WmProductRecpt" useGeneratedKeys="true" keyProperty="recptId">
        insert into wm_product_recpt
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recptCode != null and recptCode != ''">recpt_code,</if>
            <if test="recptName != null">recpt_name,</if>
            <if test="workorderId != null">workorder_id,</if>
            <if test="workorderCode != null and workorderCode != ''">workorder_code,</if>
            <if test="taskId != null">task_id,</if>
            <if test="taskCode != null and taskCode != ''">task_code,</if>
            <if test="itemId != null">item_id,</if>
            <if test="itemCode != null">item_code,</if>
            <if test="itemName != null">item_name,</if>
            <if test="specification != null">specification,</if>
            <if test="unitOfMeasure != null">unit_of_measure,</if>
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="warehouseCode != null">warehouse_code,</if>
            <if test="warehouseName != null">warehouse_name,</if>
            <if test="locationId != null">location_id,</if>
            <if test="locationCode != null">location_code,</if>
            <if test="locationName != null">location_name,</if>
            <if test="areaId != null">area_id,</if>
            <if test="areaCode != null">area_code,</if>
            <if test="areaName != null">area_name,</if>
            <if test="recptDate != null">recpt_date,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="wmsOrderNo != null">wms_order_no,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="recptCode != null and recptCode != ''">#{recptCode},</if>
            <if test="recptName != null">#{recptName},</if>
            <if test="workorderId != null">#{workorderId},</if>
            <if test="workorderCode != null and workorderCode != ''">#{workorderCode},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="taskCode != null and taskCode != ''">#{taskCode},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="itemCode != null">#{itemCode},</if>
            <if test="itemName != null">#{itemName},</if>
            <if test="specification != null">#{specification},</if>
            <if test="unitOfMeasure != null">#{unitOfMeasure},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="warehouseCode != null">#{warehouseCode},</if>
            <if test="warehouseName != null">#{warehouseName},</if>
            <if test="locationId != null">#{locationId},</if>
            <if test="locationCode != null">#{locationCode},</if>
            <if test="locationName != null">#{locationName},</if>
            <if test="areaId != null">#{areaId},</if>
            <if test="areaCode != null">#{areaCode},</if>
            <if test="areaName != null">#{areaName},</if>
            <if test="recptDate != null">#{recptDate},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="wmsOrderNo != null">#{wmsOrderNo},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateWmProductRecpt" parameterType="WmProductRecpt">
        update wm_product_recpt
        <trim prefix="SET" suffixOverrides=",">
            <if test="recptCode != null and recptCode != ''">recpt_code = #{recptCode},</if>
            <if test="recptName != null">recpt_name = #{recptName},</if>
            <if test="workorderId != null">workorder_id = #{workorderId},</if>
            <if test="workorderCode != null and workorderCode != ''">workorder_code = #{workorderCode},</if>
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="taskCode != null and taskCode != ''">task_code = #{taskCode},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="itemCode != null">item_code = #{itemCode},</if>
            <if test="itemName != null">item_name = #{itemName},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="unitOfMeasure != null">unit_of_measure = #{unitOfMeasure},</if>
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="warehouseCode != null">warehouse_code = #{warehouseCode},</if>
            <if test="warehouseName != null">warehouse_name = #{warehouseName},</if>
            <if test="locationId != null">location_id = #{locationId},</if>
            <if test="locationCode != null">location_code = #{locationCode},</if>
            <if test="locationName != null">location_name = #{locationName},</if>
            <if test="areaId != null">area_id = #{areaId},</if>
            <if test="areaCode != null">area_code = #{areaCode},</if>
            <if test="areaName != null">area_name = #{areaName},</if>
            <if test="recptDate != null">recpt_date = #{recptDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="wmsOrderNo != null">wms_order_no = #{wmsOrderNo},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where recpt_id = #{recptId}
    </update>

    <delete id="deleteWmProductRecptByRecptId" parameterType="Long">
        delete from wm_product_recpt where recpt_id = #{recptId}
    </delete>

    <delete id="deleteWmProductRecptByRecptIds" parameterType="String">
        delete from wm_product_recpt where recpt_id in
        <foreach item="recptId" collection="array" open="(" separator="," close=")">
            #{recptId}
        </foreach>
    </delete>

    <resultMap type="ProductRecptTxBean"  id="ProductRecptTxBeanResult">
        <result property="materialStockId" column="material_stock_id" ></result>
        <result property="itemId" column="item_id"></result>
        <result property="itemCode" column="item_code"></result>
        <result property="itemName" column="item_name"></result>
        <result property="specification" column="specification"></result>
        <result property="unitOfMeasure" column="unit_of_measure"></result>
        <result property="batchCode" column="batch_code"></result>
        <result property="warehouseId" column="warehouse_id"></result>
        <result property="warehouseCode" column="warehouse_code"></result>
        <result property="warehouseName" column="warehouse_name"></result>
        <result property="zoneCode" column="zone_code"></result>
        <result property="zoneName" column="zone_name"></result>
        <result property="locationCode" column="location_code"></result>
        <result property="locationName" column="location_name"></result>
        <result property="skuQuality" column="sku_quality"></result>
        <result property="inventoryType" column="inventory_type"></result>
        <result property="skuLotNo" column="sku_lot_no"></result>
        <result property="produceBatchCode" column="produce_batch_code"></result>
        <result property="workorderId" column="workorder_id"></result>
        <result property="workorderCode" column="workorder_code"></result>
        <result property="sourceDocType" column="source_doc_type"></result>
        <result property="sourceDocId" column="source_doc_id"></result>
        <result property="sourceDocCode" column="source_doc_code"></result>
        <result property="sourceDocLineId" column="source_doc_line_id"></result>
        <result property="transactionQuantity" column="transaction_quantity"></result>
        <result property="recptDate" column="recpt_date"></result>
        <result property="expireDate" column="expire_date"></result>
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <select id="getTxBean" parameterType="Long" resultMap="ProductRecptTxBeanResult">
        SELECT irl.`item_id`,irl.`item_code`,irl.`item_name`,irl.`specification`,irl.`unit_of_measure`,irl.`batch_code`,
               irl.`warehouse_id`,irl.`warehouse_code`,irl.`warehouse_name`,irl.`zone_code`,irl.`zone_name`,irl.`location_code`,irl.`location_name`,
               ir.workorder_id,ir.workorder_code,
               'PR' AS source_doc_type,ir.`recpt_id` AS source_doc_id,ir.`recpt_code` AS source_doc_code,irl.`line_id` AS source_doc_line_id,
               irl.`quantity_recived` AS transaction_quantity,ir.recpt_date, irl.`expire_date`,
               ir.`create_by`,ir.`create_time`,ir.`update_by`,ir.`update_time`,irl.sku_quality,irl.inventory_type,irl.sku_lot_no,irl.produce_batch_code
        FROM wm_product_recpt ir
                 LEFT JOIN wm_product_recpt_line irl
                           ON ir.recpt_id = irl.`recpt_id`
        WHERE ir.`recpt_id` = #{recptId}
    </select>


    <select id="checkUnique" parameterType="WmProductRecpt" resultMap="WmProductRecptResult">
        <include refid="selectWmProductRecptVo"/>
        where recpt_code = #{recptCode}
    </select>

</mapper>