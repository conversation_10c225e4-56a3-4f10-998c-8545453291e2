<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.wm.mapper.WmArrivalNoticeMapper">
    <update id="updateTakeStockStatus">
        update wm_arrival_notice set take_stock_status = #{takeStockStatus} where notice_id = #{noticeId}
    </update>

    <select id="wmArrivalNoticePage" parameterType="WmArrivalNoticeListReqVo" resultMap="WmArrivalNoticeResult">
        <include refid="selectWmArrivalNoticeVo"/>
        <where>
            <if test="noticeCode != null  and noticeCode != ''"> and notice_code  like concat('%', #{noticeCode}, '%')</if>
            <if test="poCode != null  and poCode != ''"> and po_code like concat('%', #{poCode}, '%')</if>
            <if test="warehouseCode != null  and warehouseCode != ''"> and warehouse_code = #{warehouseCode}</if>
            <if test="ownerCode != null  and ownerCode != ''"> and owner_code = #{ownerCode}</if>
            <if test="takeStockStatus != null  and takeStockStatus != ''"> and take_stock_status = #{takeStockStatus}</if>
            <if test="arrivalDateBegin != null and arrivalDateBegin != null"> and arrival_date between #{arrivalDateBegin} and #{arrivalDateEnd}</if>
        </where>
        order by create_time desc
    </select>

</mapper>