<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.wm.mapper.WmRtIssueMapper">

    <resultMap type="RtIssueTxBean" id="RtIssueTxBeanResult">
        <result property="materialStockId" column="material_stock_id"></result>
        <result property="itemId" column="item_id"></result>
        <result property="itemCode" column="item_code"></result>
        <result property="itemName" column="item_name"></result>
        <result property="specification" column="specification"></result>
        <result property="unitOfMeasure" column="unit_of_measure"></result>
        <result property="batchCode" column="batch_code"></result>
        <result property="warehouseId" column="warehouse_id"></result>
        <result property="warehouseCode" column="warehouse_code"></result>
        <result property="warehouseName" column="warehouse_name"></result>
        <result property="zoneCode" column="zone_code"></result>
        <result property="zoneName" column="zone_name"></result>
        <result property="locationCode" column="location_code"></result>
        <result property="locationName" column="location_name"></result>
        <result property="skuQuality" column="sku_quality"></result>
        <result property="inventoryType" column="inventory_type"></result>
        <result property="skuLotNo" column="sku_lot_no"></result>
        <result property="produceBatchCode" column="produce_batch_code"></result>
        <result property="sourceDocType" column="source_doc_type"></result>
        <result property="sourceDocId" column="source_doc_id"></result>
        <result property="sourceDocCode" column="source_doc_code"></result>
        <result property="sourceDocLineId" column="source_doc_line_id"></result>
        <result property="transactionQuantity" column="transaction_quantity"></result>
        <result property="recptDate" column="recpt_date"></result>
    </resultMap>

    <select id="checkUnique" parameterType="WmRtIssue" resultMap="WmRtIssueResult">
        <include refid="selectWmRtIssueVo"/>
        where rt_code = #{rtCode}
    </select>


    <select id="getTxBeans" parameterType="Long" resultMap="RtIssueTxBeanResult">
        SELECT irl.material_stock_id,
               irl.`item_id`,
               irl.`item_code`,
               irl.`item_name`,
               irl.`specification`,
               irl.`unit_of_measure`,
               irl.`batch_code`,
               irl.`warehouse_id`,
               irl.`warehouse_code`,
               irl.`warehouse_name`,
               irl.`zone_code`,
               irl.`zone_name`,
               irl.`location_code`,
               irl.`location_name`,
               ms.vendor_id    as vendorId,
               ms.vendor_code  as vendorCode,
               ms.vendor_name  as vendorName,
               ms.vendor_nick  as vendorNick,
               'RTISSUE'       AS source_doc_type,
               ir.`rt_id`      AS source_doc_id,
               ir.`rt_code`    AS source_doc_code,
               irl.`line_id`   AS source_doc_line_id,
               irl.quantity_rt AS transaction_quantity,
               ir.rt_date      as recptDate,
               irl.`sku_quality`,
               irl.`inventory_type`,
               irl.`sku_lot_no`,
               irl.`produce_batch_code`
        FROM wm_rt_issue ir
                 LEFT JOIN wm_rt_issue_line irl
                           ON ir.rt_id = irl.rt_id
                 left join wm_material_stock ms
                           on irl.material_stock_id = ms.material_stock_id
        WHERE ir.rt_id = #{rtId}
    </select>

    <select id="getTxBeansByWorkorderIdAndItemCode" resultMap="RtIssueTxBeanResult">
        SELECT irl.material_stock_id,
        irl.`item_id`,
        irl.`item_code`,
        irl.`item_name`,
        irl.`specification`,
        irl.`unit_of_measure`,
        irl.`batch_code`,
        irl.`warehouse_id`,
        irl.`warehouse_code`,
        irl.`warehouse_name`,
        irl.`zone_code`,
        irl.`zone_name`,
        irl.`location_code`,
        irl.`location_name`,
        ms.vendor_id as vendorId,ms.vendor_code as vendorCode,ms.vendor_name as vendorName,ms.vendor_nick as vendorNick,
        'RTISSUE' AS source_doc_type,ir.`rt_id` AS source_doc_id,
        ir.`rt_code` AS source_doc_code,
        irl.`line_id` AS source_doc_line_id,
        irl.quantity_rt AS transaction_quantity,
        ir.rt_date as recptDate,
        irl.`sku_quality`,
        irl.`inventory_type`,
        irl.`sku_lot_no`,
        irl.`produce_batch_code`
        FROM wm_rt_issue ir
        LEFT JOIN wm_rt_issue_line irl
        ON ir.rt_id = irl.rt_id
        left join wm_material_stock ms
        on irl.material_stock_id = ms.material_stock_id
        WHERE ir.`workorder_id` = #{workorderId} AND ir.`status` = 'FINISHED' AND irl.`item_code` IN
        <foreach item="item" collection="itemCodeList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getTxBeansByTaskIdAndItemCode" resultMap="RtIssueTxBeanResult">
        SELECT irl.material_stock_id,
        irl.`item_id`,
        irl.`item_code`,
        irl.`item_name`,
        irl.`specification`,
        irl.`unit_of_measure`,
        irl.`batch_code`,
        irl.`warehouse_id`,
        irl.`warehouse_code`,
        irl.`warehouse_name`,
        irl.`zone_code`,
        irl.`zone_name`,
        irl.`location_code`,
        irl.`location_name`,
        ms.vendor_id as vendorId,ms.vendor_code as vendorCode,ms.vendor_name as vendorName,ms.vendor_nick as vendorNick,
        'RTISSUE' AS source_doc_type,ir.`rt_id` AS source_doc_id,
        ir.`rt_code` AS source_doc_code,
        irl.`line_id` AS source_doc_line_id,
        irl.quantity_rt AS transaction_quantity,
        ir.rt_date as recptDate,
        irl.`sku_quality`,
        irl.`inventory_type`,
        irl.`sku_lot_no`,
        irl.`produce_batch_code`
        FROM wm_rt_issue ir
        LEFT JOIN wm_rt_issue_line irl
        ON ir.rt_id = irl.rt_id
        left join wm_material_stock ms
        on irl.material_stock_id = ms.material_stock_id
        WHERE ir.`task_id` = #{taskId} AND ir.`status` = 'FINISHED' AND irl.`item_code` IN
        <foreach item="item" collection="itemCodeList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="wmRtIssuePage" parameterType="WmRtIssueListReqVo" resultMap="WmRtIssueResult">
        <include refid="selectWmRtIssueVo"/>
        <where>
            <if test="rtCode != null  and rtCode != ''"> and rt_code  like concat('%', #{rtCode}, '%')</if>
            <if test="workorderCode != null  and workorderCode != ''"> and workorder_code like concat('%', #{workorderCode}, '%')</if>
            <if test="warehouseCode != null  and warehouseCode != ''"> and warehouse_code = #{warehouseCode}</if>
            <if test="wmsOrderNo != null  and wmsOrderNo != ''"> and wms_order_no like concat('%', #{wmsOrderNo}, '%')</if>
            <if test="rtDateBegin != null and rtDateEnd !=null "> and rt_date  between #{rtDateBegin} and #{rtDateEnd}</if>
        </where>
        order by create_time desc
    </select>

</mapper>