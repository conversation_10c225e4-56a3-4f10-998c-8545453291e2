<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.wm.mapper.WmArrivalNoticeLineMapper">

    <select id="selectUncheckedLine" parameterType="Long" resultMap="WmArrivalNoticeLineResult">
        <include refid="selectWmArrivalNoticeLineVo"/>
        where notice_id = #{noticeId}
        and iqc_check = 'Y'
        and iqc_id is null
    </select>


    <select id="selectWmArrivalNoticeLineByNoticeId" parameterType="Long" resultMap="WmArrivalNoticeLineResult">
        <include refid="selectWmArrivalNoticeLineVo"/>
        where notice_id = #{noticeId}
    </select>
    <select id="selectByNoticeIdAndLineNo" resultMap="WmArrivalNoticeLineResult">
        <include refid="selectWmArrivalNoticeLineVo"/>
         where notice_id = #{noticeId} and line_no = #{lineNo}
    </select>

    <delete id="deleteByNoticeId" parameterType="Long">
        delete from wm_arrival_notice_line where notice_id = #{noticeId}
    </delete>

</mapper>