<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.wm.mapper.WmIssueHeaderMapper">

    <resultMap id="IssueTxBeanResult" type="IssueTxBean">
        <result property="materialStockId" column="material_stock_id"></result>
        <result property="itemId" column="item_id"></result>
        <result property="itemCode" column="item_code"></result>
        <result property="itemName" column="item_name"></result>
        <result property="specification" column="specification"></result>
        <result property="unitOfMeasure" column="unit_of_measure"></result>
        <result property="batchCode" column="batch_code"></result>
        <result property="warehouseId" column="warehouse_id"></result>
        <result property="warehouseCode" column="warehouse_code"></result>
        <result property="warehouseName" column="warehouse_name"></result>
        <result property="zoneCode" column="zone_code"></result>
        <result property="zoneName" column="zone_name"></result>
        <result property="locationCode" column="location_code"></result>
        <result property="locationName" column="location_name"></result>
        <result property="skuQuality" column="sku_quality"></result>
        <result property="inventoryType" column="inventory_type"></result>
        <result property="skuLotNo" column="sku_lot_no"></result>
        <result property="produceBatchCode" column="produce_batch_code"></result>
        <result property="vendorId" column="vendor_id"></result>
        <result property="vendorCode" column="vendor_code"></result>
        <result property="vendorName" column="vendor_name"></result>
        <result property="vendorNick" column="vendor_nick"></result>
        <result property="sourceDocType" column="source_doc_type"></result>
        <result property="sourceDocId" column="source_doc_id"></result>
        <result property="sourceDocCode" column="source_doc_code"></result>
        <result property="sourceDocLineId" column="source_doc_line_id"></result>
        <result property="transactionQuantity" column="transaction_quantity"></result>
        <result property="workorderId" column="workorder_id"></result>
        <result property="workorderCode" column="workorder_code"></result>
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <select id="checkIssueCodeUnique" parameterType="WmIssueHeader" resultMap="WmIssueHeaderResult">
        <include refid="selectWmIssueHeaderVo"/>
        where issue_code = #{issueCode} limit 1
    </select>

    <select id="getTxBeans" parameterType="Long" resultMap="IssueTxBeanResult">
        SELECT irl.material_stock_id, irl.`item_id`,irl.`item_code`,irl.`item_name`,irl.`specification`,irl.`unit_of_measure`,irl.`batch_code`,
               irl.`warehouse_id`,irl.`warehouse_code`,irl.`warehouse_name`,irl.`zone_code`,irl.`zone_name`,irl.`location_code`,irl.`location_name`,
               'ISSUE' AS source_doc_type,ir.`issue_id` AS source_doc_id,ir.`issue_code` AS source_doc_code,irl.`line_id` AS source_doc_line_id,
               irl.`quantity_issued` AS transaction_quantity,ir.workorder_id,ir.workorder_code,
               ir.`create_by`,ir.`create_time`,ir.`update_by`,ir.`update_time`,irl.sku_quality,irl.inventory_type,irl.sku_lot_no,irl.produce_batch_code
        FROM wm_issue_header ir
                 LEFT JOIN wm_issue_line irl
                           ON ir.issue_id = irl.`issue_id`
        WHERE ir.`issue_id` = #{issueId}
    </select>

    <select id="getTxBeansByWorkorderIdAndItemCode" resultMap="IssueTxBeanResult">
        SELECT irl.material_stock_id, irl.`item_id`,irl.`item_code`,irl.`item_name`,irl.`specification`,irl.`unit_of_measure`,irl.`batch_code`,
        irl.`warehouse_id`,irl.`warehouse_code`,irl.`warehouse_name`,irl.`zone_code`,irl.`zone_name`,irl.`location_code`,irl.`location_name`,
        'ISSUE' AS source_doc_type,ir.`issue_id` AS source_doc_id,ir.`issue_code` AS source_doc_code,irl.`line_id` AS source_doc_line_id,
        irl.`quantity_issued` AS transaction_quantity,ir.workorder_id,ir.workorder_code,
        ir.`create_by`,ir.`create_time`,ir.`update_by`,ir.`update_time`,irl.sku_quality,irl.inventory_type,irl.sku_lot_no,irl.produce_batch_code
        FROM wm_issue_header ir
        LEFT JOIN wm_issue_line irl
        ON ir.issue_id = irl.`issue_id`
        WHERE ir.`workorder_id` = #{workorderId} AND ir.`status` = 'FINISHED' AND irl.`item_code` IN
        <foreach item="item" collection="itemCodeList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getTxBeansByTaskIdAndItemCode" resultMap="IssueTxBeanResult">
        SELECT irl.material_stock_id, irl.`item_id`,irl.`item_code`,irl.`item_name`,irl.`specification`,irl.`unit_of_measure`,irl.`batch_code`,
        irl.`warehouse_id`,irl.`warehouse_code`,irl.`warehouse_name`,irl.`zone_code`,irl.`zone_name`,irl.`location_code`,irl.`location_name`,
        'ISSUE' AS source_doc_type,ir.`issue_id` AS source_doc_id,ir.`issue_code` AS source_doc_code,irl.`line_id` AS source_doc_line_id,
        irl.`quantity_issued` AS transaction_quantity,ir.workorder_id,ir.workorder_code,
        ir.`create_by`,ir.`create_time`,ir.`update_by`,ir.`update_time`,irl.sku_quality,irl.inventory_type,irl.sku_lot_no,irl.produce_batch_code
        FROM wm_issue_header ir
        LEFT JOIN wm_issue_line irl
        ON ir.issue_id = irl.`issue_id`
        WHERE ir.`task_id` = #{taskId} AND ir.`status` = 'FINISHED' AND irl.`item_code` IN
        <foreach item="item" collection="itemCodeList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getTxBeansByTaskId" resultMap="IssueTxBeanResult">
        SELECT irl.material_stock_id, irl.`item_id`,irl.`item_code`,irl.`item_name`,irl.`specification`,irl.`unit_of_measure`,irl.`batch_code`,
        irl.`warehouse_id`,irl.`warehouse_code`,irl.`warehouse_name`,irl.`zone_code`,irl.`zone_name`,irl.`location_code`,irl.`location_name`,
        'ISSUE' AS source_doc_type,ir.`issue_id` AS source_doc_id,ir.`issue_code` AS source_doc_code,irl.`line_id` AS source_doc_line_id,
        irl.`quantity_issued` AS transaction_quantity,ir.workorder_id,ir.workorder_code,
        ir.`create_by`,ir.`create_time`,ir.`update_by`,ir.`update_time`,irl.sku_quality,irl.inventory_type,irl.sku_lot_no,irl.produce_batch_code
        FROM wm_issue_header ir
        LEFT JOIN wm_issue_line irl
        ON ir.issue_id = irl.`issue_id`
        WHERE ir.`task_id` = #{taskId} AND ir.`status` = 'FINISHED'
    </select>

    <select id="wmIssueHeaderPage" parameterType="WmIssueListReqVo" resultMap="WmIssueHeaderResult">
        <include refid="selectWmIssueHeaderVo"/>
        <where>
            <if test="issueCode != null  and issueCode != ''"> and issue_code  like concat('%', #{issueCode}, '%')</if>
            <if test="workorderCode != null  and workorderCode != ''"> and workorder_code like concat('%', #{workorderCode}, '%')</if>
            <if test="warehouseCode != null  and warehouseCode != ''"> and warehouse_code = #{warehouseCode}</if>
            <if test="ownerCode != null  and ownerCode != ''"> and owner_code = #{ownerCode}</if>
            <if test="wmsOrderNo != null  and wmsOrderNo != ''"> and wms_order_no like concat('%', #{wmsOrderNo}, '%')</if>
            <if test="issueDateBegin != null and issueDateEnd !=null "> and issue_date  between #{issueDateBegin} and #{issueDateEnd}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectWmIssueHeaderByIssueIdList" resultMap="WmIssueHeaderResult">
        <include refid="selectWmIssueHeaderVo"/>
        WHERE issue_id IN
        <foreach item="item" collection="issueIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


</mapper>