<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.wm.mapper.WmProductRecptLineMapper">
    <select id="selectWmProductRecptLineByLineIds" parameterType="Long" resultMap="WmProductRecptLineResult">
        <include refid="selectWmProductRecptLineVo"/>
        where recpt_id in
        <foreach item="item" collection="recptIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>