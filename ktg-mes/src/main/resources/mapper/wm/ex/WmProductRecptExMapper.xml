<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.wm.mapper.WmProductRecptMapper">


    <select id="selectWmProductRecptListEx" parameterType="WmProductRecpt" resultMap="WmProductRecptResult">
        <include refid="selectWmProductRecptVo"/>
        <where>
            <if test="recptCode != null  and recptCode != ''">and recpt_code like concat('%', #{recptCode}, '%')</if>
            <if test="recptName != null  and recptName != ''">and recpt_name like concat('%', #{recptName}, '%')</if>
            <if test="workorderId != null ">and workorder_id = #{workorderId}</if>
            <if test="workorderCode != null  and workorderCode != ''">and workorder_code like concat('%', #{workorderCode}, '%')</if>
            <if test="taskId != null ">and task_id = #{taskId}</if>
            <if test="taskCode != null  and taskCode != ''">and task_code like concat('%', #{taskCode}, '%')</if>
            <if test="itemId != null ">and item_id = #{itemId}</if>
            <if test="itemCode != null  and itemCode != ''">and item_code = #{itemCode}</if>
            <if test="itemName != null  and itemName != ''">and item_name like concat('%', #{itemName}, '%')</if>
            <if test="specification != null  and specification != ''">and specification = #{specification}</if>
            <if test="unitOfMeasure != null  and unitOfMeasure != ''">and unit_of_measure = #{unitOfMeasure}</if>
            <if test="warehouseId != null ">and warehouse_id = #{warehouseId}</if>
            <if test="warehouseCode != null  and warehouseCode != ''">and warehouse_code = #{warehouseCode}</if>
            <if test="warehouseName != null  and warehouseName != ''">and warehouse_name like concat('%',#{warehouseName}, '%')
            </if>
            <if test="locationId != null ">and location_id = #{locationId}</if>
            <if test="locationCode != null  and locationCode != ''">and location_code = #{locationCode}</if>
            <if test="locationName != null  and locationName != ''">and location_name like concat('%', #{locationName}, '%') </if>
            <if test="areaId != null ">and area_id = #{areaId}</if>
            <if test="areaCode != null  and areaCode != ''">and area_code = #{areaCode}</if>
            <if test="areaName != null  and areaName != ''">and area_name like concat('%', #{areaName}, '%')</if>
            <if test="recptDate != null ">and recpt_date = #{recptDate}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="attr1 != null  and attr1 != ''">and attr1 = #{attr1}</if>
            <if test="attr2 != null  and attr2 != ''">and attr2 = #{attr2}</if>
            <if test="attr3 != null ">and attr3 = #{attr3}</if>
            <if test="attr4 != null ">and attr4 = #{attr4}</if>
            <if test="wmsOrderNo != null  and wmsOrderNo != ''">and wms_order_no like concat('%', #{wmsOrderNo}, '%')</if>
            <if test="opsTimeBegin != null and opsTimeEnd != null">and create_time between #{opsTimeBegin} and #{opsTimeEnd}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectWmProductRecptListByTaskId" resultMap="WmProductRecptResult">
        <include refid="selectWmProductRecptVo"/>
        where task_id in
        <foreach item="item" collection="taskIdList" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </select>
</mapper>