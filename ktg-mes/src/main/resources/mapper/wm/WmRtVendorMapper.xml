<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.wm.mapper.WmRtVendorMapper">
    
    <resultMap type="WmRtVendor" id="WmRtVendorResult">
        <result property="rtId"    column="rt_id"    />
        <result property="rtCode"    column="rt_code"    />
        <result property="rtName"    column="rt_name"    />
        <result property="poCode"    column="po_code"    />
        <result property="vendorId"    column="vendor_id"    />
        <result property="vendorCode"    column="vendor_code"    />
        <result property="vendorName"    column="vendor_name"    />
        <result property="vendorNick"    column="vendor_nick"    />
        <result property="batchCode"    column="batch_code"    />
        <result property="rtDate"    column="rt_date"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="RtVendorTxBean"  id="RtVendorTxBeanResult">
        <result property="materialStockId" column="material_stock_id"></result>
        <result property="itemId" column="item_id"></result>
        <result property="itemCode" column="item_code"></result>
        <result property="itemName" column="item_name"></result>
        <result property="specification" column="specification"></result>
        <result property="unitOfMeasure" column="unit_of_measure"></result>
        <result property="batchCode" column="batch_code"></result>
        <result property="warehouseId" column="warehouse_id"></result>
        <result property="warehouseCode" column="warehouse_code"></result>
        <result property="warehouseName" column="warehouse_name"></result>
        <result property="locationId" column="location_id"></result>
        <result property="locationCode" column="location_code"></result>
        <result property="locationName" column="location_name"></result>
        <result property="areaId" column="area_id"></result>
        <result property="areaCode" column="area_code"></result>
        <result property="areaName" column="area_name"></result>
        <result property="vendorId" column="vendor_id"></result>
        <result property="vendorCode" column="vendor_code"></result>
        <result property="vendorName" column="vendor_name"></result>
        <result property="vendorNick" column="vendor_nick"></result>
        <result property="sourceDocType" column="source_doc_type"></result>
        <result property="sourceDocId" column="source_doc_id"></result>
        <result property="sourceDocCode" column="source_doc_code"></result>
        <result property="sourceDocLineId" column="source_doc_line_id"></result>
        <result property="transactionQuantity" column="transaction_quantity"></result>
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWmRtVendorVo">
        select rt_id, rt_code, rt_name, po_code, vendor_id, vendor_code, vendor_name, vendor_nick, batch_code, rt_date, status, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from wm_rt_vendor
    </sql>

    <select id="selectWmRtVendorList" parameterType="WmRtVendor" resultMap="WmRtVendorResult">
        <include refid="selectWmRtVendorVo"/>
        <where>  
            <if test="rtCode != null  and rtCode != ''"> and rt_code = #{rtCode}</if>
            <if test="rtName != null  and rtName != ''"> and rt_name like concat('%', #{rtName}, '%')</if>
            <if test="poCode != null  and poCode != ''"> and po_code = #{poCode}</if>
            <if test="vendorId != null "> and vendor_id = #{vendorId}</if>
            <if test="vendorCode != null  and vendorCode != ''"> and vendor_code = #{vendorCode}</if>
            <if test="vendorName != null  and vendorName != ''"> and vendor_name like concat('%', #{vendorName}, '%')</if>
            <if test="vendorNick != null  and vendorNick != ''"> and vendor_nick = #{vendorNick}</if>
            <if test="batchCode != null  and batchCode != ''"> and batch_code = #{batchCode}</if>
            <if test="rtDate != null "> and rt_date = #{rtDate}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectWmRtVendorByRtId" parameterType="Long" resultMap="WmRtVendorResult">
        <include refid="selectWmRtVendorVo"/>
        where rt_id = #{rtId}
    </select>

    <select id="checkCodeUnique" parameterType="WmRtVendor" resultMap="WmRtVendorResult">
        <include refid="selectWmRtVendorVo"/>
        where rt_code = #{rtCode}
    </select>

    <select id="getTxBeans" parameterType="Long" resultMap="RtVendorTxBeanResult">
        SELECT irl.material_stock_id,irl.`item_id`,irl.`item_code`,irl.`item_name`,irl.`specification`,irl.`unit_of_measure`,irl.`batch_code`,
            irl.`warehouse_id`,irl.`warehouse_code`,irl.`warehouse_name`,irl.`location_id`,irl.`location_code`,irl.`location_name`,irl.`area_id`,irl.`area_code`,irl.`area_name`,
            ir.`vendor_id`,ir.`vendor_code`,ir.`vendor_name`,ir.`vendor_nick`,
            'RTV' AS source_doc_type,ir.`rt_id` AS source_doc_id,ir.`rt_code` AS source_doc_code,irl.`line_id` AS source_doc_line_id,
            irl.`quantity_rted` AS transaction_quantity,
            ir.`create_by`,ir.`create_time`,ir.`update_by`,ir.`update_time`
        FROM wm_rt_vendor ir
           LEFT JOIN wm_rt_vendor_line irl
           ON ir.rt_id = irl.`rt_id`
        WHERE ir.`rt_id` = #{rtId}
    </select>

    <insert id="insertWmRtVendor" parameterType="WmRtVendor" useGeneratedKeys="true" keyProperty="rtId">
        insert into wm_rt_vendor
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rtCode != null and rtCode != ''">rt_code,</if>
            <if test="rtName != null and rtName != ''">rt_name,</if>
            <if test="poCode != null">po_code,</if>
            <if test="vendorId != null">vendor_id,</if>
            <if test="vendorCode != null">vendor_code,</if>
            <if test="vendorName != null">vendor_name,</if>
            <if test="vendorNick != null">vendor_nick,</if>
            <if test="batchCode != null">batch_code,</if>
            <if test="rtDate != null">rt_date,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rtCode != null and rtCode != ''">#{rtCode},</if>
            <if test="rtName != null and rtName != ''">#{rtName},</if>
            <if test="poCode != null">#{poCode},</if>
            <if test="vendorId != null">#{vendorId},</if>
            <if test="vendorCode != null">#{vendorCode},</if>
            <if test="vendorName != null">#{vendorName},</if>
            <if test="vendorNick != null">#{vendorNick},</if>
            <if test="batchCode != null">#{batchCode},</if>
            <if test="rtDate != null">#{rtDate},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWmRtVendor" parameterType="WmRtVendor">
        update wm_rt_vendor
        <trim prefix="SET" suffixOverrides=",">
            <if test="rtCode != null and rtCode != ''">rt_code = #{rtCode},</if>
            <if test="rtName != null and rtName != ''">rt_name = #{rtName},</if>
            <if test="poCode != null">po_code = #{poCode},</if>
            <if test="vendorId != null">vendor_id = #{vendorId},</if>
            <if test="vendorCode != null">vendor_code = #{vendorCode},</if>
            <if test="vendorName != null">vendor_name = #{vendorName},</if>
            <if test="vendorNick != null">vendor_nick = #{vendorNick},</if>
            <if test="batchCode != null">batch_code = #{batchCode},</if>
            <if test="rtDate != null">rt_date = #{rtDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where rt_id = #{rtId}
    </update>

    <delete id="deleteWmRtVendorByRtId" parameterType="Long">
        delete from wm_rt_vendor where rt_id = #{rtId}
    </delete>

    <delete id="deleteWmRtVendorByRtIds" parameterType="String">
        delete from wm_rt_vendor where rt_id in 
        <foreach item="rtId" collection="array" open="(" separator="," close=")">
            #{rtId}
        </foreach>
    </delete>
</mapper>