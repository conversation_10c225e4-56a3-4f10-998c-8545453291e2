<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.wm.mapper.WmStockTakingResultMapper">
    
    <resultMap type="WmStockTakingResult" id="WmStockTakingResultResult">
        <result property="resultId"    column="result_id"    />
        <result property="takingId"    column="taking_id"    />
        <result property="itemId"    column="item_id"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemName"    column="item_name"    />
        <result property="specification"    column="specification"    />
        <result property="unitOfMeasure"    column="unit_of_measure"    />
        <result property="unitName"    column="unit_name"    />
        <result property="quantity"    column="quantity"    />
        <result property="takingQuantity"    column="taking_quantity"    />
        <result property="takingStatus"    column="taking_status"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWmStockTakingResultVo">
        select result_id, taking_id, item_id, item_code, item_name, specification, unit_of_measure, unit_name, quantity, taking_quantity, taking_status, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from wm_stock_taking_result
    </sql>

    <select id="selectWmStockTakingResultList" parameterType="WmStockTakingResult" resultMap="WmStockTakingResultResult">
        <include refid="selectWmStockTakingResultVo"/>
        <where>  
            <if test="takingId != null "> and taking_id = #{takingId}</if>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code = #{itemCode}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="specification != null  and specification != ''"> and specification = #{specification}</if>
            <if test="unitOfMeasure != null  and unitOfMeasure != ''"> and unit_of_measure = #{unitOfMeasure}</if>
            <if test="unitName != null  and unitName != ''"> and unit_name like concat('%', #{unitName}, '%')</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
            <if test="takingQuantity != null "> and taking_quantity = #{takingQuantity}</if>
            <if test="takingStatus != null  and takingStatus != ''"> and taking_status = #{takingStatus}</if>
        </where>
    </select>
    
    <select id="selectWmStockTakingResultByResultId" parameterType="Long" resultMap="WmStockTakingResultResult">
        <include refid="selectWmStockTakingResultVo"/>
        where result_id = #{resultId}
    </select>

    <insert id="calculateOpenWmStockTakingResult" parameterType="Long" useGeneratedKeys="true">
        insert into wm_stock_taking_result (taking_id,item_id,item_code,item_name,specification,taking_quantity,quantity,taking_status)
        select sl.taking_id,sl.item_id,sl.item_code,sl.item_name,sl.specification,taking_quantity,quantity,
               case when IFNULL(taking_quantity,0) &gt; IFNULL(quantity,0) then 'PROFIT'
                    when   IFNULL(taking_quantity,0) &lt; IFNULL(quantity,0) then 'LOSS'
                    else 'NORMAL' end as taking_status
        from wm_stock_taking_line sl
        where sl.taking_id =  #{takingId}
    </insert>

    <insert id="calculateWmStockTakingResult" parameterType="Long" useGeneratedKeys="true">
        insert into wm_stock_taking_result (item_id,item_code,item_name,specification,taking_quantity,quantity,taking_status)
        select IFNULL(m.item_id,t.item_id) as item_id, IFNULL(m.item_code,t.item_code) as item_code,IFNULL(m.item_name,t.item_name) as item_name,IFNULL(m.specification,t.specification) as specification,
               IFNULL(t.taking_quantity,0) as taking_quantity,IFNULL(m.onhand,0) as quantity,
               case when IFNULL(t.taking_quantity,0) &gt; IFNULL(m.onhand,0) then 'PROFIT'
                    when   IFNULL(t.taking_quantity,0) &lt; IFNULL(m.onhand,0) then 'LOSS'
                    else 'NORMAL' end as taking_status
        from (
                 select sl.item_id,sl.item_code,sl.item_name,sl.specification,sum(sl.taking_quantity) as taking_quantity
                 from wm_stock_taking_line sl
                 where sl.taking_id =  #{takingId}
                 group by sl.item_id,sl.item_code,sl.item_name,sl.specification
             ) t
                 left join (
            select ms.item_id,ms.item_code,ms.item_name,ms.specification,sum(ms.quantity_onhand) as onhand
            from wm_material_stock ms
            where (ms.warehouse_id,ms.location_id,ms.area_id) in (
                select warehouse_id,location_id,area_id
                from wm_stock_taking
                where taking_id = #{takingId}
            )
            group by ms.item_id,ms.item_code,ms.item_name,ms.specification
        ) m
                           on t.item_id = m.item_id
                               and t.item_code = m.item_code
        where t.taking_quantity !=0

        union

        select IFNULL(t.item_id,m.item_id) as item_id,IFNULL(t.item_code,m.item_code) as item_code,IFNULL(t.item_name,m.item_name) as item_name,IFNULL(t.specification,m.specification) as specification,
               IFNULL(t.taking_quantity,0) as taking_quantity,IFNULL(m.onhand,0) as quantity,
               case when IFNULL(t.taking_quantity,0) &gt; IFNULL(m.onhand,0) then 'PROFIT'
                    when   IFNULL(t.taking_quantity,0) &lt; IFNULL(m.onhand,0) then 'LOSS'
                    else 'NORMAL' end as taking_status
        from (
                 select ms.item_id,ms.item_code,ms.item_name,ms.specification,sum(ms.quantity_onhand) as onhand
                 from wm_material_stock ms
                 where (ms.warehouse_id,ms.location_id,ms.area_id) in (
                     select warehouse_id,location_id,area_id
                     from wm_stock_taking
                     where taking_id = #{takingId}
                 )
                 group by ms.item_id,ms.item_code,ms.item_name,ms.specification
             ) m
                 left join (
            select sl.item_id,sl.item_code,sl.item_name,sl.specification,sum(sl.taking_quantity) as taking_quantity
            from wm_stock_taking_line sl
            where sl.taking_id =  #{takingId}
            group by sl.item_id,sl.item_code,sl.item_name,sl.specification
        ) t
                           on t.item_id = m.item_id
                               and t.item_code = m.item_code
        where m.onhand !=0
    </insert>
        
    <insert id="insertWmStockTakingResult" parameterType="WmStockTakingResult" useGeneratedKeys="true" keyProperty="resultId">
        insert into wm_stock_taking_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="takingId != null">taking_id,</if>
            <if test="itemId != null">item_id,</if>
            <if test="itemCode != null">item_code,</if>
            <if test="itemName != null">item_name,</if>
            <if test="specification != null">specification,</if>
            <if test="unitOfMeasure != null">unit_of_measure,</if>
            <if test="unitName != null">unit_name,</if>
            <if test="quantity != null">quantity,</if>
            <if test="takingQuantity != null">taking_quantity,</if>
            <if test="takingStatus != null and takingStatus != ''">taking_status,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="takingId != null">#{takingId},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="itemCode != null">#{itemCode},</if>
            <if test="itemName != null">#{itemName},</if>
            <if test="specification != null">#{specification},</if>
            <if test="unitOfMeasure != null">#{unitOfMeasure},</if>
            <if test="unitName != null">#{unitName},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="takingQuantity != null">#{takingQuantity},</if>
            <if test="takingStatus != null and takingStatus != ''">#{takingStatus},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWmStockTakingResult" parameterType="WmStockTakingResult">
        update wm_stock_taking_result
        <trim prefix="SET" suffixOverrides=",">
            <if test="takingId != null">taking_id = #{takingId},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="itemCode != null">item_code = #{itemCode},</if>
            <if test="itemName != null">item_name = #{itemName},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="unitOfMeasure != null">unit_of_measure = #{unitOfMeasure},</if>
            <if test="unitName != null">unit_name = #{unitName},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="takingQuantity != null">taking_quantity = #{takingQuantity},</if>
            <if test="takingStatus != null and takingStatus != ''">taking_status = #{takingStatus},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where result_id = #{resultId}
    </update>

    <delete id="deleteWmStockTakingResultByResultId" parameterType="Long">
        delete from wm_stock_taking_result where result_id = #{resultId}
    </delete>

    <delete id="deleteWmStockTakingResultByResultIds" parameterType="String">
        delete from wm_stock_taking_result where result_id in 
        <foreach item="resultId" collection="array" open="(" separator="," close=")">
            #{resultId}
        </foreach>
    </delete>

    <delete id="deleteWmStockTakingResultByTakingId" parameterType="Long">
        delete from wm_stock_taking_result where taking_id = #{takingId}
    </delete>
</mapper>