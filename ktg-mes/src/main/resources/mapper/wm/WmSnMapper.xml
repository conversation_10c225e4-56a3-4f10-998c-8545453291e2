<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.wm.mapper.WmSnMapper">
    
    <resultMap type="WmSn" id="WmSnResult">
        <result property="snId"    column="sn_id"    />
        <result property="snCode"    column="sn_code"    />
        <result property="itemId"    column="item_id"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemName"    column="item_name"    />
        <result property="specification"    column="specification"    />
        <result property="unitOfMeasure"    column="unit_of_measure"    />
        <result property="batchCode"    column="batch_code"    />
        <result property="snNum"     column="snNum"></result>
        <result property="workorderId"     column="workorder_id"></result>
        <result property="workorderCode"     column="workorder_code"></result>
        <result property="inputTime"     column="input_time"></result>
        <result property="genDate"   column="gen_date"></result>
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="WmSnPageVO" id="WmSnVOResult">
        <result property="ids"    column="ids"    />
        <result property="snId"    column="sn_id"    />
        <result property="snCode"    column="sn_code"    />
        <result property="itemId"    column="item_id"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemName"    column="item_name"    />
        <result property="specification"    column="specification"    />
        <result property="unitOfMeasure"    column="unit_of_measure"    />
        <result property="batchCode"    column="batch_code"    />
        <result property="snNum"     column="snNum"></result>
        <result property="workorderId"     column="workorder_id"></result>
        <result property="workorderCode"     column="workorder_code"></result>
        <result property="inputTime"     column="input_time"></result>
        <result property="genDate"   column="gen_date"></result>
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWmSnVo">
        select sn_id, sn_code, item_id, item_code, item_name, specification, unit_of_measure, batch_code, workorder_id, gen_date, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from wm_sn
    </sql>

    <select id="selectWmSnList" parameterType="WmSn" resultMap="WmSnVOResult">
        <include refid="selectWmSnVo"/>
        <where>  
            <if test="snCode != null  and snCode != ''"> and sn_code = #{snCode}</if>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code = #{itemCode}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="specification != null  and specification != ''"> and specification = #{specification}</if>
            <if test="unitOfMeasure != null  and unitOfMeasure != ''"> and unit_of_measure = #{unitOfMeasure}</if>
            <if test="batchCode != null  and batchCode != ''"> and batch_code = #{batchCode}</if>
            <if test="workorderId != null  and workorderId != ''"> and workorder_id = #{workorderId}</if>
        </where>
    </select>
    
    <select id="selectWmSnBySnId" parameterType="Long" resultMap="WmSnResult">
        <include refid="selectWmSnVo"/>
        where sn_id = #{snId}
    </select>

    <select id="selectSnList" parameterType="WmSn" resultMap="WmSnResult">
        select item_id, item_code, item_name, specification, unit_of_measure, batch_code,gen_date , count(*) as snNum
        from wm_sn
        <where>
            <if test="snCode != null  and snCode != ''"> and sn_code = #{snCode}</if>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code = #{itemCode}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="specification != null  and specification != ''"> and specification = #{specification}</if>
            <if test="unitOfMeasure != null  and unitOfMeasure != ''"> and unit_of_measure = #{unitOfMeasure}</if>
            <if test="batchCode != null  and batchCode != ''"> and batch_code = #{batchCode}</if>
        </where>
        group by item_id, item_code, item_name, specification, unit_of_measure, batch_code, gen_date
        order by item_id,batch_code desc
    </select>

    <select id="getStationList" parameterType="WmSn" resultMap="WmSnResult">
        select sn.sn_id,sn.sn_code,sn.workorder_id,wo.workorder_code, sn.item_id,sn.item_code,sn.item_name,sn.specification,sn.unit_of_measure,ps.input_time
        from wm_sn sn
                 left join pro_sn_process ps
                           on sn.sn_id = ps.sn_id
                 left join pro_workorder wo
                           on sn.workorder_id = wo.workorder_id
        where sn.workorder_id =#{workorderId}
          and ps.workstation_id = #{workstationId}
        order by ps.input_time desc
    </select>
    <select id="getWmSnList" parameterType="WmSn" resultMap="WmSnVOResult">
        select group_concat(sn_id) as ids, item_id, item_code, item_name, specification, unit_of_measure, batch_code,gen_date , count(*) as snNum
        from wm_sn
        <where>
            <if test="snCode != null  and snCode != ''"> and sn_code = #{snCode}</if>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code = #{itemCode}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="specification != null  and specification != ''"> and specification = #{specification}</if>
            <if test="unitOfMeasure != null  and unitOfMeasure != ''"> and unit_of_measure = #{unitOfMeasure}</if>
            <if test="batchCode != null  and batchCode != ''"> and batch_code = #{batchCode}</if>
        </where>
        group by item_id, item_code, item_name, specification, unit_of_measure, batch_code, gen_date
        order by item_id,batch_code desc
    </select>


    <insert id="insertWmSn" parameterType="WmSn" useGeneratedKeys="true" keyProperty="snId">
        insert into wm_sn
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="snCode != null and snCode != ''">sn_code,</if>
            <if test="itemId != null">item_id,</if>
            <if test="itemCode != null">item_code,</if>
            <if test="itemName != null">item_name,</if>
            <if test="specification != null">specification,</if>
            <if test="unitOfMeasure != null">unit_of_measure,</if>
            <if test="batchCode != null">batch_code,</if>
            <if test="workorderId != null">workorder_id,</if>
            <if test="genDate !=null">gen_date,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="snCode != null and snCode != ''">#{snCode},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="itemCode != null">#{itemCode},</if>
            <if test="itemName != null">#{itemName},</if>
            <if test="specification != null">#{specification},</if>
            <if test="unitOfMeasure != null">#{unitOfMeasure},</if>
            <if test="batchCode != null">#{batchCode},</if>
            <if test="workorderId != null">#{workorderId},</if>
            <if test="genDate !=null">#{genDate},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWmSn" parameterType="WmSn">
        update wm_sn
        <trim prefix="SET" suffixOverrides=",">
            <if test="snCode != null and snCode != ''">sn_code = #{snCode},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="itemCode != null">item_code = #{itemCode},</if>
            <if test="itemName != null">item_name = #{itemName},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="unitOfMeasure != null">unit_of_measure = #{unitOfMeasure},</if>
            <if test="batchCode != null">batch_code = #{batchCode},</if>
            <if test="workorderId != null">workorder_id = #{workorderId},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where sn_id = #{snId}
    </update>

    <delete id="deleteWmSnBySnId" parameterType="Long">
        delete from wm_sn where sn_id = #{snId}
    </delete>

    <delete id="deleteWmSnBySnIds" parameterType="String">
        delete from wm_sn where sn_id in 
        <foreach  item="item" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
</mapper>