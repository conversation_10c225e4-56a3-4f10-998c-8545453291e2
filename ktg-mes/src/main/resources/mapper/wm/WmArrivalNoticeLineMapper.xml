<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.wm.mapper.WmArrivalNoticeLineMapper">

    <resultMap type="WmArrivalNoticeLine" id="WmArrivalNoticeLineResult">
        <result property="lineId"    column="line_id"    />
        <result property="noticeId"    column="notice_id"    />
        <result property="lineNo"    column="line_no"    />
        <result property="itemId"    column="item_id"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemName"    column="item_name"    />
        <result property="specification"    column="specification"    />
        <result property="unitOfMeasure"    column="unit_of_measure"    />
        <result property="quantityArrival"    column="quantity_arrival"    />
        <result property="quantityQuanlified"    column="quantity_quanlified"    />
        <result property="produceBatchCode"    column="produce_batch_code"    />
        <result property="planQuantity"    column="plan_quantity"    />
        <result property="iqcCheck"    column="iqc_check"    />
        <result property="iqcId"    column="iqc_id"    />
        <result property="iqcCode"    column="iqc_code"    />
        <result property="planTorr"    column="plan_torr"    />
        <result property="torr"    column="torr"    />
        <result property="planWeight"    column="plan_weight"    />
        <result property="totalWeight"    column="total_weight"    />
        <result property="planSkuWrap"    column="plan_sku_wrap"    />
        <result property="skuWarp"    column="sku_warp"    />
        <result property="remark"    column="remark"    />
        <result property="errorRemark"    column="error_remark"    />
        <result property="errorAttach"    column="error_attach"    />
        <result property="beforeAdjustQuantity"    column="before_adjust_quantity"    />
        <result property="afterAdjustQuantity"    column="after_adjust_quantity"    />
        <result property="adjustDetail"    column="adjust_detail"    />
        <result property="attachListJson"    column="attach_list_json"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWmArrivalNoticeLineVo">
        select line_id, notice_id, line_no, item_id, item_code, item_name, specification, unit_of_measure, quantity_arrival, quantity_quanlified, produce_batch_code, plan_quantity, iqc_check, iqc_id, iqc_code, plan_torr, torr, plan_weight, total_weight, plan_sku_wrap, sku_warp, remark, error_remark, error_attach, before_adjust_quantity, after_adjust_quantity, adjust_detail, attach_list_json, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from wm_arrival_notice_line
    </sql>

    <select id="selectWmArrivalNoticeLineList" parameterType="WmArrivalNoticeLine" resultMap="WmArrivalNoticeLineResult">
        <include refid="selectWmArrivalNoticeLineVo"/>
        <where>
            <if test="noticeId != null "> and notice_id = #{noticeId}</if>
            <if test="lineNo != null  and lineNo != ''"> and line_no = #{lineNo}</if>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code = #{itemCode}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="specification != null  and specification != ''"> and specification = #{specification}</if>
            <if test="unitOfMeasure != null  and unitOfMeasure != ''"> and unit_of_measure = #{unitOfMeasure}</if>
            <if test="quantityArrival != null "> and quantity_arrival = #{quantityArrival}</if>
            <if test="quantityQuanlified != null "> and quantity_quanlified = #{quantityQuanlified}</if>
            <if test="produceBatchCode != null  and produceBatchCode != ''"> and produce_batch_code = #{produceBatchCode}</if>
            <if test="planQuantity != null "> and plan_quantity = #{planQuantity}</if>
            <if test="iqcCheck != null  and iqcCheck != ''"> and iqc_check = #{iqcCheck}</if>
            <if test="iqcId != null "> and iqc_id = #{iqcId}</if>
            <if test="iqcCode != null  and iqcCode != ''"> and iqc_code = #{iqcCode}</if>
            <if test="planTorr != null "> and plan_torr = #{planTorr}</if>
            <if test="torr != null "> and torr = #{torr}</if>
            <if test="planWeight != null "> and plan_weight = #{planWeight}</if>
            <if test="totalWeight != null "> and total_weight = #{totalWeight}</if>
            <if test="planSkuWrap != null  and planSkuWrap != ''"> and plan_sku_wrap = #{planSkuWrap}</if>
            <if test="skuWarp != null  and skuWarp != ''"> and sku_warp = #{skuWarp}</if>
            <if test="errorRemark != null  and errorRemark != ''"> and error_remark = #{errorRemark}</if>
            <if test="errorAttach != null  and errorAttach != ''"> and error_attach = #{errorAttach}</if>
            <if test="beforeAdjustQuantity != null "> and before_adjust_quantity = #{beforeAdjustQuantity}</if>
            <if test="afterAdjustQuantity != null "> and after_adjust_quantity = #{afterAdjustQuantity}</if>
            <if test="adjustDetail != null  and adjustDetail != ''"> and adjust_detail = #{adjustDetail}</if>
            <if test="attachListJson != null  and attachListJson != ''"> and attach_list_json = #{attachListJson}</if>
            <if test="attr1 != null  and attr1 != ''"> and attr1 = #{attr1}</if>
            <if test="attr2 != null  and attr2 != ''"> and attr2 = #{attr2}</if>
            <if test="attr3 != null "> and attr3 = #{attr3}</if>
            <if test="attr4 != null "> and attr4 = #{attr4}</if>
        </where>
    </select>

    <select id="selectWmArrivalNoticeLineByLineId" parameterType="Long" resultMap="WmArrivalNoticeLineResult">
        <include refid="selectWmArrivalNoticeLineVo"/>
        where line_id = #{lineId}
    </select>

    <insert id="insertWmArrivalNoticeLine" parameterType="WmArrivalNoticeLine" useGeneratedKeys="true" keyProperty="lineId">
        insert into wm_arrival_notice_line
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="noticeId != null">notice_id,</if>
            <if test="lineNo != null">line_no,</if>
            <if test="itemId != null">item_id,</if>
            <if test="itemCode != null">item_code,</if>
            <if test="itemName != null">item_name,</if>
            <if test="specification != null">specification,</if>
            <if test="unitOfMeasure != null">unit_of_measure,</if>
            <if test="quantityArrival != null">quantity_arrival,</if>
            <if test="quantityQuanlified != null">quantity_quanlified,</if>
            <if test="produceBatchCode != null">produce_batch_code,</if>
            <if test="planQuantity != null">plan_quantity,</if>
            <if test="iqcCheck != null">iqc_check,</if>
            <if test="iqcId != null">iqc_id,</if>
            <if test="iqcCode != null">iqc_code,</if>
            <if test="planTorr != null">plan_torr,</if>
            <if test="torr != null">torr,</if>
            <if test="planWeight != null">plan_weight,</if>
            <if test="totalWeight != null">total_weight,</if>
            <if test="planSkuWrap != null">plan_sku_wrap,</if>
            <if test="skuWarp != null">sku_warp,</if>
            <if test="remark != null">remark,</if>
            <if test="errorRemark != null">error_remark,</if>
            <if test="errorAttach != null">error_attach,</if>
            <if test="beforeAdjustQuantity != null">before_adjust_quantity,</if>
            <if test="afterAdjustQuantity != null">after_adjust_quantity,</if>
            <if test="adjustDetail != null">adjust_detail,</if>
            <if test="attachListJson != null">attach_list_json,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="noticeId != null">#{noticeId},</if>
            <if test="lineNo != null">#{lineNo},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="itemCode != null">#{itemCode},</if>
            <if test="itemName != null">#{itemName},</if>
            <if test="specification != null">#{specification},</if>
            <if test="unitOfMeasure != null">#{unitOfMeasure},</if>
            <if test="quantityArrival != null">#{quantityArrival},</if>
            <if test="quantityQuanlified != null">#{quantityQuanlified},</if>
            <if test="produceBatchCode != null">#{produceBatchCode},</if>
            <if test="planQuantity != null">#{planQuantity},</if>
            <if test="iqcCheck != null">#{iqcCheck},</if>
            <if test="iqcId != null">#{iqcId},</if>
            <if test="iqcCode != null">#{iqcCode},</if>
            <if test="planTorr != null">#{planTorr},</if>
            <if test="torr != null">#{torr},</if>
            <if test="planWeight != null">#{planWeight},</if>
            <if test="totalWeight != null">#{totalWeight},</if>
            <if test="planSkuWrap != null">#{planSkuWrap},</if>
            <if test="skuWarp != null">#{skuWarp},</if>
            <if test="remark != null">#{remark},</if>
            <if test="errorRemark != null">#{errorRemark},</if>
            <if test="errorAttach != null">#{errorAttach},</if>
            <if test="beforeAdjustQuantity != null">#{beforeAdjustQuantity},</if>
            <if test="afterAdjustQuantity != null">#{afterAdjustQuantity},</if>
            <if test="adjustDetail != null">#{adjustDetail},</if>
            <if test="attachListJson != null">#{attachListJson},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateWmArrivalNoticeLine" parameterType="WmArrivalNoticeLine">
        update wm_arrival_notice_line
        <trim prefix="SET" suffixOverrides=",">
            <if test="noticeId != null">notice_id = #{noticeId},</if>
            <if test="lineNo != null">line_no = #{lineNo},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="itemCode != null">item_code = #{itemCode},</if>
            <if test="itemName != null">item_name = #{itemName},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="unitOfMeasure != null">unit_of_measure = #{unitOfMeasure},</if>
            <if test="quantityArrival != null">quantity_arrival = #{quantityArrival},</if>
            <if test="quantityQuanlified != null">quantity_quanlified = #{quantityQuanlified},</if>
            <if test="produceBatchCode != null">produce_batch_code = #{produceBatchCode},</if>
            <if test="planQuantity != null">plan_quantity = #{planQuantity},</if>
            <if test="iqcCheck != null">iqc_check = #{iqcCheck},</if>
            <if test="iqcId != null">iqc_id = #{iqcId},</if>
            <if test="iqcCode != null">iqc_code = #{iqcCode},</if>
            <if test="planTorr != null">plan_torr = #{planTorr},</if>
            <if test="torr != null">torr = #{torr},</if>
            <if test="planWeight != null">plan_weight = #{planWeight},</if>
            <if test="totalWeight != null">total_weight = #{totalWeight},</if>
            <if test="planSkuWrap != null">plan_sku_wrap = #{planSkuWrap},</if>
            <if test="skuWarp != null">sku_warp = #{skuWarp},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="errorRemark != null">error_remark = #{errorRemark},</if>
            <if test="errorAttach != null">error_attach = #{errorAttach},</if>
            <if test="beforeAdjustQuantity != null">before_adjust_quantity = #{beforeAdjustQuantity},</if>
            <if test="afterAdjustQuantity != null">after_adjust_quantity = #{afterAdjustQuantity},</if>
            <if test="adjustDetail != null">adjust_detail = #{adjustDetail},</if>
            <if test="attachListJson != null">attach_list_json = #{attachListJson},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where line_id = #{lineId}
    </update>

    <delete id="deleteWmArrivalNoticeLineByLineId" parameterType="Long">
        delete from wm_arrival_notice_line where line_id = #{lineId}
    </delete>

    <delete id="deleteWmArrivalNoticeLineByLineIds" parameterType="String">
        delete from wm_arrival_notice_line where line_id in
        <foreach item="lineId" collection="array" open="(" separator="," close=")">
            #{lineId}
        </foreach>
    </delete>
</mapper>