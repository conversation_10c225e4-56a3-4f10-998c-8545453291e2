<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.wm.mapper.WmItemRecptMapper">

    <resultMap type="WmItemRecpt" id="WmItemRecptResult">
        <result property="recptId"    column="recpt_id"    />
        <result property="recptCode"    column="recpt_code"    />
        <result property="recptName"    column="recpt_name"    />
        <result property="iqcId"    column="iqc_id"    />
        <result property="iqcCode"    column="iqc_code"    />
        <result property="noticeId"    column="notice_id"    />
        <result property="noticeCode"    column="notice_code"    />
        <result property="poCode"    column="po_code"    />
        <result property="vendorId"    column="vendor_id"    />
        <result property="vendorCode"    column="vendor_code"    />
        <result property="vendorName"    column="vendor_name"    />
        <result property="vendorNick"    column="vendor_nick"    />
        <result property="warehouseId"    column="warehouse_id"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="locationId"    column="location_id"    />
        <result property="locationCode"    column="location_code"    />
        <result property="locationName"    column="location_name"    />
        <result property="areaId"    column="area_id"    />
        <result property="areaCode"    column="area_code"    />
        <result property="areaName"    column="area_name"    />
        <result property="recptDate"    column="recpt_date"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="ItemRecptTxBean"  id="ItemRecptTxBeanResult">
        <result property="itemId" column="item_id"></result>
        <result property="itemCode" column="item_code"></result>
        <result property="itemName" column="item_name"></result>
        <result property="specification" column="specification"></result>
        <result property="unitOfMeasure" column="unit_of_measure"></result>
        <result property="batchCode" column="batch_code"></result>
        <result property="warehouseId" column="warehouse_id"></result>
        <result property="warehouseCode" column="warehouse_code"></result>
        <result property="warehouseName" column="warehouse_name"></result>
        <result property="locationId" column="location_id"></result>
        <result property="locationCode" column="location_code"></result>
        <result property="locationName" column="location_name"></result>
        <result property="areaId" column="area_id"></result>
        <result property="areaCode" column="area_code"></result>
        <result property="areaName" column="area_name"></result>
        <result property="vendorId" column="vendor_id"></result>
        <result property="vendorCode" column="vendor_code"></result>
        <result property="vendorName" column="vendor_name"></result>
        <result property="vendorNick" column="vendor_nick"></result>
        <result property="sourceDocType" column="source_doc_type"></result>
        <result property="sourceDocId" column="source_doc_id"></result>
        <result property="sourceDocCode" column="source_doc_code"></result>
        <result property="sourceDocLineId" column="source_doc_line_id"></result>
        <result property="transactionQuantity" column="transaction_quantity"></result>
        <result property="recptDate" column="recpt_date"></result>
        <result property="expireDate" column="expire_date"></result>
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>


    <sql id="selectWmItemRecptVo">
        select recpt_id, recpt_code, recpt_name, iqc_id, iqc_code, notice_id, notice_code,po_code, vendor_id, vendor_code, vendor_name, vendor_nick, warehouse_id, warehouse_code, warehouse_name, location_id, location_code, location_name, area_id, area_code, area_name, recpt_date, status, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from wm_item_recpt
    </sql>

    <select id="selectWmItemRecptList" parameterType="WmItemRecpt" resultMap="WmItemRecptResult">
        <include refid="selectWmItemRecptVo"/>
        <where>
            <if test="recptCode != null  and recptCode != ''"> and recpt_code = #{recptCode}</if>
            <if test="recptName != null  and recptName != ''"> and recpt_name like concat('%', #{recptName}, '%')</if>
            <if test="iqcId != null "> and iqc_id = #{iqcId}</if>
            <if test="iqcCode != null  and iqcCode != ''"> and iqc_code = #{iqcCode}</if>
            <if test="noticeId != null "> and notice_id = #{noticeId}</if>
            <if test="noticeCode != null  and noticeCode != ''"> and notice_code = #{noticeCode}</if>
            <if test="poCode != null  and poCode != ''"> and po_code = #{poCode}</if>
            <if test="vendorId != null "> and vendor_id = #{vendorId}</if>
            <if test="vendorCode != null  and vendorCode != ''"> and vendor_code = #{vendorCode}</if>
            <if test="vendorName != null  and vendorName != ''"> and vendor_name like concat('%', #{vendorName}, '%')</if>
            <if test="vendorNick != null  and vendorNick != ''"> and vendor_nick = #{vendorNick}</if>
            <if test="warehouseId != null "> and warehouse_id = #{warehouseId}</if>
            <if test="warehouseCode != null  and warehouseCode != ''"> and warehouse_code = #{warehouseCode}</if>
            <if test="warehouseName != null  and warehouseName != ''"> and warehouse_name like concat('%', #{warehouseName}, '%')</if>
            <if test="locationId != null "> and location_id = #{locationId}</if>
            <if test="locationCode != null  and locationCode != ''"> and location_code = #{locationCode}</if>
            <if test="locationName != null  and locationName != ''"> and location_name like concat('%', #{locationName}, '%')</if>
            <if test="areaId != null "> and area_id = #{areaId}</if>
            <if test="areaCode != null  and areaCode != ''"> and area_code = #{areaCode}</if>
            <if test="areaName != null  and areaName != ''"> and area_name like concat('%', #{areaName}, '%')</if>
            <if test="recptDate != null "> and recpt_date = #{recptDate}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectWmItemRecptByRecptId" parameterType="Long" resultMap="WmItemRecptResult">
        <include refid="selectWmItemRecptVo"/>
        where recpt_id = #{recptId}
    </select>


    <select id="checkRecptCodeUnique" parameterType="WmItemRecpt" resultMap="WmItemRecptResult">
        <include refid="selectWmItemRecptVo"/>
        where recpt_code = #{recptCode}
    </select>

    <select id="getTxBeans" parameterType="Long" resultMap="ItemRecptTxBeanResult">
        SELECT irl.`item_id`,irl.`item_code`,irl.`item_name`,irl.`specification`,irl.`unit_of_measure`,irl.`batch_code`,
            irl.`warehouse_id`,irl.`warehouse_code`,irl.`warehouse_name`,irl.`location_id`,irl.`location_code`,irl.`location_name`,irl.`area_id`,irl.`area_code`,irl.`area_name`,
            ir.`vendor_id`,ir.`vendor_code`,ir.`vendor_name`,ir.`vendor_nick`,
            'IR' AS source_doc_type,ir.`recpt_id` AS source_doc_id,ir.`recpt_code` AS source_doc_code,irl.`line_id` AS source_doc_line_id,
            irl.`quantity_recived` AS transaction_quantity,ir.recpt_date, irl.`expire_date`,
            ir.`create_by`,ir.`create_time`,ir.`update_by`,ir.`update_time`
        FROM wm_item_recpt ir
           LEFT JOIN wm_item_recpt_line irl
           ON ir.recpt_id = irl.`recpt_id`
        WHERE ir.`recpt_id` = #{recptId}
    </select>


    <insert id="insertWmItemRecpt" parameterType="WmItemRecpt" useGeneratedKeys="true" keyProperty="recptId">
        insert into wm_item_recpt
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recptCode != null and recptCode != ''">recpt_code,</if>
            <if test="recptName != null and recptName != ''">recpt_name,</if>
            <if test="iqcId != null">iqc_id,</if>
            <if test="iqcCode != null">iqc_code,</if>
            <if test="noticeId != null">notice_id,</if>
            <if test="noticeCode != null">notice_code,</if>
            <if test="poCode != null">po_code,</if>
            <if test="vendorId != null">vendor_id,</if>
            <if test="vendorCode != null">vendor_code,</if>
            <if test="vendorName != null">vendor_name,</if>
            <if test="vendorNick != null">vendor_nick,</if>
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="warehouseCode != null">warehouse_code,</if>
            <if test="warehouseName != null">warehouse_name,</if>
            <if test="locationId != null">location_id,</if>
            <if test="locationCode != null">location_code,</if>
            <if test="locationName != null">location_name,</if>
            <if test="areaId != null">area_id,</if>
            <if test="areaCode != null">area_code,</if>
            <if test="areaName != null">area_name,</if>
            <if test="recptDate != null">recpt_date,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="recptCode != null and recptCode != ''">#{recptCode},</if>
            <if test="recptName != null and recptName != ''">#{recptName},</if>
            <if test="iqcId != null">#{iqcId},</if>
            <if test="iqcCode != null">#{iqcCode},</if>
            <if test="noticeId != null">#{noticeId},</if>
            <if test="noticeCode != null">#{noticeCode},</if>
            <if test="poCode != null">#{poCode},</if>
            <if test="vendorId != null">#{vendorId},</if>
            <if test="vendorCode != null">#{vendorCode},</if>
            <if test="vendorName != null">#{vendorName},</if>
            <if test="vendorNick != null">#{vendorNick},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="warehouseCode != null">#{warehouseCode},</if>
            <if test="warehouseName != null">#{warehouseName},</if>
            <if test="locationId != null">#{locationId},</if>
            <if test="locationCode != null">#{locationCode},</if>
            <if test="locationName != null">#{locationName},</if>
            <if test="areaId != null">#{areaId},</if>
            <if test="areaCode != null">#{areaCode},</if>
            <if test="areaName != null">#{areaName},</if>
            <if test="recptDate != null">#{recptDate},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateWmItemRecpt" parameterType="WmItemRecpt">
        update wm_item_recpt
        <trim prefix="SET" suffixOverrides=",">
            <if test="recptCode != null and recptCode != ''">recpt_code = #{recptCode},</if>
            <if test="recptName != null and recptName != ''">recpt_name = #{recptName},</if>
            <if test="iqcId != null">iqc_id = #{iqcId},</if>
            <if test="iqcCode != null">iqc_code = #{iqcCode},</if>
            <if test="noticeId != null">notice_id = #{noticeId},</if>
            <if test="noticeCode != null">notice_code = #{noticeCode},</if>
            <if test="poCode != null">po_code = #{poCode},</if>
            <if test="vendorId != null">vendor_id = #{vendorId},</if>
            <if test="vendorCode != null">vendor_code = #{vendorCode},</if>
            <if test="vendorName != null">vendor_name = #{vendorName},</if>
            <if test="vendorNick != null">vendor_nick = #{vendorNick},</if>
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="warehouseCode != null">warehouse_code = #{warehouseCode},</if>
            <if test="warehouseName != null">warehouse_name = #{warehouseName},</if>
            <if test="locationId != null">location_id = #{locationId},</if>
            <if test="locationCode != null">location_code = #{locationCode},</if>
            <if test="locationName != null">location_name = #{locationName},</if>
            <if test="areaId != null">area_id = #{areaId},</if>
            <if test="areaCode != null">area_code = #{areaCode},</if>
            <if test="areaName != null">area_name = #{areaName},</if>
            <if test="recptDate != null">recpt_date = #{recptDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where recpt_id = #{recptId}
    </update>

    <delete id="deleteWmItemRecptByRecptId" parameterType="Long">
        delete from wm_item_recpt where recpt_id = #{recptId}
    </delete>

    <delete id="deleteWmItemRecptByRecptIds" parameterType="String">
        delete from wm_item_recpt where recpt_id in
        <foreach item="recptId" collection="array" open="(" separator="," close=")">
            #{recptId}
        </foreach>
    </delete>
</mapper>