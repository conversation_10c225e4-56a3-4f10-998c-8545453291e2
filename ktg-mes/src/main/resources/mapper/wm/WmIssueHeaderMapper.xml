<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.wm.mapper.WmIssueHeaderMapper">

    <resultMap type="WmIssueHeader" id="WmIssueHeaderResult">
        <result property="issueId"    column="issue_id"    />
        <result property="issueCode"    column="issue_code"    />
        <result property="issueName"    column="issue_name"    />
        <result property="workstationId"    column="workstation_id"    />
        <result property="workstationCode"    column="workstation_code"    />
        <result property="workstationName"    column="workstation_name"    />
        <result property="workorderId"    column="workorder_id"    />
        <result property="workorderCode"    column="workorder_code"    />
        <result property="taskId"    column="task_id"    />
        <result property="taskCode"    column="task_code"    />
        <result property="workshopId"    column="workshop_id"    />
        <result property="workshopName"    column="workshop_name"    />
        <result property="clientId"    column="client_id"    />
        <result property="clientCode"    column="client_code"    />
        <result property="clientName"    column="client_name"    />
        <result property="clientNick"    column="client_nick"    />
        <result property="warehouseId"    column="warehouse_id"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="ownerCode"    column="owner_code"    />
        <result property="ownerName"    column="owner_name"    />
        <result property="locationId"    column="location_id"    />
        <result property="locationCode"    column="location_code"    />
        <result property="locationName"    column="location_name"    />
        <result property="areaId"    column="area_id"    />
        <result property="areaCode"    column="area_code"    />
        <result property="areaName"    column="area_name"    />
        <result property="issueDate"    column="issue_date"    />
        <result property="status"    column="status"    />
        <result property="wmsOrderNo"    column="wms_order_no"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWmIssueHeaderVo">
        select issue_id, issue_code, issue_name, workstation_id, workstation_code, workstation_name, workorder_id, workorder_code, task_id, task_code, workshop_id, workshop_name, client_id, client_code, client_name, client_nick, warehouse_id, warehouse_code, warehouse_name, owner_code, owner_name, location_id, location_code, location_name, area_id, area_code, area_name, issue_date, status, wms_order_no, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from wm_issue_header
    </sql>

    <select id="selectWmIssueHeaderList" parameterType="WmIssueHeader" resultMap="WmIssueHeaderResult">
        <include refid="selectWmIssueHeaderVo"/>
        <where>
            <if test="issueCode != null  and issueCode != ''"> and issue_code = #{issueCode}</if>
            <if test="issueName != null  and issueName != ''"> and issue_name like concat('%', #{issueName}, '%')</if>
            <if test="workstationId != null "> and workstation_id = #{workstationId}</if>
            <if test="workstationCode != null  and workstationCode != ''"> and workstation_code = #{workstationCode}</if>
            <if test="workstationName != null  and workstationName != ''"> and workstation_name like concat('%', #{workstationName}, '%')</if>
            <if test="workorderId != null "> and workorder_id = #{workorderId}</if>
            <if test="workorderCode != null  and workorderCode != ''"> and workorder_code = #{workorderCode}</if>
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="taskCode != null  and taskCode != ''"> and task_code = #{taskCode}</if>
            <if test="workshopId != null "> and workshop_id = #{workshopId}</if>
            <if test="workshopName != null  and workshopName != ''"> and workshop_name like concat('%', #{workshopName}, '%')</if>
            <if test="clientId != null "> and client_id = #{clientId}</if>
            <if test="clientCode != null  and clientCode != ''"> and client_code = #{clientCode}</if>
            <if test="clientName != null  and clientName != ''"> and client_name like concat('%', #{clientName}, '%')</if>
            <if test="clientNick != null  and clientNick != ''"> and client_nick = #{clientNick}</if>
            <if test="warehouseId != null "> and warehouse_id = #{warehouseId}</if>
            <if test="warehouseCode != null  and warehouseCode != ''"> and warehouse_code = #{warehouseCode}</if>
            <if test="warehouseName != null  and warehouseName != ''"> and warehouse_name like concat('%', #{warehouseName}, '%')</if>
            <if test="ownerCode != null  and ownerCode != ''"> and owner_code = #{ownerCode}</if>
            <if test="ownerName != null  and ownerName != ''"> and owner_name like concat('%', #{ownerName}, '%')</if>
            <if test="locationId != null "> and location_id = #{locationId}</if>
            <if test="locationCode != null  and locationCode != ''"> and location_code = #{locationCode}</if>
            <if test="locationName != null  and locationName != ''"> and location_name like concat('%', #{locationName}, '%')</if>
            <if test="areaId != null "> and area_id = #{areaId}</if>
            <if test="areaCode != null  and areaCode != ''"> and area_code = #{areaCode}</if>
            <if test="areaName != null  and areaName != ''"> and area_name like concat('%', #{areaName}, '%')</if>
            <if test="issueDate != null "> and issue_date = #{issueDate}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="wmsOrderNo != null  and wmsOrderNo != ''"> and wms_order_no = #{wmsOrderNo}</if>
            <if test="attr1 != null  and attr1 != ''"> and attr1 = #{attr1}</if>
            <if test="attr2 != null  and attr2 != ''"> and attr2 = #{attr2}</if>
            <if test="attr3 != null "> and attr3 = #{attr3}</if>
            <if test="attr4 != null "> and attr4 = #{attr4}</if>
        </where>
    </select>

    <select id="selectWmIssueHeaderByIssueId" parameterType="Long" resultMap="WmIssueHeaderResult">
        <include refid="selectWmIssueHeaderVo"/>
        where issue_id = #{issueId}
    </select>

    <insert id="insertWmIssueHeader" parameterType="WmIssueHeader" useGeneratedKeys="true" keyProperty="issueId">
        insert into wm_issue_header
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="issueCode != null and issueCode != ''">issue_code,</if>
            <if test="issueName != null and issueName != ''">issue_name,</if>
            <if test="workstationId != null">workstation_id,</if>
            <if test="workstationCode != null">workstation_code,</if>
            <if test="workstationName != null">workstation_name,</if>
            <if test="workorderId != null">workorder_id,</if>
            <if test="workorderCode != null">workorder_code,</if>
            <if test="taskId != null">task_id,</if>
            <if test="taskCode != null">task_code,</if>
            <if test="workshopId != null">workshop_id,</if>
            <if test="workshopName != null">workshop_name,</if>
            <if test="clientId != null">client_id,</if>
            <if test="clientCode != null">client_code,</if>
            <if test="clientName != null">client_name,</if>
            <if test="clientNick != null">client_nick,</if>
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="warehouseCode != null">warehouse_code,</if>
            <if test="warehouseName != null">warehouse_name,</if>
            <if test="ownerCode != null">owner_code,</if>
            <if test="ownerName != null">owner_name,</if>
            <if test="locationId != null">location_id,</if>
            <if test="locationCode != null">location_code,</if>
            <if test="locationName != null">location_name,</if>
            <if test="areaId != null">area_id,</if>
            <if test="areaCode != null">area_code,</if>
            <if test="areaName != null">area_name,</if>
            <if test="issueDate != null">issue_date,</if>
            <if test="status != null">status,</if>
            <if test="wmsOrderNo != null">wms_order_no,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="issueCode != null and issueCode != ''">#{issueCode},</if>
            <if test="issueName != null and issueName != ''">#{issueName},</if>
            <if test="workstationId != null">#{workstationId},</if>
            <if test="workstationCode != null">#{workstationCode},</if>
            <if test="workstationName != null">#{workstationName},</if>
            <if test="workorderId != null">#{workorderId},</if>
            <if test="workorderCode != null">#{workorderCode},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="taskCode != null">#{taskCode},</if>
            <if test="workshopId != null">#{workshopId},</if>
            <if test="workshopName != null">#{workshopName},</if>
            <if test="clientId != null">#{clientId},</if>
            <if test="clientCode != null">#{clientCode},</if>
            <if test="clientName != null">#{clientName},</if>
            <if test="clientNick != null">#{clientNick},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="warehouseCode != null">#{warehouseCode},</if>
            <if test="warehouseName != null">#{warehouseName},</if>
            <if test="ownerCode != null">#{ownerCode},</if>
            <if test="ownerName != null">#{ownerName},</if>
            <if test="locationId != null">#{locationId},</if>
            <if test="locationCode != null">#{locationCode},</if>
            <if test="locationName != null">#{locationName},</if>
            <if test="areaId != null">#{areaId},</if>
            <if test="areaCode != null">#{areaCode},</if>
            <if test="areaName != null">#{areaName},</if>
            <if test="issueDate != null">#{issueDate},</if>
            <if test="status != null">#{status},</if>
            <if test="wmsOrderNo != null">#{wmsOrderNo},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateWmIssueHeader" parameterType="WmIssueHeader">
        update wm_issue_header
        <trim prefix="SET" suffixOverrides=",">
            <if test="issueCode != null and issueCode != ''">issue_code = #{issueCode},</if>
            <if test="issueName != null and issueName != ''">issue_name = #{issueName},</if>
            <if test="workstationId != null">workstation_id = #{workstationId},</if>
            <if test="workstationCode != null">workstation_code = #{workstationCode},</if>
            <if test="workstationName != null">workstation_name = #{workstationName},</if>
            <if test="workorderId != null">workorder_id = #{workorderId},</if>
            <if test="workorderCode != null">workorder_code = #{workorderCode},</if>
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="taskCode != null">task_code = #{taskCode},</if>
            <if test="workshopId != null">workshop_id = #{workshopId},</if>
            <if test="workshopName != null">workshop_name = #{workshopName},</if>
            <if test="clientId != null">client_id = #{clientId},</if>
            <if test="clientCode != null">client_code = #{clientCode},</if>
            <if test="clientName != null">client_name = #{clientName},</if>
            <if test="clientNick != null">client_nick = #{clientNick},</if>
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="warehouseCode != null">warehouse_code = #{warehouseCode},</if>
            <if test="warehouseName != null">warehouse_name = #{warehouseName},</if>
            <if test="ownerCode != null">owner_code = #{ownerCode},</if>
            <if test="ownerName != null">owner_name = #{ownerName},</if>
            <if test="locationId != null">location_id = #{locationId},</if>
            <if test="locationCode != null">location_code = #{locationCode},</if>
            <if test="locationName != null">location_name = #{locationName},</if>
            <if test="areaId != null">area_id = #{areaId},</if>
            <if test="areaCode != null">area_code = #{areaCode},</if>
            <if test="areaName != null">area_name = #{areaName},</if>
            <if test="issueDate != null">issue_date = #{issueDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="wmsOrderNo != null">wms_order_no = #{wmsOrderNo},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where issue_id = #{issueId}
    </update>

    <delete id="deleteWmIssueHeaderByIssueId" parameterType="Long">
        delete from wm_issue_header where issue_id = #{issueId}
    </delete>

    <delete id="deleteWmIssueHeaderByIssueIds" parameterType="String">
        delete from wm_issue_header where issue_id in
        <foreach item="issueId" collection="array" open="(" separator="," close=")">
            #{issueId}
        </foreach>
    </delete>
</mapper>