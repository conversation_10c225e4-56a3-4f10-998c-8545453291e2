<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.wm.mapper.WmOutsourceIssueMapper">
    
    <resultMap type="WmOutsourceIssue" id="WmOutsourceIssueResult">
        <result property="issueId"    column="issue_id"    />
        <result property="issueCode"    column="issue_code"    />
        <result property="issueName"    column="issue_name"    />
        <result property="workorderId"    column="workorder_id"    />
        <result property="workorderCode"    column="workorder_code"    />
        <result property="vendorId"    column="vendor_id"    />
        <result property="vendorCode"    column="vendor_code"    />
        <result property="vendorName"    column="vendor_name"    />
        <result property="vendorNick"    column="vendor_nick"    />
        <result property="warehouseId"    column="warehouse_id"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="locationId"    column="location_id"    />
        <result property="locationCode"    column="location_code"    />
        <result property="locationName"    column="location_name"    />
        <result property="areaId"    column="area_id"    />
        <result property="areaCode"    column="area_code"    />
        <result property="areaName"    column="area_name"    />
        <result property="issueDate"    column="issue_date"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap id="OutsourceIssueTxBeanResult" type="OutsourceIssueTxBean">
        <result property="materialStockId" column="material_stock_id"></result>
        <result property="itemId" column="item_id"></result>
        <result property="itemCode" column="item_code"></result>
        <result property="itemName" column="item_name"></result>
        <result property="specification" column="specification"></result>
        <result property="unitOfMeasure" column="unit_of_measure"></result>
        <result property="batchCode" column="batch_code"></result>
        <result property="warehouseId" column="warehouse_id"></result>
        <result property="warehouseCode" column="warehouse_code"></result>
        <result property="warehouseName" column="warehouse_name"></result>
        <result property="locationId" column="location_id"></result>
        <result property="locationCode" column="location_code"></result>
        <result property="locationName" column="location_name"></result>
        <result property="areaId" column="area_id"></result>
        <result property="areaCode" column="area_code"></result>
        <result property="areaName" column="area_name"></result>
        <result property="vendorId" column="vendor_id"></result>
        <result property="vendorCode" column="vendor_code"></result>
        <result property="vendorName" column="vendor_name"></result>
        <result property="vendorNick" column="vendor_nick"></result>
        <result property="sourceDocType" column="source_doc_type"></result>
        <result property="sourceDocId" column="source_doc_id"></result>
        <result property="sourceDocCode" column="source_doc_code"></result>
        <result property="sourceDocLineId" column="source_doc_line_id"></result>
        <result property="transactionQuantity" column="transaction_quantity"></result>
        <result property="workorderId" column="workorder_id"></result>
        <result property="workorderCode" column="workorder_code"></result>
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWmOutsourceIssueVo">
        select issue_id, issue_code, issue_name, workorder_id, workorder_code, vendor_id, vendor_code, vendor_name, vendor_nick, warehouse_id, warehouse_code, warehouse_name, location_id, location_code, location_name, area_id, area_code, area_name, issue_date, status, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from wm_outsource_issue
    </sql>

    <select id="selectWmOutsourceIssueList" parameterType="WmOutsourceIssue" resultMap="WmOutsourceIssueResult">
        <include refid="selectWmOutsourceIssueVo"/>
        <where>  
            <if test="issueCode != null  and issueCode != ''"> and issue_code = #{issueCode}</if>
            <if test="issueName != null  and issueName != ''"> and issue_name like concat('%', #{issueName}, '%')</if>
            <if test="workorderId != null "> and workorder_id = #{workorderId}</if>
            <if test="workorderCode != null  and workorderCode != ''"> and workorder_code = #{workorderCode}</if>
            <if test="vendorId != null "> and vendor_id = #{vendorId}</if>
            <if test="vendorCode != null  and vendorCode != ''"> and vendor_code = #{vendorCode}</if>
            <if test="vendorName != null  and vendorName != ''"> and vendor_name like concat('%', #{vendorName}, '%')</if>
            <if test="vendorNick != null  and vendorNick != ''"> and vendor_nick = #{vendorNick}</if>
            <if test="warehouseId != null "> and warehouse_id = #{warehouseId}</if>
            <if test="warehouseCode != null  and warehouseCode != ''"> and warehouse_code = #{warehouseCode}</if>
            <if test="warehouseName != null  and warehouseName != ''"> and warehouse_name like concat('%', #{warehouseName}, '%')</if>
            <if test="locationId != null "> and location_id = #{locationId}</if>
            <if test="locationCode != null  and locationCode != ''"> and location_code = #{locationCode}</if>
            <if test="locationName != null  and locationName != ''"> and location_name like concat('%', #{locationName}, '%')</if>
            <if test="areaId != null "> and area_id = #{areaId}</if>
            <if test="areaCode != null  and areaCode != ''"> and area_code = #{areaCode}</if>
            <if test="areaName != null  and areaName != ''"> and area_name like concat('%', #{areaName}, '%')</if>
            <if test="issueDate != null "> and issue_date = #{issueDate}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectWmOutsourceIssueByIssueId" parameterType="Long" resultMap="WmOutsourceIssueResult">
        <include refid="selectWmOutsourceIssueVo"/>
        where issue_id = #{issueId}
    </select>

    <select id="getTxBeans" parameterType="Long" resultMap="OutsourceIssueTxBeanResult">
        SELECT irl.material_stock_id, irl.`item_id`,irl.`item_code`,irl.`item_name`,irl.`specification`,irl.`unit_of_measure`,irl.`batch_code`,
               irl.`warehouse_id`,irl.`warehouse_code`,irl.`warehouse_name`,irl.`location_id`,irl.`location_code`,irl.`location_name`,irl.`area_id`,irl.`area_code`,irl.`area_name`,
               'ISSUE' AS source_doc_type,ir.`issue_id` AS source_doc_id,ir.`issue_code` AS source_doc_code,irl.`line_id` AS source_doc_line_id,
               irl.`quantity_issued` AS transaction_quantity,ir.workorder_id,ir.workorder_code,
               ir.`create_by`,ir.`create_time`,ir.`update_by`,ir.`update_time`
        FROM wm_outsource_issue ir
                 LEFT JOIN wm_outsource_issue_line irl
                           ON ir.issue_id = irl.`issue_id`
        WHERE ir.`issue_id` = #{issueId}
    </select>
    <select id="checkIssueCodeUnique" resultType="com.ktg.mes.wm.domain.WmOutsourceIssue" resultMap="WmOutsourceIssueResult">
        <include refid="selectWmOutsourceIssueVo"/>
        where issue_code = #{issueCode}
        limit 1
    </select>

    <insert id="insertWmOutsourceIssue" parameterType="WmOutsourceIssue" useGeneratedKeys="true" keyProperty="issueId">
        insert into wm_outsource_issue
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="issueCode != null and issueCode != ''">issue_code,</if>
            <if test="issueName != null and issueName != ''">issue_name,</if>
            <if test="workorderId != null">workorder_id,</if>
            <if test="workorderCode != null">workorder_code,</if>
            <if test="vendorId != null">vendor_id,</if>
            <if test="vendorCode != null">vendor_code,</if>
            <if test="vendorName != null">vendor_name,</if>
            <if test="vendorNick != null">vendor_nick,</if>
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="warehouseCode != null">warehouse_code,</if>
            <if test="warehouseName != null">warehouse_name,</if>
            <if test="locationId != null">location_id,</if>
            <if test="locationCode != null">location_code,</if>
            <if test="locationName != null">location_name,</if>
            <if test="areaId != null">area_id,</if>
            <if test="areaCode != null">area_code,</if>
            <if test="areaName != null">area_name,</if>
            <if test="issueDate != null">issue_date,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="issueCode != null and issueCode != ''">#{issueCode},</if>
            <if test="issueName != null and issueName != ''">#{issueName},</if>
            <if test="workorderId != null">#{workorderId},</if>
            <if test="workorderCode != null">#{workorderCode},</if>
            <if test="vendorId != null">#{vendorId},</if>
            <if test="vendorCode != null">#{vendorCode},</if>
            <if test="vendorName != null">#{vendorName},</if>
            <if test="vendorNick != null">#{vendorNick},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="warehouseCode != null">#{warehouseCode},</if>
            <if test="warehouseName != null">#{warehouseName},</if>
            <if test="locationId != null">#{locationId},</if>
            <if test="locationCode != null">#{locationCode},</if>
            <if test="locationName != null">#{locationName},</if>
            <if test="areaId != null">#{areaId},</if>
            <if test="areaCode != null">#{areaCode},</if>
            <if test="areaName != null">#{areaName},</if>
            <if test="issueDate != null">#{issueDate},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWmOutsourceIssue" parameterType="WmOutsourceIssue">
        update wm_outsource_issue
        <trim prefix="SET" suffixOverrides=",">
            <if test="issueCode != null and issueCode != ''">issue_code = #{issueCode},</if>
            <if test="issueName != null and issueName != ''">issue_name = #{issueName},</if>
            <if test="workorderId != null">workorder_id = #{workorderId},</if>
            <if test="workorderCode != null">workorder_code = #{workorderCode},</if>
            <if test="vendorId != null">vendor_id = #{vendorId},</if>
            <if test="vendorCode != null">vendor_code = #{vendorCode},</if>
            <if test="vendorName != null">vendor_name = #{vendorName},</if>
            <if test="vendorNick != null">vendor_nick = #{vendorNick},</if>
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="warehouseCode != null">warehouse_code = #{warehouseCode},</if>
            <if test="warehouseName != null">warehouse_name = #{warehouseName},</if>
            <if test="locationId != null">location_id = #{locationId},</if>
            <if test="locationCode != null">location_code = #{locationCode},</if>
            <if test="locationName != null">location_name = #{locationName},</if>
            <if test="areaId != null">area_id = #{areaId},</if>
            <if test="areaCode != null">area_code = #{areaCode},</if>
            <if test="areaName != null">area_name = #{areaName},</if>
            <if test="issueDate != null">issue_date = #{issueDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where issue_id = #{issueId}
    </update>

    <delete id="deleteWmOutsourceIssueByIssueId" parameterType="Long">
        delete from wm_outsource_issue where issue_id = #{issueId}
    </delete>

    <delete id="deleteWmOutsourceIssueByIssueIds" parameterType="String">
        delete from wm_outsource_issue where issue_id in 
        <foreach item="issueId" collection="array" open="(" separator="," close=")">
            #{issueId}
        </foreach>
    </delete>
</mapper>