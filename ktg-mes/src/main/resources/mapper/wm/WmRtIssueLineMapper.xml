<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.wm.mapper.WmRtIssueLineMapper">

    <resultMap type="WmRtIssueLine" id="WmRtIssueLineResult">
        <result property="lineId"    column="line_id"    />
        <result property="rtId"    column="rt_id"    />
        <result property="materialStockId"    column="material_stock_id"    />
        <result property="itemId"    column="item_id"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemName"    column="item_name"    />
        <result property="specification"    column="specification"    />
        <result property="unitOfMeasure"    column="unit_of_measure"    />
        <result property="quantityRt"    column="quantity_rt"    />
        <result property="batchCode"    column="batch_code"    />
        <result property="warehouseId"    column="warehouse_id"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="locationId"    column="location_id"    />
        <result property="zoneCode"    column="zone_code"    />
        <result property="zoneName"    column="zone_name"    />
        <result property="locationCode"    column="location_code"    />
        <result property="locationName"    column="location_name"    />
        <result property="areaId"    column="area_id"    />
        <result property="areaCode"    column="area_code"    />
        <result property="areaName"    column="area_name"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="skuQuality"    column="sku_quality"    />
        <result property="inventoryType"    column="inventory_type"    />
        <result property="skuLotNo"    column="sku_lot_no"    />
        <result property="produceBatchCode"    column="produce_batch_code"    />
        <result property="fromLocationCode"    column="from_location_code"    />
        <result property="physicalQty"    column="physical_qty"    />
        <result property="availableQty"    column="available_qty"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWmRtIssueLineVo">
        select line_id, rt_id, material_stock_id, item_id, item_code, item_name, specification, unit_of_measure, quantity_rt, batch_code, warehouse_id, warehouse_code, warehouse_name, location_id, zone_code, zone_name, location_code, location_name, area_id, area_code, area_name, remark, attr1, attr2, attr3, attr4, sku_quality, inventory_type, sku_lot_no, produce_batch_code, from_location_code, physical_qty, available_qty, create_by, create_time, update_by, update_time from wm_rt_issue_line
    </sql>

    <select id="selectWmRtIssueLineList" parameterType="WmRtIssueLine" resultMap="WmRtIssueLineResult">
        <include refid="selectWmRtIssueLineVo"/>
        <where>
            <if test="rtId != null "> and rt_id = #{rtId}</if>
            <if test="materialStockId != null "> and material_stock_id = #{materialStockId}</if>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code = #{itemCode}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="specification != null  and specification != ''"> and specification = #{specification}</if>
            <if test="unitOfMeasure != null  and unitOfMeasure != ''"> and unit_of_measure = #{unitOfMeasure}</if>
            <if test="quantityRt != null "> and quantity_rt = #{quantityRt}</if>
            <if test="batchCode != null  and batchCode != ''"> and batch_code = #{batchCode}</if>
            <if test="warehouseId != null "> and warehouse_id = #{warehouseId}</if>
            <if test="warehouseCode != null  and warehouseCode != ''"> and warehouse_code = #{warehouseCode}</if>
            <if test="warehouseName != null  and warehouseName != ''"> and warehouse_name like concat('%', #{warehouseName}, '%')</if>
            <if test="locationId != null "> and location_id = #{locationId}</if>
            <if test="zoneCode != null  and zoneCode != ''"> and zone_code = #{zoneCode}</if>
            <if test="zoneName != null  and zoneName != ''"> and zone_name like concat('%', #{zoneName}, '%')</if>
            <if test="locationCode != null  and locationCode != ''"> and location_code = #{locationCode}</if>
            <if test="locationName != null  and locationName != ''"> and location_name like concat('%', #{locationName}, '%')</if>
            <if test="areaId != null "> and area_id = #{areaId}</if>
            <if test="areaCode != null  and areaCode != ''"> and area_code = #{areaCode}</if>
            <if test="areaName != null  and areaName != ''"> and area_name like concat('%', #{areaName}, '%')</if>
            <if test="attr1 != null  and attr1 != ''"> and attr1 = #{attr1}</if>
            <if test="attr2 != null  and attr2 != ''"> and attr2 = #{attr2}</if>
            <if test="attr3 != null "> and attr3 = #{attr3}</if>
            <if test="attr4 != null "> and attr4 = #{attr4}</if>
            <if test="skuQuality != null  and skuQuality != ''"> and sku_quality = #{skuQuality}</if>
            <if test="inventoryType != null  and inventoryType != ''"> and inventory_type = #{inventoryType}</if>
            <if test="skuLotNo != null  and skuLotNo != ''"> and sku_lot_no = #{skuLotNo}</if>
            <if test="produceBatchCode != null  and produceBatchCode != ''"> and produce_batch_code = #{produceBatchCode}</if>
            <if test="fromLocationCode != null  and fromLocationCode != ''"> and from_location_code = #{fromLocationCode}</if>
            <if test="physicalQty != null "> and physical_qty = #{physicalQty}</if>
            <if test="availableQty != null "> and available_qty = #{availableQty}</if>
        </where>
    </select>

    <select id="selectWmRtIssueLineByLineId" parameterType="Long" resultMap="WmRtIssueLineResult">
        <include refid="selectWmRtIssueLineVo"/>
        where line_id = #{lineId}
    </select>

    <insert id="insertWmRtIssueLine" parameterType="WmRtIssueLine" useGeneratedKeys="true" keyProperty="lineId">
        insert into wm_rt_issue_line
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rtId != null">rt_id,</if>
            <if test="materialStockId != null">material_stock_id,</if>
            <if test="itemId != null">item_id,</if>
            <if test="itemCode != null">item_code,</if>
            <if test="itemName != null">item_name,</if>
            <if test="specification != null">specification,</if>
            <if test="unitOfMeasure != null">unit_of_measure,</if>
            <if test="quantityRt != null">quantity_rt,</if>
            <if test="batchCode != null">batch_code,</if>
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="warehouseCode != null">warehouse_code,</if>
            <if test="warehouseName != null">warehouse_name,</if>
            <if test="locationId != null">location_id,</if>
            <if test="zoneCode != null">zone_code,</if>
            <if test="zoneName != null">zone_name,</if>
            <if test="locationCode != null">location_code,</if>
            <if test="locationName != null">location_name,</if>
            <if test="areaId != null">area_id,</if>
            <if test="areaCode != null">area_code,</if>
            <if test="areaName != null">area_name,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="skuQuality != null">sku_quality,</if>
            <if test="inventoryType != null">inventory_type,</if>
            <if test="skuLotNo != null">sku_lot_no,</if>
            <if test="produceBatchCode != null">produce_batch_code,</if>
            <if test="fromLocationCode != null">from_location_code,</if>
            <if test="physicalQty != null">physical_qty,</if>
            <if test="availableQty != null">available_qty,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rtId != null">#{rtId},</if>
            <if test="materialStockId != null">#{materialStockId},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="itemCode != null">#{itemCode},</if>
            <if test="itemName != null">#{itemName},</if>
            <if test="specification != null">#{specification},</if>
            <if test="unitOfMeasure != null">#{unitOfMeasure},</if>
            <if test="quantityRt != null">#{quantityRt},</if>
            <if test="batchCode != null">#{batchCode},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="warehouseCode != null">#{warehouseCode},</if>
            <if test="warehouseName != null">#{warehouseName},</if>
            <if test="locationId != null">#{locationId},</if>
            <if test="zoneCode != null">#{zoneCode},</if>
            <if test="zoneName != null">#{zoneName},</if>
            <if test="locationCode != null">#{locationCode},</if>
            <if test="locationName != null">#{locationName},</if>
            <if test="areaId != null">#{areaId},</if>
            <if test="areaCode != null">#{areaCode},</if>
            <if test="areaName != null">#{areaName},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="skuQuality != null">#{skuQuality},</if>
            <if test="inventoryType != null">#{inventoryType},</if>
            <if test="skuLotNo != null">#{skuLotNo},</if>
            <if test="produceBatchCode != null">#{produceBatchCode},</if>
            <if test="fromLocationCode != null">#{fromLocationCode},</if>
            <if test="physicalQty != null">#{physicalQty},</if>
            <if test="availableQty != null">#{availableQty},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateWmRtIssueLine" parameterType="WmRtIssueLine">
        update wm_rt_issue_line
        <trim prefix="SET" suffixOverrides=",">
            <if test="rtId != null">rt_id = #{rtId},</if>
            <if test="materialStockId != null">material_stock_id = #{materialStockId},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="itemCode != null">item_code = #{itemCode},</if>
            <if test="itemName != null">item_name = #{itemName},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="unitOfMeasure != null">unit_of_measure = #{unitOfMeasure},</if>
            <if test="quantityRt != null">quantity_rt = #{quantityRt},</if>
            <if test="batchCode != null">batch_code = #{batchCode},</if>
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="warehouseCode != null">warehouse_code = #{warehouseCode},</if>
            <if test="warehouseName != null">warehouse_name = #{warehouseName},</if>
            <if test="locationId != null">location_id = #{locationId},</if>
            <if test="zoneCode != null">zone_code = #{zoneCode},</if>
            <if test="zoneName != null">zone_name = #{zoneName},</if>
            <if test="locationCode != null">location_code = #{locationCode},</if>
            <if test="locationName != null">location_name = #{locationName},</if>
            <if test="areaId != null">area_id = #{areaId},</if>
            <if test="areaCode != null">area_code = #{areaCode},</if>
            <if test="areaName != null">area_name = #{areaName},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="skuQuality != null">sku_quality = #{skuQuality},</if>
            <if test="inventoryType != null">inventory_type = #{inventoryType},</if>
            <if test="skuLotNo != null">sku_lot_no = #{skuLotNo},</if>
            <if test="produceBatchCode != null">produce_batch_code = #{produceBatchCode},</if>
            <if test="fromLocationCode != null">from_location_code = #{fromLocationCode},</if>
            <if test="physicalQty != null">physical_qty = #{physicalQty},</if>
            <if test="availableQty != null">available_qty = #{availableQty},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where line_id = #{lineId}
    </update>

    <delete id="deleteWmRtIssueLineByLineId" parameterType="Long">
        delete from wm_rt_issue_line where line_id = #{lineId}
    </delete>

    <delete id="deleteWmRtIssueLineByLineIds" parameterType="String">
        delete from wm_rt_issue_line where line_id in
        <foreach item="lineId" collection="array" open="(" separator="," close=")">
            #{lineId}
        </foreach>
    </delete>

    <delete id="deleteByRtId" parameterType="Long">
        delete from wm_rt_issue_line where rt_id = #{rtId}
    </delete>

</mapper>