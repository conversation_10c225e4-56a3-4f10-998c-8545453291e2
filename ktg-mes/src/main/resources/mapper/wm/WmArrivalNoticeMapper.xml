<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.wm.mapper.WmArrivalNoticeMapper">

    <resultMap type="WmArrivalNotice" id="WmArrivalNoticeResult">
        <result property="noticeId"    column="notice_id"    />
        <result property="noticeCode"    column="notice_code"    />
        <result property="noticeName"    column="notice_name"    />
        <result property="erpOrderNo"    column="erp_order_no"    />
        <result property="poCode"    column="po_code"    />
        <result property="asnId"    column="asn_id"    />
        <result property="vendorId"    column="vendor_id"    />
        <result property="vendorCode"    column="vendor_code"    />
        <result property="vendorName"    column="vendor_name"    />
        <result property="vendorNick"    column="vendor_nick"    />
        <result property="arrivalDate"    column="arrival_date"    />
        <result property="contact"    column="contact"    />
        <result property="tel"    column="tel"    />
        <result property="status"    column="status"    />
        <result property="takeStockStatus"    column="take_stock_status"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="wmsNoticeCode"    column="wms_notice_code"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="ownerCode"    column="owner_code"    />
        <result property="ownerName"    column="owner_name"    />
        <result property="planQuantity"    column="plan_quantity"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWmArrivalNoticeVo">
        select notice_id, notice_code, notice_name, erp_order_no, po_code, asn_id, vendor_id, vendor_code, vendor_name, vendor_nick, arrival_date, contact, tel, status, take_stock_status, remark, attr1, attr2, attr3, attr4, wms_notice_code, warehouse_code, warehouse_name, owner_code, owner_name, plan_quantity, create_by, create_time, update_by, update_time from wm_arrival_notice
    </sql>

    <select id="selectWmArrivalNoticeList" parameterType="WmArrivalNotice" resultMap="WmArrivalNoticeResult">
        <include refid="selectWmArrivalNoticeVo"/>
        <where>
            <if test="noticeCode != null  and noticeCode != ''"> and notice_code = #{noticeCode}</if>
            <if test="noticeName != null  and noticeName != ''"> and notice_name like concat('%', #{noticeName}, '%')</if>
            <if test="erpOrderNo != null  and erpOrderNo != ''"> and erp_order_no = #{erpOrderNo}</if>
            <if test="poCode != null  and poCode != ''"> and po_code = #{poCode}</if>
            <if test="asnId != null  and asnId != ''"> and asn_id = #{asnId}</if>
            <if test="vendorId != null "> and vendor_id = #{vendorId}</if>
            <if test="vendorCode != null  and vendorCode != ''"> and vendor_code = #{vendorCode}</if>
            <if test="vendorName != null  and vendorName != ''"> and vendor_name like concat('%', #{vendorName}, '%')</if>
            <if test="vendorNick != null  and vendorNick != ''"> and vendor_nick = #{vendorNick}</if>
            <if test="arrivalDate != null "> and arrival_date = #{arrivalDate}</if>
            <if test="contact != null  and contact != ''"> and contact = #{contact}</if>
            <if test="tel != null  and tel != ''"> and tel = #{tel}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="takeStockStatus != null  and takeStockStatus != ''"> and take_stock_status = #{takeStockStatus}</if>
            <if test="attr1 != null  and attr1 != ''"> and attr1 = #{attr1}</if>
            <if test="attr2 != null  and attr2 != ''"> and attr2 = #{attr2}</if>
            <if test="attr3 != null "> and attr3 = #{attr3}</if>
            <if test="attr4 != null "> and attr4 = #{attr4}</if>
            <if test="wmsNoticeCode != null  and wmsNoticeCode != ''"> and wms_notice_code = #{wmsNoticeCode}</if>
            <if test="warehouseCode != null  and warehouseCode != ''"> and warehouse_code = #{warehouseCode}</if>
            <if test="warehouseName != null  and warehouseName != ''"> and warehouse_name like concat('%', #{warehouseName}, '%')</if>
            <if test="ownerCode != null  and ownerCode != ''"> and owner_code = #{ownerCode}</if>
            <if test="ownerName != null  and ownerName != ''"> and owner_name like concat('%', #{ownerName}, '%')</if>
            <if test="planQuantity != null "> and plan_quantity = #{planQuantity}</if>
        </where>
    </select>

    <select id="selectWmArrivalNoticeByNoticeId" parameterType="Long" resultMap="WmArrivalNoticeResult">
        <include refid="selectWmArrivalNoticeVo"/>
        where notice_id = #{noticeId}
    </select>

    <insert id="insertWmArrivalNotice" parameterType="WmArrivalNotice" useGeneratedKeys="true" keyProperty="noticeId">
        insert into wm_arrival_notice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="noticeCode != null and noticeCode != ''">notice_code,</if>
            <if test="noticeName != null and noticeName != ''">notice_name,</if>
            <if test="erpOrderNo != null">erp_order_no,</if>
            <if test="poCode != null">po_code,</if>
            <if test="asnId != null">asn_id,</if>
            <if test="vendorId != null">vendor_id,</if>
            <if test="vendorCode != null">vendor_code,</if>
            <if test="vendorName != null">vendor_name,</if>
            <if test="vendorNick != null">vendor_nick,</if>
            <if test="arrivalDate != null">arrival_date,</if>
            <if test="contact != null">contact,</if>
            <if test="tel != null">tel,</if>
            <if test="status != null">status,</if>
            <if test="takeStockStatus != null">take_stock_status,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="wmsNoticeCode != null">wms_notice_code,</if>
            <if test="warehouseCode != null">warehouse_code,</if>
            <if test="warehouseName != null">warehouse_name,</if>
            <if test="ownerCode != null">owner_code,</if>
            <if test="ownerName != null">owner_name,</if>
            <if test="planQuantity != null">plan_quantity,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="noticeCode != null and noticeCode != ''">#{noticeCode},</if>
            <if test="noticeName != null and noticeName != ''">#{noticeName},</if>
            <if test="erpOrderNo != null">#{erpOrderNo},</if>
            <if test="poCode != null">#{poCode},</if>
            <if test="asnId != null">#{asnId},</if>
            <if test="vendorId != null">#{vendorId},</if>
            <if test="vendorCode != null">#{vendorCode},</if>
            <if test="vendorName != null">#{vendorName},</if>
            <if test="vendorNick != null">#{vendorNick},</if>
            <if test="arrivalDate != null">#{arrivalDate},</if>
            <if test="contact != null">#{contact},</if>
            <if test="tel != null">#{tel},</if>
            <if test="status != null">#{status},</if>
            <if test="takeStockStatus != null">#{takeStockStatus},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="wmsNoticeCode != null">#{wmsNoticeCode},</if>
            <if test="warehouseCode != null">#{warehouseCode},</if>
            <if test="warehouseName != null">#{warehouseName},</if>
            <if test="ownerCode != null">#{ownerCode},</if>
            <if test="ownerName != null">#{ownerName},</if>
            <if test="planQuantity != null">#{planQuantity},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateWmArrivalNotice" parameterType="WmArrivalNotice">
        update wm_arrival_notice
        <trim prefix="SET" suffixOverrides=",">
            <if test="noticeCode != null and noticeCode != ''">notice_code = #{noticeCode},</if>
            <if test="noticeName != null and noticeName != ''">notice_name = #{noticeName},</if>
            <if test="erpOrderNo != null">erp_order_no = #{erpOrderNo},</if>
            <if test="poCode != null">po_code = #{poCode},</if>
            <if test="asnId != null">asn_id = #{asnId},</if>
            <if test="vendorId != null">vendor_id = #{vendorId},</if>
            <if test="vendorCode != null">vendor_code = #{vendorCode},</if>
            <if test="vendorName != null">vendor_name = #{vendorName},</if>
            <if test="vendorNick != null">vendor_nick = #{vendorNick},</if>
            <if test="arrivalDate != null">arrival_date = #{arrivalDate},</if>
            <if test="contact != null">contact = #{contact},</if>
            <if test="tel != null">tel = #{tel},</if>
            <if test="status != null">status = #{status},</if>
            <if test="takeStockStatus != null">take_stock_status = #{takeStockStatus},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="wmsNoticeCode != null">wms_notice_code = #{wmsNoticeCode},</if>
            <if test="warehouseCode != null">warehouse_code = #{warehouseCode},</if>
            <if test="warehouseName != null">warehouse_name = #{warehouseName},</if>
            <if test="ownerCode != null">owner_code = #{ownerCode},</if>
            <if test="ownerName != null">owner_name = #{ownerName},</if>
            <if test="planQuantity != null">plan_quantity = #{planQuantity},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where notice_id = #{noticeId}
    </update>

    <delete id="deleteWmArrivalNoticeByNoticeId" parameterType="Long">
        delete from wm_arrival_notice where notice_id = #{noticeId}
    </delete>

    <delete id="deleteWmArrivalNoticeByNoticeIds" parameterType="String">
        delete from wm_arrival_notice where notice_id in
        <foreach item="noticeId" collection="array" open="(" separator="," close=")">
            #{noticeId}
        </foreach>
    </delete>
</mapper>