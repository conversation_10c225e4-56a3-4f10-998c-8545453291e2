<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.qc.mapper.QcPendingInspectMapper">

    <select id="selectQcPendingListNew" parameterType="QcPendingInspect" resultMap="QcPendingInspectResult">
        SELECT *
        FROM
            (
                SELECT
                    pf.feedback_code AS source_doc_code,
                    'PQC' AS qc_type,
                    pf.item_code,
                    pf.item_name,
                    pf.quantity_uncheck,
                    pf.workstation_name AS address,
                    pf.feedback_time AS record_time
                FROM
                pro_feedback pf
                WHERE
                pf.quantity_uncheck > 0
                UNION ALL
                SELECT
                    qi.iqc_code as sourceDocCode,
                    'IQC' as qcType,
                    qi.item_code as itemCode,
                    qi.item_name as itemName,
                    qi.quantity_recived as quantityUncheck,
                    NULL as address,
                    qi.create_time AS record_time
                FROM
                    qc_iqc qi
                WHERE
                    qi.status != 'CONFIRMED'
                UNION ALL
                SELECT
                    qo.oqc_code as sourceDocCode,
                    'OQC' as qcType,
                    qo.item_code as itemCode,
                    qo.item_name as itemName,
                    qo.quantity_receive as quantityUncheck,
                    qo.work_shop_name as address,
                    qo.create_time AS record_time
                FROM
                    qc_oqc qo
                WHERE
                     qo.status != 'CONFIRMED'
        ) t where 1=1
            <if test="qcType != null  and qcType != ''"> and t.qc_type = #{qcType}</if>
            <if test="sourceDocCode != null  and sourceDocCode != ''"> and t.source_doc_code like concat('%', #{sourceDocCode} , '%')</if>
            <if test="itemName != null  and itemName != ''"> and t.item_name like concat('%', #{itemName} , '%')</if>
        ORDER BY record_time DESC
    </select>

</mapper>