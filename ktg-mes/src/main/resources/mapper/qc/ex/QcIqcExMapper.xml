<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.qc.mapper.QcIqcMapper">

    <select id="selectQcIqcByIqcIdList" resultMap="QcIqcResult">
        <include refid="selectQcIqcVo"/>
        where iqc_id in
        <foreach item="iqcId" collection="iqcIdList" open="(" separator="," close=")">
            #{iqcId}
        </foreach>
    </select>


    <select id="checkIqcCodeUnique" parameterType="QcIqc" resultMap="QcIqcResult">
        <include refid="selectQcIqcVo"/>
        where iqc_code = #{iqcCode} limit 1
    </select>

    <select id="countArrivalNoticeLineUnique" resultType="java.lang.Integer">
        select count(*) from qc_iqc
          where source_doc_id = #{arrivalNoticeId}
            and source_doc_type = 'WM_ARRIVAL_NOTICE_CODE'
            and item_code = #{itemCode}
            and produce_batch_code = #{produceBatchCode}
    </select>

    <update id="updateCrMajMinQuaAndRate" parameterType="Long">
        UPDATE qc_iqc a
            INNER JOIN (
            SELECT SUM(CASE WHEN defect_level = 'CR' THEN defect_quantity ELSE 0 END ) AS cr_quantity,
            SUM(CASE WHEN defect_level = 'MAJ' THEN defect_quantity ELSE 0 END) AS maj_quantity,
            SUM(CASE WHEN defect_level = 'MIN' THEN defect_quantity ELSE 0 END) AS min_quantity,
            qid.`qc_id` as 'iqc_id'
            FROM qc_defect_record qid
            WHERE qid.qc_id = #{iqcId}
            and qc_type = 'IQC'
            GROUP BY qid.qc_id
            ) b
        ON a.`iqc_id` = b.iqc_id
            SET a.cr_quantity=b.cr_quantity,a.maj_quantity=b.maj_quantity,a.min_quantity=b.min_quantity,
                a.`cr_rate`= ROUND(b.cr_quantity/a.`quantity_check`*100,2),
                a.`maj_rate`= ROUND(b.maj_quantity/a.`quantity_check`*100,2),
                a.`min_rate`= ROUND(b.min_quantity/a.`quantity_check`*100,2)
        WHERE a.iqc_id = #{iqcId}
    </update>

    <select id="qcIqcPage" parameterType="QcIqcListReqVo" resultMap="QcIqcResult">
        <include refid="selectQcIqcVo"/>
        <where>
            <if test="iqcCode != null  and iqcCode != ''"> and iqc_code like concat('%', #{iqcCode}, '%')</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code like concat('%', #{itemCode}, '%')</if>
            <if test="produceBatchCode != null  and produceBatchCode != ''"> and produce_batch_code like concat('%', #{produceBatchCode}, '%')</if>
            <if test="checkResult != null  and checkResult != ''"> and check_result = #{checkResult}</if>
            <if test="reciveDateBegin != null and reciveDateEnd != null"> and recive_date between #{reciveDateBegin} and #{reciveDateEnd}</if>
            <if test="inspectDateBegin != null and inspectDateEnd != null "> and inspect_date between #{inspectDateBegin} and #{inspectDateEnd}</if>
            <if test="ownerCode != null  and ownerCode != ''"> and owner_code = #{ownerCode}</if>
            <if test="arrivalNoticeCode != null  and arrivalNoticeCode != ''"> and source_doc_code = #{arrivalNoticeCode} and source_doc_type = 'WM_ARRIVAL_NOTICE_CODE'</if>
        </where>
        order by iqc_id desc
    </select>

</mapper>