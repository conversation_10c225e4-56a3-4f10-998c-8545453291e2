<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.qc.mapper.QcChangeMapper">

    <select id="qcChangePage" parameterType="QcChangeListReqVo" resultMap="QcChangeResult">
        <include refid="selectQcChangeVo"/>
        <where>
            <if test="changeCode != null  and changeCode != ''"> and change_code like concat('%', #{changeCode}, '%')</if>
            <if test="sourceDocCode != null  and sourceDocCode != ''"> and source_doc_code like concat('%', #{sourceDocCode}, '%')</if>
            <if test="ownerCode != null  and ownerCode != ''"> and owner_code = #{ownerCode}</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code like concat('%', #{itemCode}, '%')</if>
            <if test="wmsOrderNo != null  and wmsOrderNo != ''"> and wms_order_no like concat('%', #{wmsOrderNo}, '%')</if>
            <if test="createTimeBegin != null  and createTimeEnd != null"> and create_time between #{createTimeBegin} and #{createTimeEnd}</if>
        </where>
        order by create_time desc
    </select>

</mapper>