<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.qc.mapper.QcOqcMapper">

    <select id="checkOqcCodeUnique" parameterType="QcOqc" resultMap="QcOqcResult">
        <include refid="selectQcOqcVo"/>
        where oqc_code = #{oqcCode}
    </select>


    <select id="qcOqcPage" parameterType="QcOqcListReqVo" resultMap="QcOqcResult">
        <include refid="selectQcOqcVo"/>
        <where>
            <if test="oqcCode != null  and oqcCode != ''"> and oqc_code like concat('%', #{oqcCode}, '%')</if>
            <if test="workshopId != null  and workshopId != ''"> and work_shop_id = #{workshopId}</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code like concat('%', #{itemCode}, '%')</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="workorderCode != null  and workorderCode != ''"> and workorder_code like concat('%', #{workorderCode}, '%')</if>
            <if test="taskCode != null  and taskCode != ''"> and task_code like concat('%', #{taskCode}, '%')</if>
            <if test="checkResult != null  and checkResult != ''"> and check_result = #{checkResult}</if>
            <if test="outDateStart != null and outDateEnd != null"> and out_date between #{outDateStart} and #{outDateEnd}</if>
            <if test="inspectDateStart != null and inspectDateEnd != null "> and inspect_date between #{inspectDateStart} and #{inspectDateEnd}</if>
            <if test="produceBatchCode != null  and produceBatchCode != ''"> and produce_batch_code like concat('%', #{produceBatchCode}, '%')</if>
        </where>
        order by create_time desc
    </select>

    <update id="updateCrMajMinQuaAndRate" parameterType="Long">
        UPDATE qc_oqc a
            INNER JOIN (
                SELECT SUM(CASE WHEN defect_level = 'CR' THEN defect_quantity ELSE 0 END ) AS cr_quantity,
                       SUM(CASE WHEN defect_level = 'MAJ' THEN defect_quantity ELSE 0 END) AS maj_quantity,
                       SUM(CASE WHEN defect_level = 'MIN' THEN defect_quantity ELSE 0 END) AS min_quantity,
                       qid.`qc_id` as 'oqc_id'
                FROM qc_defect_record qid
                WHERE qid.qc_id = #{oqcId}
                  and qc_type = 'OQC'
                GROUP BY qid.qc_id
            ) b
            ON a.`oqc_id` = b.oqc_id
        SET a.cr_quantity=b.cr_quantity,a.maj_quantity=b.maj_quantity,a.min_quantity=b.min_quantity,
            a.`cr_rate`= ROUND(b.cr_quantity/a.`quantity_check`*100,2),
            a.`maj_rate`= ROUND(b.maj_quantity/a.`quantity_check`*100,2),
            a.`min_rate`= ROUND(b.min_quantity/a.`quantity_check`*100,2)
        WHERE a.oqc_id = #{oqcId}
    </update>


    <select id="selectQcOqcListByTaskId" resultMap="QcOqcResult">
        <include refid="selectQcOqcVo"/>
        where task_id in
        <foreach item="item" collection="taskIdList" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

</mapper>