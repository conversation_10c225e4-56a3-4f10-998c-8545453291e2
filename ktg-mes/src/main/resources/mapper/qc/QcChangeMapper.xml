<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.qc.mapper.QcChangeMapper">
    <resultMap type="QcChange" id="QcChangeResult">
        <result property="changeId"    column="change_id"    />
        <result property="changeCode"    column="change_code"    />
        <result property="sourceDocId"    column="source_doc_id"    />
        <result property="sourceDocType"    column="source_doc_type"    />
        <result property="sourceDocCode"    column="source_doc_code"    />
        <result property="ownerCode"    column="owner_code"    />
        <result property="ownerName"    column="owner_name"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemName"    column="item_name"    />
        <result property="changeReason"    column="change_reason"    />
        <result property="checkQuantity"    column="check_quantity"    />
        <result property="totalQuantity"    column="total_quantity"    />
        <result property="status"    column="status"    />
        <result property="wmsOrderNo"    column="wms_order_no"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectQcChangeVo">
        select change_id, change_code, source_doc_id, source_doc_type, source_doc_code, owner_code, owner_name, warehouse_code, warehouse_name, item_code, item_name, change_reason, check_quantity, total_quantity, status, wms_order_no, create_by, create_time, update_by, update_time from qc_change
    </sql>

    <select id="selectQcChangeList" parameterType="QcChange" resultMap="QcChangeResult">
        <include refid="selectQcChangeVo"/>
        <where>
            <if test="changeCode != null  and changeCode != ''"> and change_code = #{changeCode}</if>
            <if test="sourceDocId != null "> and source_doc_id = #{sourceDocId}</if>
            <if test="sourceDocType != null  and sourceDocType != ''"> and source_doc_type = #{sourceDocType}</if>
            <if test="sourceDocCode != null  and sourceDocCode != ''"> and source_doc_code = #{sourceDocCode}</if>
            <if test="ownerCode != null  and ownerCode != ''"> and owner_code = #{ownerCode}</if>
            <if test="ownerName != null  and ownerName != ''"> and owner_name like concat('%', #{ownerName}, '%')</if>
            <if test="warehouseCode != null  and warehouseCode != ''"> and warehouse_code = #{warehouseCode}</if>
            <if test="warehouseName != null  and warehouseName != ''"> and warehouse_name like concat('%', #{warehouseName}, '%')</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code = #{itemCode}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="changeReason != null  and changeReason != ''"> and change_reason = #{changeReason}</if>
            <if test="checkQuantity != null "> and check_quantity = #{checkQuantity}</if>
            <if test="totalQuantity != null "> and total_quantity = #{totalQuantity}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="wmsOrderNo != null  and wmsOrderNo != ''"> and wms_order_no = #{wmsOrderNo}</if>
        </where>
    </select>

    <select id="selectQcChangeByChangeId" parameterType="Long" resultMap="QcChangeResult">
        <include refid="selectQcChangeVo"/>
        where change_id = #{changeId}
    </select>

    <insert id="insertQcChange" parameterType="QcChange" useGeneratedKeys="true" keyProperty="changeId">
        insert into qc_change
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="changeCode != null and changeCode != ''">change_code,</if>
            <if test="sourceDocId != null">source_doc_id,</if>
            <if test="sourceDocType != null">source_doc_type,</if>
            <if test="sourceDocCode != null">source_doc_code,</if>
            <if test="ownerCode != null and ownerCode != ''">owner_code,</if>
            <if test="ownerName != null and ownerName != ''">owner_name,</if>
            <if test="warehouseCode != null and warehouseCode != ''">warehouse_code,</if>
            <if test="warehouseName != null and warehouseName != ''">warehouse_name,</if>
            <if test="itemCode != null and itemCode != ''">item_code,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="changeReason != null">change_reason,</if>
            <if test="checkQuantity != null">check_quantity,</if>
            <if test="totalQuantity != null">total_quantity,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="wmsOrderNo != null">wms_order_no,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="changeCode != null and changeCode != ''">#{changeCode},</if>
            <if test="sourceDocId != null">#{sourceDocId},</if>
            <if test="sourceDocType != null">#{sourceDocType},</if>
            <if test="sourceDocCode != null">#{sourceDocCode},</if>
            <if test="ownerCode != null and ownerCode != ''">#{ownerCode},</if>
            <if test="ownerName != null and ownerName != ''">#{ownerName},</if>
            <if test="warehouseCode != null and warehouseCode != ''">#{warehouseCode},</if>
            <if test="warehouseName != null and warehouseName != ''">#{warehouseName},</if>
            <if test="itemCode != null and itemCode != ''">#{itemCode},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="changeReason != null">#{changeReason},</if>
            <if test="checkQuantity != null">#{checkQuantity},</if>
            <if test="totalQuantity != null">#{totalQuantity},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="wmsOrderNo != null">#{wmsOrderNo},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateQcChange" parameterType="QcChange">
        update qc_change
        <trim prefix="SET" suffixOverrides=",">
            <if test="changeCode != null and changeCode != ''">change_code = #{changeCode},</if>
            <if test="sourceDocId != null">source_doc_id = #{sourceDocId},</if>
            <if test="sourceDocType != null">source_doc_type = #{sourceDocType},</if>
            <if test="sourceDocCode != null">source_doc_code = #{sourceDocCode},</if>
            <if test="ownerCode != null and ownerCode != ''">owner_code = #{ownerCode},</if>
            <if test="ownerName != null and ownerName != ''">owner_name = #{ownerName},</if>
            <if test="warehouseCode != null and warehouseCode != ''">warehouse_code = #{warehouseCode},</if>
            <if test="warehouseName != null and warehouseName != ''">warehouse_name = #{warehouseName},</if>
            <if test="itemCode != null and itemCode != ''">item_code = #{itemCode},</if>
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="changeReason != null">change_reason = #{changeReason},</if>
            <if test="checkQuantity != null">check_quantity = #{checkQuantity},</if>
            <if test="totalQuantity != null">total_quantity = #{totalQuantity},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="wmsOrderNo != null">wms_order_no = #{wmsOrderNo},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where change_id = #{changeId}
    </update>

    <delete id="deleteQcChangeByChangeId" parameterType="Long">
        delete from qc_change where change_id = #{changeId}
    </delete>

    <delete id="deleteQcChangeByChangeIds" parameterType="String">
        delete from qc_change where change_id in
        <foreach item="changeId" collection="array" open="(" separator="," close=")">
            #{changeId}
        </foreach>
    </delete>
</mapper>