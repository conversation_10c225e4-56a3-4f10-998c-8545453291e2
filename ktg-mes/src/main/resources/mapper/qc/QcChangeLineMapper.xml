<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.qc.mapper.QcChangeLineMapper">

    <resultMap type="QcChangeLine" id="QcChangeLineResult">
        <result property="lineId"    column="line_id"    />
        <result property="changeId"    column="change_id"    />
        <result property="targetSkuQuality"    column="target_sku_quality"    />
        <result property="targetInventoryType"    column="target_inventory_type"    />
        <result property="targetSkuLotNo"    column="target_sku_lot_no"    />
        <result property="targetLocationCode"    column="target_location_code"    />
        <result property="targetLocationName"    column="target_location_name"    />
        <result property="quantity"    column="quantity"    />
        <result property="originSkuQuality"    column="origin_sku_quality"    />
        <result property="originInventoryType"    column="origin_inventory_type"    />
        <result property="originSkuLotNo"    column="origin_sku_lot_no"    />
        <result property="originProductionBatchNo"    column="origin_production_batch_no"    />
        <result property="originDocCode"    column="origin_doc_code"    />
        <result property="originLocationCode"    column="origin_location_code"    />
        <result property="externalSkuLotNo"    column="external_sku_lot_no"    />
        <result property="manufDate"    column="manuf_date"    />
        <result property="expireDate"    column="expire_date"    />
        <result property="receiveDate"    column="receive_date"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectQcChangeLineVo">
        select line_id, change_id, target_sku_quality, target_inventory_type, target_sku_lot_no, target_location_code, target_location_name, quantity, origin_sku_quality, origin_inventory_type, origin_sku_lot_no, origin_production_batch_no, origin_doc_code, origin_location_code, external_sku_lot_no, manuf_date, expire_date, receive_date, create_by, create_time, update_by, update_time from qc_change_line
    </sql>

    <select id="selectQcChangeLineList" parameterType="QcChangeLine" resultMap="QcChangeLineResult">
        <include refid="selectQcChangeLineVo"/>
        <where>
            <if test="changeId != null "> and change_id = #{changeId}</if>
            <if test="targetSkuQuality != null  and targetSkuQuality != ''"> and target_sku_quality = #{targetSkuQuality}</if>
            <if test="targetInventoryType != null  and targetInventoryType != ''"> and target_inventory_type = #{targetInventoryType}</if>
            <if test="targetSkuLotNo != null  and targetSkuLotNo != ''"> and target_sku_lot_no = #{targetSkuLotNo}</if>
            <if test="targetLocationCode != null  and targetLocationCode != ''"> and target_location_code = #{targetLocationCode}</if>
            <if test="targetLocationName != null  and targetLocationName != ''"> and target_location_name like concat('%', #{targetLocationName}, '%')</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
            <if test="originSkuQuality != null  and originSkuQuality != ''"> and origin_sku_quality = #{originSkuQuality}</if>
            <if test="originInventoryType != null  and originInventoryType != ''"> and origin_inventory_type = #{originInventoryType}</if>
            <if test="originSkuLotNo != null  and originSkuLotNo != ''"> and origin_sku_lot_no = #{originSkuLotNo}</if>
            <if test="originProductionBatchNo != null  and originProductionBatchNo != ''"> and origin_production_batch_no = #{originProductionBatchNo}</if>
            <if test="originDocCode != null  and originDocCode != ''"> and origin_doc_code = #{originDocCode}</if>
            <if test="originLocationCode != null  and originLocationCode != ''"> and origin_location_code = #{originLocationCode}</if>
            <if test="externalSkuLotNo != null  and externalSkuLotNo != ''"> and external_sku_lot_no = #{externalSkuLotNo}</if>
            <if test="manufDate != null "> and manuf_date = #{manufDate}</if>
            <if test="expireDate != null "> and expire_date = #{expireDate}</if>
            <if test="receiveDate != null "> and receive_date = #{receiveDate}</if>
        </where>
    </select>

    <select id="selectQcChangeLineByLineId" parameterType="Long" resultMap="QcChangeLineResult">
        <include refid="selectQcChangeLineVo"/>
        where line_id = #{lineId}
    </select>

    <insert id="insertQcChangeLine" parameterType="QcChangeLine" useGeneratedKeys="true" keyProperty="lineId">
        insert into qc_change_line
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="changeId != null">change_id,</if>
            <if test="targetSkuQuality != null">target_sku_quality,</if>
            <if test="targetInventoryType != null">target_inventory_type,</if>
            <if test="targetSkuLotNo != null">target_sku_lot_no,</if>
            <if test="targetLocationCode != null">target_location_code,</if>
            <if test="targetLocationName != null">target_location_name,</if>
            <if test="quantity != null">quantity,</if>
            <if test="originSkuQuality != null">origin_sku_quality,</if>
            <if test="originInventoryType != null">origin_inventory_type,</if>
            <if test="originSkuLotNo != null">origin_sku_lot_no,</if>
            <if test="originProductionBatchNo != null">origin_production_batch_no,</if>
            <if test="originDocCode != null">origin_doc_code,</if>
            <if test="originLocationCode != null">origin_location_code,</if>
            <if test="externalSkuLotNo != null">external_sku_lot_no,</if>
            <if test="manufDate != null">manuf_date,</if>
            <if test="expireDate != null">expire_date,</if>
            <if test="receiveDate != null">receive_date,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="changeId != null">#{changeId},</if>
            <if test="targetSkuQuality != null">#{targetSkuQuality},</if>
            <if test="targetInventoryType != null">#{targetInventoryType},</if>
            <if test="targetSkuLotNo != null">#{targetSkuLotNo},</if>
            <if test="targetLocationCode != null">#{targetLocationCode},</if>
            <if test="targetLocationName != null">#{targetLocationName},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="originSkuQuality != null">#{originSkuQuality},</if>
            <if test="originInventoryType != null">#{originInventoryType},</if>
            <if test="originSkuLotNo != null">#{originSkuLotNo},</if>
            <if test="originProductionBatchNo != null">#{originProductionBatchNo},</if>
            <if test="originDocCode != null">#{originDocCode},</if>
            <if test="originLocationCode != null">#{originLocationCode},</if>
            <if test="externalSkuLotNo != null">#{externalSkuLotNo},</if>
            <if test="manufDate != null">#{manufDate},</if>
            <if test="expireDate != null">#{expireDate},</if>
            <if test="receiveDate != null">#{receiveDate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateQcChangeLine" parameterType="QcChangeLine">
        update qc_change_line
        <trim prefix="SET" suffixOverrides=",">
            <if test="changeId != null">change_id = #{changeId},</if>
            <if test="targetSkuQuality != null">target_sku_quality = #{targetSkuQuality},</if>
            <if test="targetInventoryType != null">target_inventory_type = #{targetInventoryType},</if>
            <if test="targetSkuLotNo != null">target_sku_lot_no = #{targetSkuLotNo},</if>
            <if test="targetLocationCode != null">target_location_code = #{targetLocationCode},</if>
            <if test="targetLocationName != null">target_location_name = #{targetLocationName},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="originSkuQuality != null">origin_sku_quality = #{originSkuQuality},</if>
            <if test="originInventoryType != null">origin_inventory_type = #{originInventoryType},</if>
            <if test="originSkuLotNo != null">origin_sku_lot_no = #{originSkuLotNo},</if>
            <if test="originProductionBatchNo != null">origin_production_batch_no = #{originProductionBatchNo},</if>
            <if test="originDocCode != null">origin_doc_code = #{originDocCode},</if>
            <if test="originLocationCode != null">origin_location_code = #{originLocationCode},</if>
            <if test="externalSkuLotNo != null">external_sku_lot_no = #{externalSkuLotNo},</if>
            <if test="manufDate != null">manuf_date = #{manufDate},</if>
            <if test="expireDate != null">expire_date = #{expireDate},</if>
            <if test="receiveDate != null">receive_date = #{receiveDate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where line_id = #{lineId}
    </update>

    <delete id="deleteQcChangeLineByLineId" parameterType="Long">
        delete from qc_change_line where line_id = #{lineId}
    </delete>

    <delete id="deleteQcChangeLineByLineIds" parameterType="String">
        delete from qc_change_line where line_id in
        <foreach item="lineId" collection="array" open="(" separator="," close=")">
            #{lineId}
        </foreach>
    </delete>
</mapper>