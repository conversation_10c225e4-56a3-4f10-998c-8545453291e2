<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.qc.mapper.QcTemplateProductMapper">
    
    <resultMap type="QcTemplateProduct" id="QcTemplateProductResult">
        <result property="recordId"    column="record_id"    />
        <result property="templateId"    column="template_id"    />
        <result property="itemId"    column="item_id"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemName"    column="item_name"    />
        <result property="specification"    column="specification"    />
        <result property="unitOfMeasure"    column="unit_of_measure"    />
        <result property="quantityCheck"    column="quantity_check"    />
        <result property="quantityUnqualified"    column="quantity_unqualified"    />
        <result property="crRate"    column="cr_rate"    />
        <result property="majRate"    column="maj_rate"    />
        <result property="minRate"    column="min_rate"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectQcTemplateProductVo">
        select record_id, template_id, item_id, item_code, item_name, specification, unit_of_measure, quantity_check, quantity_unqualified, cr_rate, maj_rate, min_rate, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from qc_template_product
    </sql>

    <select id="selectQcTemplateProductList" parameterType="QcTemplateProduct" resultMap="QcTemplateProductResult">
        <include refid="selectQcTemplateProductVo"/>
        <where>  
            <if test="templateId != null "> and template_id = #{templateId}</if>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code = #{itemCode}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="specification != null  and specification != ''"> and specification = #{specification}</if>
            <if test="unitOfMeasure != null  and unitOfMeasure != ''"> and unit_of_measure = #{unitOfMeasure}</if>
            <if test="quantityCheck != null "> and quantity_check = #{quantityCheck}</if>
            <if test="quantityUnqualified != null "> and quantity_unqualified = #{quantityUnqualified}</if>
            <if test="crRate != null "> and cr_rate = #{crRate}</if>
            <if test="majRate != null "> and maj_rate = #{majRate}</if>
            <if test="minRate != null "> and min_rate = #{minRate}</if>
        </where>
    </select>
    
    <select id="selectQcTemplateProductByRecordId" parameterType="Long" resultMap="QcTemplateProductResult">
        <include refid="selectQcTemplateProductVo"/>
        where record_id = #{recordId}
    </select>

    <select id="checkProductUnique" parameterType="QcTemplateProduct" resultMap="QcTemplateProductResult">
        <include refid="selectQcTemplateProductVo"/>
        where item_id = #{itemId}
    </select>

    <insert id="insertQcTemplateProduct" parameterType="QcTemplateProduct" useGeneratedKeys="true" keyProperty="recordId">
        insert into qc_template_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="templateId != null">template_id,</if>
            <if test="itemId != null">item_id,</if>
            <if test="itemCode != null">item_code,</if>
            <if test="itemName != null">item_name,</if>
            <if test="specification != null">specification,</if>
            <if test="unitOfMeasure != null">unit_of_measure,</if>
            <if test="quantityCheck != null">quantity_check,</if>
            <if test="quantityUnqualified != null">quantity_unqualified,</if>
            <if test="crRate != null">cr_rate,</if>
            <if test="majRate != null">maj_rate,</if>
            <if test="minRate != null">min_rate,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="templateId != null">#{templateId},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="itemCode != null">#{itemCode},</if>
            <if test="itemName != null">#{itemName},</if>
            <if test="specification != null">#{specification},</if>
            <if test="unitOfMeasure != null">#{unitOfMeasure},</if>
            <if test="quantityCheck != null">#{quantityCheck},</if>
            <if test="quantityUnqualified != null">#{quantityUnqualified},</if>
            <if test="crRate != null">#{crRate},</if>
            <if test="majRate != null">#{majRate},</if>
            <if test="minRate != null">#{minRate},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateQcTemplateProduct" parameterType="QcTemplateProduct">
        update qc_template_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="templateId != null">template_id = #{templateId},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="itemCode != null">item_code = #{itemCode},</if>
            <if test="itemName != null">item_name = #{itemName},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="unitOfMeasure != null">unit_of_measure = #{unitOfMeasure},</if>
            <if test="quantityCheck != null">quantity_check = #{quantityCheck},</if>
            <if test="quantityUnqualified != null">quantity_unqualified = #{quantityUnqualified},</if>
            <if test="crRate != null">cr_rate = #{crRate},</if>
            <if test="majRate != null">maj_rate = #{majRate},</if>
            <if test="minRate != null">min_rate = #{minRate},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where record_id = #{recordId}
    </update>

    <delete id="deleteQcTemplateProductByRecordId" parameterType="Long">
        delete from qc_template_product where record_id = #{recordId}
    </delete>

    <delete id="deleteQcTemplateProductByRecordIds" parameterType="String">
        delete from qc_template_product where record_id in 
        <foreach item="recordId" collection="array" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </delete>

    <delete id="deleteByTemplateId" parameterType="Long">
        delete from qc_template_product where template_id = #{templateId}
    </delete>

</mapper>