<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.md.mapper.MdWorkshopMapper">
    <resultMap type="MdWorkshop" id="MdWorkshopResult">
        <result property="workshopId"    column="workshop_id"    />
        <result property="workshopCode"    column="workshop_code"    />
        <result property="workshopName"    column="workshop_name"    />
        <result property="area"    column="area"    />
        <result property="charge"    column="charge"    />
        <result property="enableFlag"    column="enable_flag"    />
        <result property="avlLineSideWarehouseCode"    column="avl_line_side_warehouse_code"    />
        <result property="avlLineSideWarehouseName"    column="avl_line_side_warehouse_name"    />
        <result property="avlLineSideZoneCode"    column="avl_line_side_zone_code"    />
        <result property="avlLineSideZoneName"    column="avl_line_side_zone_name"    />
        <result property="avlLineSideLocationCode"    column="avl_line_side_location_code"    />
        <result property="avlLineSideLocationName"    column="avl_line_side_location_name"    />
        <result property="damageLineSideWarehouseCode"    column="damage_line_side_warehouse_code"    />
        <result property="damageLineSideWarehouseName"    column="damage_line_side_warehouse_name"    />
        <result property="damageLineSideZoneCode"    column="damage_line_side_zone_code"    />
        <result property="damageLineSideZoneName"    column="damage_line_side_zone_name"    />
        <result property="damageLineSideLocationCode"    column="damage_line_side_location_code"    />
        <result property="damageLineSideLocationName"    column="damage_line_side_location_name"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMdWorkshopVo">
        select workshop_id, workshop_code, workshop_name, area, charge, enable_flag, avl_line_side_warehouse_code, avl_line_side_warehouse_name, avl_line_side_zone_code, avl_line_side_zone_name, avl_line_side_location_code, avl_line_side_location_name, damage_line_side_warehouse_code, damage_line_side_warehouse_name, damage_line_side_zone_code, damage_line_side_zone_name, damage_line_side_location_code, damage_line_side_location_name, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from md_workshop
    </sql>

    <select id="selectMdWorkshopList" parameterType="MdWorkshop" resultMap="MdWorkshopResult">
        <include refid="selectMdWorkshopVo"/>
        <where>
            <if test="workshopCode != null  and workshopCode != ''"> and workshop_code = #{workshopCode}</if>
            <if test="workshopName != null  and workshopName != ''"> and workshop_name like concat('%', #{workshopName}, '%')</if>
            <if test="area != null "> and area = #{area}</if>
            <if test="charge != null  and charge != ''"> and charge = #{charge}</if>
            <if test="enableFlag != null  and enableFlag != ''"> and enable_flag = #{enableFlag}</if>
            <if test="avlLineSideWarehouseCode != null  and avlLineSideWarehouseCode != ''"> and avl_line_side_warehouse_code = #{avlLineSideWarehouseCode}</if>
            <if test="avlLineSideWarehouseName != null  and avlLineSideWarehouseName != ''"> and avl_line_side_warehouse_name like concat('%', #{avlLineSideWarehouseName}, '%')</if>
            <if test="avlLineSideZoneCode != null  and avlLineSideZoneCode != ''"> and avl_line_side_zone_code = #{avlLineSideZoneCode}</if>
            <if test="avlLineSideZoneName != null  and avlLineSideZoneName != ''"> and avl_line_side_zone_name like concat('%', #{avlLineSideZoneName}, '%')</if>
            <if test="avlLineSideLocationCode != null  and avlLineSideLocationCode != ''"> and avl_line_side_location_code = #{avlLineSideLocationCode}</if>
            <if test="avlLineSideLocationName != null  and avlLineSideLocationName != ''"> and avl_line_side_location_name like concat('%', #{avlLineSideLocationName}, '%')</if>
            <if test="damageLineSideWarehouseCode != null  and damageLineSideWarehouseCode != ''"> and damage_line_side_warehouse_code = #{damageLineSideWarehouseCode}</if>
            <if test="damageLineSideWarehouseName != null  and damageLineSideWarehouseName != ''"> and damage_line_side_warehouse_name like concat('%', #{damageLineSideWarehouseName}, '%')</if>
            <if test="damageLineSideZoneCode != null  and damageLineSideZoneCode != ''"> and damage_line_side_zone_code = #{damageLineSideZoneCode}</if>
            <if test="damageLineSideZoneName != null  and damageLineSideZoneName != ''"> and damage_line_side_zone_name like concat('%', #{damageLineSideZoneName}, '%')</if>
            <if test="damageLineSideLocationCode != null  and damageLineSideLocationCode != ''"> and damage_line_side_location_code = #{damageLineSideLocationCode}</if>
            <if test="damageLineSideLocationName != null  and damageLineSideLocationName != ''"> and damage_line_side_location_name like concat('%', #{damageLineSideLocationName}, '%')</if>
            <if test="attr1 != null  and attr1 != ''"> and attr1 = #{attr1}</if>
            <if test="attr2 != null  and attr2 != ''"> and attr2 = #{attr2}</if>
            <if test="attr3 != null "> and attr3 = #{attr3}</if>
            <if test="attr4 != null "> and attr4 = #{attr4}</if>
        </where>
    </select>

    <select id="selectMdWorkshopByWorkshopId" parameterType="Long" resultMap="MdWorkshopResult">
        <include refid="selectMdWorkshopVo"/>
        where workshop_id = #{workshopId}
    </select>

    <insert id="insertMdWorkshop" parameterType="MdWorkshop" useGeneratedKeys="true" keyProperty="workshopId">
        insert into md_workshop
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workshopCode != null and workshopCode != ''">workshop_code,</if>
            <if test="workshopName != null and workshopName != ''">workshop_name,</if>
            <if test="area != null">area,</if>
            <if test="charge != null">charge,</if>
            <if test="enableFlag != null and enableFlag != ''">enable_flag,</if>
            <if test="avlLineSideWarehouseCode != null and avlLineSideWarehouseCode != ''">avl_line_side_warehouse_code,</if>
            <if test="avlLineSideWarehouseName != null and avlLineSideWarehouseName != ''">avl_line_side_warehouse_name,</if>
            <if test="avlLineSideZoneCode != null">avl_line_side_zone_code,</if>
            <if test="avlLineSideZoneName != null">avl_line_side_zone_name,</if>
            <if test="avlLineSideLocationCode != null">avl_line_side_location_code,</if>
            <if test="avlLineSideLocationName != null">avl_line_side_location_name,</if>
            <if test="damageLineSideWarehouseCode != null and damageLineSideWarehouseCode != ''">damage_line_side_warehouse_code,</if>
            <if test="damageLineSideWarehouseName != null and damageLineSideWarehouseName != ''">damage_line_side_warehouse_name,</if>
            <if test="damageLineSideZoneCode != null">damage_line_side_zone_code,</if>
            <if test="damageLineSideZoneName != null">damage_line_side_zone_name,</if>
            <if test="damageLineSideLocationCode != null">damage_line_side_location_code,</if>
            <if test="damageLineSideLocationName != null">damage_line_side_location_name,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workshopCode != null and workshopCode != ''">#{workshopCode},</if>
            <if test="workshopName != null and workshopName != ''">#{workshopName},</if>
            <if test="area != null">#{area},</if>
            <if test="charge != null">#{charge},</if>
            <if test="enableFlag != null and enableFlag != ''">#{enableFlag},</if>
            <if test="avlLineSideWarehouseCode != null and avlLineSideWarehouseCode != ''">#{avlLineSideWarehouseCode},</if>
            <if test="avlLineSideWarehouseName != null and avlLineSideWarehouseName != ''">#{avlLineSideWarehouseName},</if>
            <if test="avlLineSideZoneCode != null">#{avlLineSideZoneCode},</if>
            <if test="avlLineSideZoneName != null">#{avlLineSideZoneName},</if>
            <if test="avlLineSideLocationCode != null">#{avlLineSideLocationCode},</if>
            <if test="avlLineSideLocationName != null">#{avlLineSideLocationName},</if>
            <if test="damageLineSideWarehouseCode != null and damageLineSideWarehouseCode != ''">#{damageLineSideWarehouseCode},</if>
            <if test="damageLineSideWarehouseName != null and damageLineSideWarehouseName != ''">#{damageLineSideWarehouseName},</if>
            <if test="damageLineSideZoneCode != null">#{damageLineSideZoneCode},</if>
            <if test="damageLineSideZoneName != null">#{damageLineSideZoneName},</if>
            <if test="damageLineSideLocationCode != null">#{damageLineSideLocationCode},</if>
            <if test="damageLineSideLocationName != null">#{damageLineSideLocationName},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateMdWorkshop" parameterType="MdWorkshop">
        update md_workshop
        <trim prefix="SET" suffixOverrides=",">
            <if test="workshopCode != null and workshopCode != ''">workshop_code = #{workshopCode},</if>
            <if test="workshopName != null and workshopName != ''">workshop_name = #{workshopName},</if>
            <if test="area != null">area = #{area},</if>
            <if test="charge != null">charge = #{charge},</if>
            <if test="enableFlag != null and enableFlag != ''">enable_flag = #{enableFlag},</if>
            <if test="avlLineSideWarehouseCode != null and avlLineSideWarehouseCode != ''">avl_line_side_warehouse_code = #{avlLineSideWarehouseCode},</if>
            <if test="avlLineSideWarehouseName != null and avlLineSideWarehouseName != ''">avl_line_side_warehouse_name = #{avlLineSideWarehouseName},</if>
            <if test="avlLineSideZoneCode != null">avl_line_side_zone_code = #{avlLineSideZoneCode},</if>
            <if test="avlLineSideZoneName != null">avl_line_side_zone_name = #{avlLineSideZoneName},</if>
            <if test="avlLineSideLocationCode != null">avl_line_side_location_code = #{avlLineSideLocationCode},</if>
            <if test="avlLineSideLocationName != null">avl_line_side_location_name = #{avlLineSideLocationName},</if>
            <if test="damageLineSideWarehouseCode != null and damageLineSideWarehouseCode != ''">damage_line_side_warehouse_code = #{damageLineSideWarehouseCode},</if>
            <if test="damageLineSideWarehouseName != null and damageLineSideWarehouseName != ''">damage_line_side_warehouse_name = #{damageLineSideWarehouseName},</if>
            <if test="damageLineSideZoneCode != null">damage_line_side_zone_code = #{damageLineSideZoneCode},</if>
            <if test="damageLineSideZoneName != null">damage_line_side_zone_name = #{damageLineSideZoneName},</if>
            <if test="damageLineSideLocationCode != null">damage_line_side_location_code = #{damageLineSideLocationCode},</if>
            <if test="damageLineSideLocationName != null">damage_line_side_location_name = #{damageLineSideLocationName},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where workshop_id = #{workshopId}
    </update>

    <delete id="deleteMdWorkshopByWorkshopId" parameterType="Long">
        delete from md_workshop where workshop_id = #{workshopId}
    </delete>

    <delete id="deleteMdWorkshopByWorkshopIds" parameterType="String">
        delete from md_workshop where workshop_id in
        <foreach item="workshopId" collection="array" open="(" separator="," close=")">
            #{workshopId}
        </foreach>
    </delete>
</mapper>