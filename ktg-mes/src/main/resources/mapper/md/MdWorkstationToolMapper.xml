<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.md.mapper.MdWorkstationToolMapper">
    
    <resultMap type="MdWorkstationTool" id="MdWorkstationToolResult">
        <result property="recordId"    column="record_id"    />
        <result property="workstationId"    column="workstation_id"    />
        <result property="toolTypeId"    column="tool_type_id"    />
        <result property="toolTypeCode"    column="tool_type_code"    />
        <result property="toolTypeName"    column="tool_type_name"    />
        <result property="quantity"    column="quantity"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMdWorkstationToolVo">
        select record_id, workstation_id, tool_type_id, tool_type_code, tool_type_name, quantity, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from md_workstation_tool
    </sql>

    <select id="selectMdWorkstationToolList" parameterType="MdWorkstationTool" resultMap="MdWorkstationToolResult">
        <include refid="selectMdWorkstationToolVo"/>
        <where>  
            <if test="workstationId != null "> and workstation_id = #{workstationId}</if>
            <if test="toolTypeId != null "> and tool_type_id = #{toolTypeId}</if>
            <if test="toolTypeCode != null  and toolTypeCode != ''"> and tool_type_code = #{toolTypeCode}</if>
            <if test="toolTypeName != null  and toolTypeName != ''"> and tool_type_name like concat('%', #{toolTypeName}, '%')</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
        </where>
    </select>
    
    <select id="selectMdWorkstationToolByRecordId" parameterType="Long" resultMap="MdWorkstationToolResult">
        <include refid="selectMdWorkstationToolVo"/>
        where record_id = #{recordId}
    </select>

    <select id="checkToolTypeExists" parameterType="MdWorkstationTool" resultMap="MdWorkstationToolResult">
        <include refid="selectMdWorkstationToolVo"/>
        where tool_type_id = #{toolTypeId} and workstation_id = #{workstationId} limit 1
    </select>

    <insert id="insertMdWorkstationTool" parameterType="MdWorkstationTool" useGeneratedKeys="true" keyProperty="recordId">
        insert into md_workstation_tool
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workstationId != null">workstation_id,</if>
            <if test="toolTypeId != null">tool_type_id,</if>
            <if test="toolTypeCode != null">tool_type_code,</if>
            <if test="toolTypeName != null">tool_type_name,</if>
            <if test="quantity != null">quantity,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workstationId != null">#{workstationId},</if>
            <if test="toolTypeId != null">#{toolTypeId},</if>
            <if test="toolTypeCode != null">#{toolTypeCode},</if>
            <if test="toolTypeName != null">#{toolTypeName},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMdWorkstationTool" parameterType="MdWorkstationTool">
        update md_workstation_tool
        <trim prefix="SET" suffixOverrides=",">
            <if test="workstationId != null">workstation_id = #{workstationId},</if>
            <if test="toolTypeId != null">tool_type_id = #{toolTypeId},</if>
            <if test="toolTypeCode != null">tool_type_code = #{toolTypeCode},</if>
            <if test="toolTypeName != null">tool_type_name = #{toolTypeName},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where record_id = #{recordId}
    </update>

    <delete id="deleteMdWorkstationToolByRecordId" parameterType="Long">
        delete from md_workstation_tool where record_id = #{recordId}
    </delete>

    <delete id="deleteMdWorkstationToolByRecordIds" parameterType="String">
        delete from md_workstation_tool where record_id in 
        <foreach item="recordId" collection="array" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </delete>

    <delete id="deleteByWorkstationId" parameterType="Long">
        delete from md_workstation_tool where workstation_id = #{workstationId}
    </delete>
</mapper>