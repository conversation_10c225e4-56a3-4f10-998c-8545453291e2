<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.md.mapper.MdWorkshopMapper">
    <select id="checkWorkshopCodeUnique" parameterType="MdWorkshop" resultMap="MdWorkshopResult">
        <include refid="selectMdWorkshopVo"/>
        where workshop_code = #{workshopCode} limit 1
    </select>

    <select id="checkWorkshopNameUnique" parameterType="MdWorkshop" resultMap="MdWorkshopResult">
        <include refid="selectMdWorkshopVo"/>
        where workshop_name = #{workshopName} limit 1
    </select>
</mapper>