<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.md.mapper.MdProductBomMapper">
    
    <resultMap type="MdProductBom" id="MdProductBomResult">
        <result property="bomId"    column="bom_id"    />
        <result property="itemId"    column="item_id"    />
        <result property="bomItemId"    column="bom_item_id"    />
        <result property="bomItemCode"    column="bom_item_code"    />
        <result property="bomItemName"    column="bom_item_name"    />
        <result property="bomItemSpec"    column="bom_item_spec"    />
        <result property="unitOfMeasure"    column="unit_of_measure"    />
        <result property="itemOrProduct"    column="item_or_product"    />
        <result property="quantity"    column="quantity"    />
        <result property="enableFlag"    column="enable_flag"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMdProductBomVo">
        select bom_id, item_id, bom_item_id, bom_item_code, bom_item_name, bom_item_spec, unit_of_measure, item_or_product, quantity, enable_flag, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from md_product_bom
    </sql>

    <select id="selectMdProductBomList" parameterType="MdProductBom" resultMap="MdProductBomResult">
        <include refid="selectMdProductBomVo"/>
        <where>  
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="bomItemId != null "> and bom_item_id = #{bomItemId}</if>
            <if test="bomItemCode != null  and bomItemCode != ''"> and bom_item_code = #{bomItemCode}</if>
            <if test="bomItemName != null  and bomItemName != ''"> and bom_item_name like concat('%', #{bomItemName}, '%')</if>
            <if test="bomItemSpec != null  and bomItemSpec != ''"> and bom_item_spec = #{bomItemSpec}</if>
            <if test="unitOfMeasure != null  and unitOfMeasure != ''"> and unit_of_measure = #{unitOfMeasure}</if>
            <if test="itemOrProduct != null  and itemOrProduct != ''"> and item_or_product = #{itemOrProduct}</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
            <if test="enableFlag != null  and enableFlag != ''"> and enable_flag = #{enableFlag}</if>
        </where>
    </select>
    
    <select id="selectMdProductBomByBomId" parameterType="Long" resultMap="MdProductBomResult">
        <include refid="selectMdProductBomVo"/>
        where bom_id = #{bomId}
    </select>
        
    <insert id="insertMdProductBom" parameterType="MdProductBom" useGeneratedKeys="true" keyProperty="bomId">
        insert into md_product_bom
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="itemId != null">item_id,</if>
            <if test="bomItemId != null">bom_item_id,</if>
            <if test="bomItemCode != null and bomItemCode != ''">bom_item_code,</if>
            <if test="bomItemName != null and bomItemName != ''">bom_item_name,</if>
            <if test="bomItemSpec != null">bom_item_spec,</if>
            <if test="unitOfMeasure != null and unitOfMeasure != ''">unit_of_measure,</if>
            <if test="itemOrProduct != null and itemOrProduct != ''">item_or_product,</if>
            <if test="quantity != null">quantity,</if>
            <if test="enableFlag != null and enableFlag != ''">enable_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="itemId != null">#{itemId},</if>
            <if test="bomItemId != null">#{bomItemId},</if>
            <if test="bomItemCode != null and bomItemCode != ''">#{bomItemCode},</if>
            <if test="bomItemName != null and bomItemName != ''">#{bomItemName},</if>
            <if test="bomItemSpec != null">#{bomItemSpec},</if>
            <if test="unitOfMeasure != null and unitOfMeasure != ''">#{unitOfMeasure},</if>
            <if test="itemOrProduct != null and itemOrProduct != ''">#{itemOrProduct},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="enableFlag != null and enableFlag != ''">#{enableFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMdProductBom" parameterType="MdProductBom">
        update md_product_bom
        <trim prefix="SET" suffixOverrides=",">
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="bomItemId != null">bom_item_id = #{bomItemId},</if>
            <if test="bomItemCode != null and bomItemCode != ''">bom_item_code = #{bomItemCode},</if>
            <if test="bomItemName != null and bomItemName != ''">bom_item_name = #{bomItemName},</if>
            <if test="bomItemSpec != null">bom_item_spec = #{bomItemSpec},</if>
            <if test="unitOfMeasure != null and unitOfMeasure != ''">unit_of_measure = #{unitOfMeasure},</if>
            <if test="itemOrProduct != null and itemOrProduct != ''">item_or_product = #{itemOrProduct},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="enableFlag != null and enableFlag != ''">enable_flag = #{enableFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where bom_id = #{bomId}
    </update>

    <delete id="deleteMdProductBomByBomId" parameterType="Long">
        delete from md_product_bom where bom_id = #{bomId}
    </delete>

    <delete id="deleteMdProductBomByBomIds" parameterType="String">
        delete from md_product_bom where bom_id in 
        <foreach item="bomId" collection="array" open="(" separator="," close=")">
            #{bomId}
        </foreach>
    </delete>
</mapper>