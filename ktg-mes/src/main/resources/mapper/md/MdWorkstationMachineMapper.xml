<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.md.mapper.MdWorkstationMachineMapper">
    
    <resultMap type="MdWorkstationMachine" id="MdWorkstationMachineResult">
        <result property="recordId"    column="record_id"    />
        <result property="workstationId"    column="workstation_id"    />
        <result property="machineryId"    column="machinery_id"    />
        <result property="machineryCode"    column="machinery_code"    />
        <result property="machineryName"    column="machinery_name"    />
        <result property="quantity"    column="quantity"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMdWorkstationMachineVo">
        select record_id, workstation_id, machinery_id, machinery_code, machinery_name, quantity, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from md_workstation_machine
    </sql>

    <select id="selectMdWorkstationMachineList" parameterType="MdWorkstationMachine" resultMap="MdWorkstationMachineResult">
        <include refid="selectMdWorkstationMachineVo"/>
        <where>  
            <if test="workstationId != null "> and workstation_id = #{workstationId}</if>
            <if test="machineryId != null "> and machinery_id = #{machineryId}</if>
            <if test="machineryCode != null  and machineryCode != ''"> and machinery_code = #{machineryCode}</if>
            <if test="machineryName != null  and machineryName != ''"> and machinery_name like concat('%', #{machineryName}, '%')</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
        </where>
    </select>
    
    <select id="selectMdWorkstationMachineByRecordId" parameterType="Long" resultMap="MdWorkstationMachineResult">
        <include refid="selectMdWorkstationMachineVo"/>
        where record_id = #{recordId}
    </select>

    <select id="checkMachineryExists" parameterType="MdWorkstationMachine" resultMap="MdWorkstationMachineResult">
        <include refid="selectMdWorkstationMachineVo"/>
        where machinery_code = #{machineryCode} limit 1
    </select>

    <insert id="insertMdWorkstationMachine" parameterType="MdWorkstationMachine" useGeneratedKeys="true" keyProperty="recordId">
        insert into md_workstation_machine
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workstationId != null">workstation_id,</if>
            <if test="machineryId != null">machinery_id,</if>
            <if test="machineryCode != null">machinery_code,</if>
            <if test="machineryName != null">machinery_name,</if>
            <if test="quantity != null">quantity,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workstationId != null">#{workstationId},</if>
            <if test="machineryId != null">#{machineryId},</if>
            <if test="machineryCode != null">#{machineryCode},</if>
            <if test="machineryName != null">#{machineryName},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMdWorkstationMachine" parameterType="MdWorkstationMachine">
        update md_workstation_machine
        <trim prefix="SET" suffixOverrides=",">
            <if test="workstationId != null">workstation_id = #{workstationId},</if>
            <if test="machineryId != null">machinery_id = #{machineryId},</if>
            <if test="machineryCode != null">machinery_code = #{machineryCode},</if>
            <if test="machineryName != null">machinery_name = #{machineryName},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where record_id = #{recordId}
    </update>

    <delete id="deleteMdWorkstationMachineByRecordId" parameterType="Long">
        delete from md_workstation_machine where record_id = #{recordId}
    </delete>

    <delete id="deleteMdWorkstationMachineByRecordIds" parameterType="String">
        delete from md_workstation_machine where record_id in 
        <foreach item="recordId" collection="array" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </delete>

    <delete id="deleteByWorkstationId" parameterType="Long">
        delete from md_workstation_machine where workstation_id = #{workstationId}
    </delete>
</mapper>