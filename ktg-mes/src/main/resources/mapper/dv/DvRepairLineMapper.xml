<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.dv.mapper.DvRepairLineMapper">
    
    <resultMap type="DvRepairLine" id="DvRepairLineResult">
        <result property="lineId"    column="line_id"    />
        <result property="repairId"    column="repair_id"    />
        <result property="subjectId"    column="subject_id"    />
        <result property="subjectCode"    column="subject_code"    />
        <result property="subjectName"    column="subject_name"    />
        <result property="subjectType"    column="subject_type"    />
        <result property="subjectContent"    column="subject_content"    />
        <result property="subjectStandard"    column="subject_standard"    />
        <result property="malfunction"    column="malfunction"    />
        <result property="malfunctionUrl"    column="malfunction_url"    />
        <result property="repairDes"    column="repair_des"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDvRepairLineVo">
        select line_id, repair_id, subject_id, subject_code, subject_name, subject_type, subject_content, subject_standard, malfunction, malfunction_url, repair_des, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from dv_repair_line
    </sql>

    <select id="selectDvRepairLineList" parameterType="DvRepairLine" resultMap="DvRepairLineResult">
        <include refid="selectDvRepairLineVo"/>
        <where>  
            <if test="repairId != null "> and repair_id = #{repairId}</if>
            <if test="subjectId != null "> and subject_id = #{subjectId}</if>
            <if test="subjectCode != null  and subjectCode != ''"> and subject_code = #{subjectCode}</if>
            <if test="subjectName != null  and subjectName != ''"> and subject_name like concat('%', #{subjectName}, '%')</if>
            <if test="subjectType != null  and subjectType != ''"> and subject_type = #{subjectType}</if>
            <if test="subjectContent != null  and subjectContent != ''"> and subject_content = #{subjectContent}</if>
            <if test="subjectStandard != null  and subjectStandard != ''"> and subject_standard = #{subjectStandard}</if>
            <if test="malfunction != null  and malfunction != ''"> and malfunction = #{malfunction}</if>
            <if test="malfunctionUrl != null  and malfunctionUrl != ''"> and malfunction_url = #{malfunctionUrl}</if>
            <if test="repairDes != null  and repairDes != ''"> and repair_des = #{repairDes}</if>
        </where>
    </select>
    
    <select id="selectDvRepairLineByLineId" parameterType="Long" resultMap="DvRepairLineResult">
        <include refid="selectDvRepairLineVo"/>
        where line_id = #{lineId}
    </select>
        
    <insert id="insertDvRepairLine" parameterType="DvRepairLine" useGeneratedKeys="true" keyProperty="lineId">
        insert into dv_repair_line
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="repairId != null">repair_id,</if>
            <if test="subjectId != null">subject_id,</if>
            <if test="subjectCode != null and subjectCode != ''">subject_code,</if>
            <if test="subjectName != null">subject_name,</if>
            <if test="subjectType != null">subject_type,</if>
            <if test="subjectContent != null and subjectContent != ''">subject_content,</if>
            <if test="subjectStandard != null">subject_standard,</if>
            <if test="malfunction != null">malfunction,</if>
            <if test="malfunctionUrl != null">malfunction_url,</if>
            <if test="repairDes != null">repair_des,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="repairId != null">#{repairId},</if>
            <if test="subjectId != null">#{subjectId},</if>
            <if test="subjectCode != null and subjectCode != ''">#{subjectCode},</if>
            <if test="subjectName != null">#{subjectName},</if>
            <if test="subjectType != null">#{subjectType},</if>
            <if test="subjectContent != null and subjectContent != ''">#{subjectContent},</if>
            <if test="subjectStandard != null">#{subjectStandard},</if>
            <if test="malfunction != null">#{malfunction},</if>
            <if test="malfunctionUrl != null">#{malfunctionUrl},</if>
            <if test="repairDes != null">#{repairDes},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDvRepairLine" parameterType="DvRepairLine">
        update dv_repair_line
        <trim prefix="SET" suffixOverrides=",">
            <if test="repairId != null">repair_id = #{repairId},</if>
            <if test="subjectId != null">subject_id = #{subjectId},</if>
            <if test="subjectCode != null and subjectCode != ''">subject_code = #{subjectCode},</if>
            <if test="subjectName != null">subject_name = #{subjectName},</if>
            <if test="subjectType != null">subject_type = #{subjectType},</if>
            <if test="subjectContent != null and subjectContent != ''">subject_content = #{subjectContent},</if>
            <if test="subjectStandard != null">subject_standard = #{subjectStandard},</if>
            <if test="malfunction != null">malfunction = #{malfunction},</if>
            <if test="malfunctionUrl != null">malfunction_url = #{malfunctionUrl},</if>
            <if test="repairDes != null">repair_des = #{repairDes},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where line_id = #{lineId}
    </update>

    <delete id="deleteDvRepairLineByLineId" parameterType="Long">
        delete from dv_repair_line where line_id = #{lineId}
    </delete>

    <delete id="deleteDvRepairLineByLineIds" parameterType="String">
        delete from dv_repair_line where line_id in 
        <foreach item="lineId" collection="array" open="(" separator="," close=")">
            #{lineId}
        </foreach>
    </delete>

    <delete id="deleteByRepairId" parameterType="Long">
        delete from dv_repair_line where repair_id = #{repairId}
    </delete>
</mapper>