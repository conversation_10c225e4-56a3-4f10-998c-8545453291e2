<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.dv.mapper.DvSubjectMapper">
    
    <resultMap type="DvSubject" id="DvSubjectResult">
        <result property="subjectId"    column="subject_id"    />
        <result property="subjectCode"    column="subject_code"    />
        <result property="subjectName"    column="subject_name"    />
        <result property="subjectType"    column="subject_type"    />
        <result property="subjectContent"    column="subject_content"    />
        <result property="subjectStandard"    column="subject_standard"    />
        <result property="enableFlag"    column="enable_flag"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDvSubjectVo">
        select subject_id, subject_code, subject_name, subject_type, subject_content, subject_standard, enable_flag, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from dv_subject
    </sql>

    <select id="selectDvSubjectList" parameterType="DvSubject" resultMap="DvSubjectResult">
        <include refid="selectDvSubjectVo"/>
        <where>  
            <if test="subjectCode != null  and subjectCode != ''"> and subject_code = #{subjectCode}</if>
            <if test="subjectName != null  and subjectName != ''"> and subject_name like concat('%', #{subjectName}, '%')</if>
            <if test="subjectType != null  and subjectType != ''"> and subject_type = #{subjectType}</if>
            <if test="subjectContent != null  and subjectContent != ''"> and subject_content = #{subjectContent}</if>
            <if test="subjectStandard != null  and subjectStandard != ''"> and subject_standard = #{subjectStandard}</if>
            <if test="enableFlag != null  and enableFlag != ''"> and enable_flag = #{enableFlag}</if>
        </where>
    </select>
    
    <select id="selectDvSubjectBySubjectId" parameterType="Long" resultMap="DvSubjectResult">
        <include refid="selectDvSubjectVo"/>
        where subject_id = #{subjectId}
    </select>

    <select id="checkSubjectCodeUnique" parameterType="DvSubject" resultMap="DvSubjectResult">
        <include refid="selectDvSubjectVo"/>
        where subject_code = #{subjectCode}
    </select>

    <insert id="insertDvSubject" parameterType="DvSubject" useGeneratedKeys="true" keyProperty="subjectId">
        insert into dv_subject
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="subjectCode != null and subjectCode != ''">subject_code,</if>
            <if test="subjectName != null and subjectName != ''">subject_name,</if>
            <if test="subjectType != null">subject_type,</if>
            <if test="subjectContent != null and subjectContent != ''">subject_content,</if>
            <if test="subjectStandard != null">subject_standard,</if>
            <if test="enableFlag != null and enableFlag != ''">enable_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="subjectCode != null and subjectCode != ''">#{subjectCode},</if>
            <if test="subjectName != null and subjectName != ''">#{subjectName},</if>
            <if test="subjectType != null">#{subjectType},</if>
            <if test="subjectContent != null and subjectContent != ''">#{subjectContent},</if>
            <if test="subjectStandard != null">#{subjectStandard},</if>
            <if test="enableFlag != null and enableFlag != ''">#{enableFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDvSubject" parameterType="DvSubject">
        update dv_subject
        <trim prefix="SET" suffixOverrides=",">
            <if test="subjectCode != null and subjectCode != ''">subject_code = #{subjectCode},</if>
            <if test="subjectName != null and subjectName != ''">subject_name = #{subjectName},</if>
            <if test="subjectType != null">subject_type = #{subjectType},</if>
            <if test="subjectContent != null and subjectContent != ''">subject_content = #{subjectContent},</if>
            <if test="subjectStandard != null">subject_standard = #{subjectStandard},</if>
            <if test="enableFlag != null and enableFlag != ''">enable_flag = #{enableFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where subject_id = #{subjectId}
    </update>

    <delete id="deleteDvSubjectBySubjectId" parameterType="Long">
        delete from dv_subject where subject_id = #{subjectId}
    </delete>

    <delete id="deleteDvSubjectBySubjectIds" parameterType="String">
        delete from dv_subject where subject_id in 
        <foreach item="subjectId" collection="array" open="(" separator="," close=")">
            #{subjectId}
        </foreach>
    </delete>
</mapper>