<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.cal.mapper.CalHolidayMapper">
    
    <resultMap type="CalHoliday" id="CalHolidayResult">
        <result property="holidayId"    column="holiday_id"    />
        <result property="theDay"    column="the_day"    />
        <result property="holidayType"    column="holiday_type"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCalHolidayVo">
        select holiday_id, the_day, holiday_type, start_time, end_time, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from cal_holiday
    </sql>

    <select id="selectCalHolidayList" parameterType="CalHoliday" resultMap="CalHolidayResult">
        <include refid="selectCalHolidayVo"/>
        <where>  
            <if test="theDay != null "> and the_day = #{theDay}</if>
            <if test="holidayType != null  and holidayType != ''"> and holiday_type = #{holidayType}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
        </where>
    </select>
    
    <select id="selectCalHolidayByHolidayId" parameterType="Long" resultMap="CalHolidayResult">
        <include refid="selectCalHolidayVo"/>
        where holiday_id = #{holidayId}
    </select>
        
    <insert id="insertCalHoliday" parameterType="CalHoliday" useGeneratedKeys="true" keyProperty="holidayId">
        insert into cal_holiday
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="theDay != null">the_day,</if>
            <if test="holidayType != null">holiday_type,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="theDay != null">#{theDay},</if>
            <if test="holidayType != null">#{holidayType},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCalHoliday" parameterType="CalHoliday">
        update cal_holiday
        <trim prefix="SET" suffixOverrides=",">
            <if test="theDay != null">the_day = #{theDay},</if>
            <if test="holidayType != null">holiday_type = #{holidayType},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where holiday_id = #{holidayId}
    </update>

    <delete id="deleteCalHolidayByHolidayId" parameterType="Long">
        delete from cal_holiday where holiday_id = #{holidayId}
    </delete>

    <delete id="deleteCalHolidayByHolidayIds" parameterType="String">
        delete from cal_holiday where holiday_id in 
        <foreach item="holidayId" collection="array" open="(" separator="," close=")">
            #{holidayId}
        </foreach>
    </delete>
</mapper>