<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.pro.mapper.ProWorkorderMapper">
    <resultMap type="ProWorkorder" id="ProWorkorderResult">
        <result property="workorderId"    column="workorder_id"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="workorderCode"    column="workorder_code"    />
        <result property="workorderName"    column="workorder_name"    />
        <result property="orderSource"    column="order_source"    />
        <result property="sourceCode"    column="source_code"    />
        <result property="productId"    column="product_id"    />
        <result property="productCode"    column="product_code"    />
        <result property="productName"    column="product_name"    />
        <result property="productSpc"    column="product_spc"    />
        <result property="unitOfMeasure"    column="unit_of_measure"    />
        <result property="quantity"    column="quantity"    />
        <result property="quantityProduced"    column="quantity_produced"    />
        <result property="quantityChanged"    column="quantity_changed"    />
        <result property="quantityScheduled"    column="quantity_scheduled"    />
        <result property="clientId"    column="client_id"    />
        <result property="clientCode"    column="client_code"    />
        <result property="clientName"    column="client_name"    />
        <result property="requestDate"    column="request_date"    />
        <result property="parentId"    column="parent_id"    />
        <result property="ancestors"    column="ancestors"    />
        <result property="status"    column="status"    />
        <result property="ownerCode"    column="owner_code"    />
        <result property="ownerName"    column="owner_name"    />
        <result property="workshopId"    column="workshop_id"    />
        <result property="workshopName"    column="workshop_name"    />
        <result property="oqcCheck"    column="oqc_check"    />
        <result property="keyItems"    column="key_items"    />
        <result property="remark"    column="remark"    />
        <result property="externalLinkBillNo"    column="external_link_bill_no"    />
        <result property="externalLinkLineNo"    column="external_link_line_no"    />
        <result property="requirement"    column="requirement"    />
        <result property="productionDate"    column="production_date"    />
        <result property="expirationDate"    column="expiration_date"    />
        <result property="shelfLife"    column="shelf_life"    />
        <result property="theoreticalOutput"    column="theoretical_output"    />
        <result property="brand"    column="brand"    />
        <result property="submitStatus"    column="submit_status"    />
        <result property="pushStatus"    column="push_status"    />
        <result property="pushDate"    column="push_date"    />
        <result property="skuWrap"    column="sku_wrap"    />
        <result property="produceBatchCode"    column="produce_batch_code"    />
        <result property="pushErpAttach"    column="push_erp_attach"    />
        <result property="linkType"    column="link_type"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="batchCode"    column="batch_code"    />
        <result property="finishDate"    column="finish_date"    />
        <result property="workorderType"    column="workorder_type"    />
        <result property="vendorId"    column="vendor_id"    />
        <result property="vendorCode"    column="vendor_code"    />
        <result property="vendorName"    column="vendor_name"    />
    </resultMap>

    <sql id="selectProWorkorderVo">
        select workorder_id, warehouse_code, warehouse_name, workorder_code, workorder_name, order_source, source_code, product_id, product_code, product_name, product_spc, unit_of_measure, quantity, quantity_produced, quantity_changed, quantity_scheduled, client_id, client_code, client_name, request_date, parent_id, ancestors, status, owner_code, owner_name, workshop_id, workshop_name, oqc_check, key_items, remark, external_link_bill_no, external_link_line_no, requirement, production_date, expiration_date, shelf_life, theoretical_output, brand, submit_status, push_status, push_date, sku_wrap, produce_batch_code, push_erp_attach, link_type, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time, batch_code, finish_date, workorder_type, vendor_id, vendor_code, vendor_name from pro_workorder
    </sql>

    <select id="selectProWorkorderList" parameterType="ProWorkorder" resultMap="ProWorkorderResult">
        <include refid="selectProWorkorderVo"/>
        <where>
            <if test="warehouseCode != null  and warehouseCode != ''"> and warehouse_code = #{warehouseCode}</if>
            <if test="warehouseName != null  and warehouseName != ''"> and warehouse_name like concat('%', #{warehouseName}, '%')</if>
            <if test="workorderCode != null  and workorderCode != ''"> and workorder_code = #{workorderCode}</if>
            <if test="workorderName != null  and workorderName != ''"> and workorder_name like concat('%', #{workorderName}, '%')</if>
            <if test="orderSource != null  and orderSource != ''"> and order_source = #{orderSource}</if>
            <if test="sourceCode != null  and sourceCode != ''"> and source_code = #{sourceCode}</if>
            <if test="productId != null "> and product_id = #{productId}</if>
            <if test="productCode != null  and productCode != ''"> and product_code = #{productCode}</if>
            <if test="productName != null  and productName != ''"> and product_name like concat('%', #{productName}, '%')</if>
            <if test="productSpc != null  and productSpc != ''"> and product_spc = #{productSpc}</if>
            <if test="unitOfMeasure != null  and unitOfMeasure != ''"> and unit_of_measure = #{unitOfMeasure}</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
            <if test="quantityProduced != null "> and quantity_produced = #{quantityProduced}</if>
            <if test="quantityChanged != null "> and quantity_changed = #{quantityChanged}</if>
            <if test="quantityScheduled != null "> and quantity_scheduled = #{quantityScheduled}</if>
            <if test="clientId != null "> and client_id = #{clientId}</if>
            <if test="clientCode != null  and clientCode != ''"> and client_code = #{clientCode}</if>
            <if test="clientName != null  and clientName != ''"> and client_name like concat('%', #{clientName}, '%')</if>
            <if test="requestDate != null "> and request_date = #{requestDate}</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="ancestors != null  and ancestors != ''"> and ancestors = #{ancestors}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="ownerCode != null  and ownerCode != ''"> and owner_code = #{ownerCode}</if>
            <if test="ownerName != null  and ownerName != ''"> and owner_name like concat('%', #{ownerName}, '%')</if>
            <if test="workshopId != null "> and workshop_id = #{workshopId}</if>
            <if test="workshopName != null  and workshopName != ''"> and workshop_name like concat('%', #{workshopName}, '%')</if>
            <if test="oqcCheck != null  and oqcCheck != ''"> and oqc_check = #{oqcCheck}</if>
            <if test="keyItems != null  and keyItems != ''"> and key_items = #{keyItems}</if>
            <if test="externalLinkBillNo != null  and externalLinkBillNo != ''"> and external_link_bill_no = #{externalLinkBillNo}</if>
            <if test="externalLinkLineNo != null  and externalLinkLineNo != ''"> and external_link_line_no = #{externalLinkLineNo}</if>
            <if test="requirement != null  and requirement != ''"> and requirement = #{requirement}</if>
            <if test="productionDate != null "> and production_date = #{productionDate}</if>
            <if test="expirationDate != null "> and expiration_date = #{expirationDate}</if>
            <if test="shelfLife != null "> and shelf_life = #{shelfLife}</if>
            <if test="theoreticalOutput != null "> and theoretical_output = #{theoreticalOutput}</if>
            <if test="brand != null  and brand != ''"> and brand = #{brand}</if>
            <if test="submitStatus != null  and submitStatus != ''"> and submit_status = #{submitStatus}</if>
            <if test="pushStatus != null  and pushStatus != ''"> and push_status = #{pushStatus}</if>
            <if test="pushDate != null "> and push_date = #{pushDate}</if>
            <if test="skuWrap != null  and skuWrap != ''"> and sku_wrap = #{skuWrap}</if>
            <if test="produceBatchCode != null  and produceBatchCode != ''"> and produce_batch_code = #{produceBatchCode}</if>
            <if test="pushErpAttach != null  and pushErpAttach != ''"> and push_erp_attach = #{pushErpAttach}</if>
            <if test="linkType != null "> and link_type = #{linkType}</if>
            <if test="attr1 != null  and attr1 != ''"> and attr1 = #{attr1}</if>
            <if test="attr2 != null  and attr2 != ''"> and attr2 = #{attr2}</if>
            <if test="attr3 != null "> and attr3 = #{attr3}</if>
            <if test="attr4 != null "> and attr4 = #{attr4}</if>
            <if test="batchCode != null  and batchCode != ''"> and batch_code = #{batchCode}</if>
            <if test="finishDate != null "> and finish_date = #{finishDate}</if>
            <if test="workorderType != null  and workorderType != ''"> and workorder_type = #{workorderType}</if>
            <if test="vendorId != null "> and vendor_id = #{vendorId}</if>
            <if test="vendorCode != null  and vendorCode != ''"> and vendor_code = #{vendorCode}</if>
            <if test="vendorName != null  and vendorName != ''"> and vendor_name like concat('%', #{vendorName}, '%')</if>
        </where>
    </select>

    <select id="selectProWorkorderByWorkorderId" parameterType="Long" resultMap="ProWorkorderResult">
        <include refid="selectProWorkorderVo"/>
        where workorder_id = #{workorderId}
    </select>

    <insert id="insertProWorkorder" parameterType="ProWorkorder" useGeneratedKeys="true" keyProperty="workorderId">
        insert into pro_workorder
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="warehouseCode != null and warehouseCode != ''">warehouse_code,</if>
            <if test="warehouseName != null and warehouseName != ''">warehouse_name,</if>
            <if test="workorderCode != null and workorderCode != ''">workorder_code,</if>
            <if test="workorderName != null and workorderName != ''">workorder_name,</if>
            <if test="orderSource != null and orderSource != ''">order_source,</if>
            <if test="sourceCode != null">source_code,</if>
            <if test="productId != null">product_id,</if>
            <if test="productCode != null and productCode != ''">product_code,</if>
            <if test="productName != null and productName != ''">product_name,</if>
            <if test="productSpc != null">product_spc,</if>
            <if test="unitOfMeasure != null and unitOfMeasure != ''">unit_of_measure,</if>
            <if test="quantity != null">quantity,</if>
            <if test="quantityProduced != null">quantity_produced,</if>
            <if test="quantityChanged != null">quantity_changed,</if>
            <if test="quantityScheduled != null">quantity_scheduled,</if>
            <if test="clientId != null">client_id,</if>
            <if test="clientCode != null">client_code,</if>
            <if test="clientName != null">client_name,</if>
            <if test="requestDate != null">request_date,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="ancestors != null">ancestors,</if>
            <if test="status != null">status,</if>
            <if test="ownerCode != null and ownerCode != ''">owner_code,</if>
            <if test="ownerName != null and ownerName != ''">owner_name,</if>
            <if test="workshopId != null">workshop_id,</if>
            <if test="workshopName != null and workshopName != ''">workshop_name,</if>
            <if test="oqcCheck != null">oqc_check,</if>
            <if test="keyItems != null">key_items,</if>
            <if test="remark != null">remark,</if>
            <if test="externalLinkBillNo != null">external_link_bill_no,</if>
            <if test="externalLinkLineNo != null">external_link_line_no,</if>
            <if test="requirement != null">requirement,</if>
            <if test="productionDate != null">production_date,</if>
            <if test="expirationDate != null">expiration_date,</if>
            <if test="shelfLife != null">shelf_life,</if>
            <if test="theoreticalOutput != null">theoretical_output,</if>
            <if test="brand != null">brand,</if>
            <if test="submitStatus != null">submit_status,</if>
            <if test="pushStatus != null">push_status,</if>
            <if test="pushDate != null">push_date,</if>
            <if test="skuWrap != null">sku_wrap,</if>
            <if test="produceBatchCode != null and produceBatchCode != ''">produce_batch_code,</if>
            <if test="pushErpAttach != null">push_erp_attach,</if>
            <if test="linkType != null">link_type,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="batchCode != null">batch_code,</if>
            <if test="finishDate != null">finish_date,</if>
            <if test="workorderType != null">workorder_type,</if>
            <if test="vendorId != null">vendor_id,</if>
            <if test="vendorCode != null">vendor_code,</if>
            <if test="vendorName != null">vendor_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="warehouseCode != null and warehouseCode != ''">#{warehouseCode},</if>
            <if test="warehouseName != null and warehouseName != ''">#{warehouseName},</if>
            <if test="workorderCode != null and workorderCode != ''">#{workorderCode},</if>
            <if test="workorderName != null">#{workorderName},</if>
            <if test="orderSource != null">#{orderSource},</if>
            <if test="sourceCode != null">#{sourceCode},</if>
            <if test="productId != null">#{productId},</if>
            <if test="productCode != null and productCode != ''">#{productCode},</if>
            <if test="productName != null and productName != ''">#{productName},</if>
            <if test="productSpc != null">#{productSpc},</if>
            <if test="unitOfMeasure != null">#{unitOfMeasure},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="quantityProduced != null">#{quantityProduced},</if>
            <if test="quantityChanged != null">#{quantityChanged},</if>
            <if test="quantityScheduled != null">#{quantityScheduled},</if>
            <if test="clientId != null">#{clientId},</if>
            <if test="clientCode != null">#{clientCode},</if>
            <if test="clientName != null">#{clientName},</if>
            <if test="requestDate != null">#{requestDate},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="ancestors != null">#{ancestors},</if>
            <if test="status != null">#{status},</if>
            <if test="ownerCode != null and ownerCode != ''">#{ownerCode},</if>
            <if test="ownerName != null and ownerName != ''">#{ownerName},</if>
            <if test="workshopId != null">#{workshopId},</if>
            <if test="workshopName != null and workshopName != ''">#{workshopName},</if>
            <if test="oqcCheck != null">#{oqcCheck},</if>
            <if test="keyItems != null">#{keyItems},</if>
            <if test="remark != null">#{remark},</if>
            <if test="externalLinkBillNo != null">#{externalLinkBillNo},</if>
            <if test="externalLinkLineNo != null">#{externalLinkLineNo},</if>
            <if test="requirement != null">#{requirement},</if>
            <if test="productionDate != null">#{productionDate},</if>
            <if test="expirationDate != null">#{expirationDate},</if>
            <if test="shelfLife != null">#{shelfLife},</if>
            <if test="theoreticalOutput != null">#{theoreticalOutput},</if>
            <if test="brand != null">#{brand},</if>
            <if test="submitStatus != null">#{submitStatus},</if>
            <if test="pushStatus != null">#{pushStatus},</if>
            <if test="pushDate != null">#{pushDate},</if>
            <if test="skuWrap != null">#{skuWrap},</if>
            <if test="produceBatchCode != null and produceBatchCode != ''">#{produceBatchCode},</if>
            <if test="pushErpAttach != null">#{pushErpAttach},</if>
            <if test="linkType != null">#{linkType},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="batchCode != null">#{batchCode},</if>
            <if test="finishDate != null">#{finishDate},</if>
            <if test="workorderType != null">#{workorderType},</if>
            <if test="vendorId != null">#{vendorId},</if>
            <if test="vendorCode != null">#{vendorCode},</if>
            <if test="vendorName != null">#{vendorName},</if>
        </trim>
    </insert>

    <update id="updateProWorkorder" parameterType="ProWorkorder">
        update pro_workorder
        <trim prefix="SET" suffixOverrides=",">
            <if test="warehouseCode != null and warehouseCode != ''">warehouse_code = #{warehouseCode},</if>
            <if test="warehouseName != null and warehouseName != ''">warehouse_name = #{warehouseName},</if>
            <if test="workorderCode != null and workorderCode != ''">workorder_code = #{workorderCode},</if>
            <if test="workorderName != null">workorder_name = #{workorderName},</if>
            <if test="orderSource != null">order_source = #{orderSource},</if>
            <if test="sourceCode != null">source_code = #{sourceCode},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="productCode != null and productCode != ''">product_code = #{productCode},</if>
            <if test="productName != null and productName != ''">product_name = #{productName},</if>
            <if test="productSpc != null">product_spc = #{productSpc},</if>
            <if test="unitOfMeasure != null">unit_of_measure = #{unitOfMeasure},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="quantityProduced != null">quantity_produced = #{quantityProduced},</if>
            <if test="quantityChanged != null">quantity_changed = #{quantityChanged},</if>
            <if test="quantityScheduled != null">quantity_scheduled = #{quantityScheduled},</if>
            <if test="clientId != null">client_id = #{clientId},</if>
            <if test="clientCode != null">client_code = #{clientCode},</if>
            <if test="clientName != null">client_name = #{clientName},</if>
            <if test="requestDate != null">request_date = #{requestDate},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="ancestors != null">ancestors = #{ancestors},</if>
            <if test="status != null">status = #{status},</if>
            <if test="ownerCode != null and ownerCode != ''">owner_code = #{ownerCode},</if>
            <if test="ownerName != null and ownerName != ''">owner_name = #{ownerName},</if>
            <if test="workshopId != null">workshop_id = #{workshopId},</if>
            <if test="workshopName != null and workshopName != ''">workshop_name = #{workshopName},</if>
            <if test="oqcCheck != null">oqc_check = #{oqcCheck},</if>
            <if test="keyItems != null">key_items = #{keyItems},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="externalLinkBillNo != null">external_link_bill_no = #{externalLinkBillNo},</if>
            <if test="externalLinkLineNo != null">external_link_line_no = #{externalLinkLineNo},</if>
            <if test="requirement != null">requirement = #{requirement},</if>
            <if test="productionDate != null">production_date = #{productionDate},</if>
            <if test="expirationDate != null">expiration_date = #{expirationDate},</if>
            <if test="shelfLife != null">shelf_life = #{shelfLife},</if>
            <if test="theoreticalOutput != null">theoretical_output = #{theoreticalOutput},</if>
            <if test="brand != null">brand = #{brand},</if>
            <if test="submitStatus != null">submit_status = #{submitStatus},</if>
            <if test="pushStatus != null">push_status = #{pushStatus},</if>
            <if test="pushDate != null">push_date = #{pushDate},</if>
            <if test="skuWrap != null">sku_wrap = #{skuWrap},</if>
            <if test="produceBatchCode != null and produceBatchCode != ''">produce_batch_code = #{produceBatchCode},</if>
            <if test="pushErpAttach != null">push_erp_attach = #{pushErpAttach},</if>
            <if test="linkType != null">link_type = #{linkType},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="batchCode != null">batch_code = #{batchCode},</if>
            <if test="finishDate != null">finish_date = #{finishDate},</if>
            <if test="workorderType != null">workorder_type = #{workorderType},</if>
            <if test="vendorId != null">vendor_id = #{vendorId},</if>
            <if test="vendorCode != null">vendor_code = #{vendorCode},</if>
            <if test="vendorName != null">vendor_name = #{vendorName},</if>
        </trim>
        where workorder_id = #{workorderId}
    </update>

    <delete id="deleteProWorkorderByWorkorderId" parameterType="Long">
        delete from pro_workorder where workorder_id = #{workorderId}
    </delete>

    <delete id="deleteProWorkorderByWorkorderIds" parameterType="String">
        delete from pro_workorder where workorder_id in
        <foreach item="workorderId" collection="array" open="(" separator="," close=")">
            #{workorderId}
        </foreach>
    </delete>
</mapper>