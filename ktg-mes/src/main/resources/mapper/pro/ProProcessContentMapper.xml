<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.pro.mapper.ProProcessContentMapper">
    
    <resultMap type="ProProcessContent" id="ProProcessContentResult">
        <result property="contentId"    column="content_id"    />
        <result property="processId"    column="process_id"    />
        <result property="orderNum"    column="order_num"    />
        <result property="contentText"    column="content_text"    />
        <result property="device"    column="device"    />
        <result property="material"    column="material"    />
        <result property="docUrl"    column="doc_url"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectProProcessContentVo">
        select content_id, process_id, order_num, content_text, device, material, doc_url, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from pro_process_content
    </sql>

    <select id="selectProProcessContentList" parameterType="ProProcessContent" resultMap="ProProcessContentResult">
        <include refid="selectProProcessContentVo"/>
        <where>  
            <if test="processId != null "> and process_id = #{processId}</if>
            <if test="orderNum != null "> and order_num = #{orderNum}</if>
            <if test="contentText != null  and contentText != ''"> and content_text = #{contentText}</if>
            <if test="device != null  and device != ''"> and device = #{device}</if>
            <if test="material != null  and material != ''"> and material = #{material}</if>
            <if test="docUrl != null  and docUrl != ''"> and doc_url = #{docUrl}</if>
        </where>
        order by order_num asc
    </select>
    
    <select id="selectProProcessContentByContentId" parameterType="Long" resultMap="ProProcessContentResult">
        <include refid="selectProProcessContentVo"/>
        where content_id = #{contentId}
    </select>

    <insert id="insertProProcessContent" parameterType="ProProcessContent" useGeneratedKeys="true" keyProperty="contentId">
        insert into pro_process_content
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="processId != null">process_id,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="contentText != null">content_text,</if>
            <if test="device != null">device,</if>
            <if test="material != null">material,</if>
            <if test="docUrl != null">doc_url,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="processId != null">#{processId},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="contentText != null">#{contentText},</if>
            <if test="device != null">#{device},</if>
            <if test="material != null">#{material},</if>
            <if test="docUrl != null">#{docUrl},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateProProcessContent" parameterType="ProProcessContent">
        update pro_process_content
        <trim prefix="SET" suffixOverrides=",">
            <if test="processId != null">process_id = #{processId},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="contentText != null">content_text = #{contentText},</if>
            <if test="device != null">device = #{device},</if>
            <if test="material != null">material = #{material},</if>
            <if test="docUrl != null">doc_url = #{docUrl},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where content_id = #{contentId}
    </update>

    <delete id="deleteProProcessContentByContentId" parameterType="Long">
        delete from pro_process_content where content_id = #{contentId}
    </delete>

    <delete id="deleteProProcessContentByContentIds" parameterType="String">
        delete from pro_process_content where content_id in 
        <foreach item="contentId" collection="array" open="(" separator="," close=")">
            #{contentId}
        </foreach>
    </delete>
</mapper>