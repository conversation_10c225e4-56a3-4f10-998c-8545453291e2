<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.pro.mapper.ProTaskExceptionMapper">

    <resultMap type="ProTaskException" id="ProTaskExceptionResult">
        <result property="exceptionId"    column="exception_id"    />
        <result property="taskId"    column="task_id"    />
        <result property="taskCode"    column="task_code"    />
        <result property="workorderId"    column="workorder_id"    />
        <result property="workorderCode"    column="workorder_code"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemName"    column="item_name"    />
        <result property="exceptionReason"    column="exception_reason"    />
        <result property="exceptionReasonDesc"    column="exception_reason_desc"    />
        <result property="exceptionQuantity"    column="exception_quantity"    />
        <result property="registrant"    column="registrant"    />
        <result property="registrantName"    column="registrant_name"    />
        <result property="registerTime"    column="register_time"    />
        <result property="attachmentUrl"    column="attachment_url"    />
        <result property="attachmentName"    column="attachment_name"    />
        <result property="attachmentSize"    column="attachment_size"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectProTaskExceptionVo">
        select exception_id, task_id, task_code, workorder_id, workorder_code, item_code, item_name, exception_reason, exception_reason_desc, exception_quantity, registrant, registrant_name, register_time, attachment_url, attachment_name, attachment_size, status, remark, create_by, create_time, update_by, update_time from pro_task_exception
    </sql>

    <select id="selectProTaskExceptionList" parameterType="ProTaskException" resultMap="ProTaskExceptionResult">
        <include refid="selectProTaskExceptionVo"/>
        <where>
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="taskCode != null  and taskCode != ''"> and task_code = #{taskCode}</if>
            <if test="workorderId != null "> and workorder_id = #{workorderId}</if>
            <if test="workorderCode != null  and workorderCode != ''"> and workorder_code = #{workorderCode}</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code = #{itemCode}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="exceptionReason != null  and exceptionReason != ''"> and exception_reason = #{exceptionReason}</if>
            <if test="exceptionReasonDesc != null  and exceptionReasonDesc != ''"> and exception_reason_desc = #{exceptionReasonDesc}</if>
            <if test="exceptionQuantity != null "> and exception_quantity = #{exceptionQuantity}</if>
            <if test="registrant != null  and registrant != ''"> and registrant = #{registrant}</if>
            <if test="registrantName != null  and registrantName != ''"> and registrant_name like concat('%', #{registrantName}, '%')</if>
            <if test="registerTime != null "> and register_time = #{registerTime}</if>
            <if test="attachmentUrl != null  and attachmentUrl != ''"> and attachment_url = #{attachmentUrl}</if>
            <if test="attachmentName != null  and attachmentName != ''"> and attachment_name like concat('%', #{attachmentName}, '%')</if>
            <if test="attachmentSize != null "> and attachment_size = #{attachmentSize}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectProTaskExceptionByExceptionId" parameterType="Long" resultMap="ProTaskExceptionResult">
        <include refid="selectProTaskExceptionVo"/>
        where exception_id = #{exceptionId}
    </select>

    <insert id="insertProTaskException" parameterType="ProTaskException" useGeneratedKeys="true" keyProperty="exceptionId">
        insert into pro_task_exception
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="taskCode != null and taskCode != ''">task_code,</if>
            <if test="workorderId != null">workorder_id,</if>
            <if test="workorderCode != null and workorderCode != ''">workorder_code,</if>
            <if test="itemCode != null and itemCode != ''">item_code,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="exceptionReason != null and exceptionReason != ''">exception_reason,</if>
            <if test="exceptionReasonDesc != null">exception_reason_desc,</if>
            <if test="exceptionQuantity != null">exception_quantity,</if>
            <if test="registrant != null">registrant,</if>
            <if test="registrantName != null">registrant_name,</if>
            <if test="registerTime != null">register_time,</if>
            <if test="attachmentUrl != null">attachment_url,</if>
            <if test="attachmentName != null">attachment_name,</if>
            <if test="attachmentSize != null">attachment_size,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="taskCode != null and taskCode != ''">#{taskCode},</if>
            <if test="workorderId != null">#{workorderId},</if>
            <if test="workorderCode != null and workorderCode != ''">#{workorderCode},</if>
            <if test="itemCode != null and itemCode != ''">#{itemCode},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="exceptionReason != null and exceptionReason != ''">#{exceptionReason},</if>
            <if test="exceptionReasonDesc != null">#{exceptionReasonDesc},</if>
            <if test="exceptionQuantity != null">#{exceptionQuantity},</if>
            <if test="registrant != null">#{registrant},</if>
            <if test="registrantName != null">#{registrantName},</if>
            <if test="registerTime != null">#{registerTime},</if>
            <if test="attachmentUrl != null">#{attachmentUrl},</if>
            <if test="attachmentName != null">#{attachmentName},</if>
            <if test="attachmentSize != null">#{attachmentSize},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateProTaskException" parameterType="ProTaskException">
        update pro_task_exception
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="taskCode != null and taskCode != ''">task_code = #{taskCode},</if>
            <if test="workorderId != null">workorder_id = #{workorderId},</if>
            <if test="workorderCode != null and workorderCode != ''">workorder_code = #{workorderCode},</if>
            <if test="itemCode != null and itemCode != ''">item_code = #{itemCode},</if>
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="exceptionReason != null and exceptionReason != ''">exception_reason = #{exceptionReason},</if>
            <if test="exceptionReasonDesc != null">exception_reason_desc = #{exceptionReasonDesc},</if>
            <if test="exceptionQuantity != null">exception_quantity = #{exceptionQuantity},</if>
            <if test="registrant != null">registrant = #{registrant},</if>
            <if test="registrantName != null">registrant_name = #{registrantName},</if>
            <if test="registerTime != null">register_time = #{registerTime},</if>
            <if test="attachmentUrl != null">attachment_url = #{attachmentUrl},</if>
            <if test="attachmentName != null">attachment_name = #{attachmentName},</if>
            <if test="attachmentSize != null">attachment_size = #{attachmentSize},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where exception_id = #{exceptionId}
    </update>

    <delete id="deleteProTaskExceptionByExceptionId" parameterType="Long">
        delete from pro_task_exception where exception_id = #{exceptionId}
    </delete>

    <delete id="deleteProTaskExceptionByExceptionIds" parameterType="String">
        delete from pro_task_exception where exception_id in
        <foreach item="exceptionId" collection="array" open="(" separator="," close=")">
            #{exceptionId}
        </foreach>
    </delete>
</mapper>
