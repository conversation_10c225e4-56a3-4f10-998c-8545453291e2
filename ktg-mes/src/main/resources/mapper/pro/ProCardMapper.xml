<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.pro.mapper.ProCardMapper">
    
    <resultMap type="ProCard" id="ProCardResult">
        <result property="cardId"    column="card_id"    />
        <result property="cardCode"    column="card_code"    />
        <result property="workorderId"    column="workorder_id"    />
        <result property="workorderCode"    column="workorder_code"    />
        <result property="workorderName"    column="workorder_name"    />
        <result property="batchCode"    column="batch_code"    />
        <result property="itemId"    column="item_id"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemName"    column="item_name"    />
        <result property="specification"    column="specification"    />
        <result property="unitOfMeasure"    column="unit_of_measure"    />
        <result property="barcodeUrl"    column="barcode_url"    />
        <result property="quantityTransfered"    column="quantity_transfered"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="inputTime"    column="input_time"    />
    </resultMap>

    <sql id="selectProCardVo">
        select card_id, card_code, workorder_id, workorder_code, workorder_name, batch_code, item_id, item_code, item_name, specification, unit_of_measure, barcode_url, quantity_transfered, status, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from pro_card
    </sql>

    <select id="selectProCardList" parameterType="ProCard" resultMap="ProCardResult">
        <include refid="selectProCardVo"/>
        <where>  
            <if test="cardCode != null  and cardCode != ''"> and card_code = #{cardCode}</if>
            <if test="workorderId != null "> and workorder_id = #{workorderId}</if>
            <if test="workorderCode != null  and workorderCode != ''"> and workorder_code = #{workorderCode}</if>
            <if test="workorderName != null  and workorderName != ''"> and workorder_name like concat('%', #{workorderName}, '%')</if>
            <if test="batchCode != null  and batchCode != ''"> and batch_code = #{batchCode}</if>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code = #{itemCode}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="specification != null  and specification != ''"> and specification = #{specification}</if>
            <if test="unitOfMeasure != null  and unitOfMeasure != ''"> and unit_of_measure = #{unitOfMeasure}</if>
            <if test="barcodeUrl != null  and barcodeUrl != ''"> and barcode_url = #{barcodeUrl}</if>
            <if test="quantityTransfered != null "> and quantity_transfered = #{quantityTransfered}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="attr1 != null  and attr1 != ''"> and attr1 = #{attr1}</if>
            <if test="attr2 != null  and attr2 != ''"> and attr2 = #{attr2}</if>
            <if test="attr3 != null "> and attr3 = #{attr3}</if>
            <if test="attr4 != null "> and attr4 = #{attr4}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectProCardByCardId" parameterType="Long" resultMap="ProCardResult">
        <include refid="selectProCardVo"/>
        where card_id = #{cardId}
    </select>

    <select id="getStationList" parameterType="ProCard" resultMap="ProCardResult">
        select pc.card_id,pc.card_code,pc.workorder_id,pc.workorder_code,pc.workorder_name,pc.item_id,pc.item_code,pc.item_name,pc.specification, pc.unit_of_measure,pc.quantity_transfered,
               pcp.record_id,pcp.workstation_id,pcp.input_time
        from pro_card pc
                 left join pro_card_process pcp
                           on pc.card_id = pcp.card_id
        where pc.workorder_id = #{workorderId}
          and pcp.workstation_id = #{workstationId}
        order by pcp.input_time desc
    </select>
    <select id="checkCardCodeUnique" resultType="com.ktg.mes.pro.domain.ProCard" resultMap="ProCardResult">
        <include refid="selectProCardVo"/>
        where card_code = #{cardCode}
        limit 1
    </select>


    <insert id="insertProCard" parameterType="ProCard" useGeneratedKeys="true" keyProperty="cardId">
        insert into pro_card
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cardCode != null">card_code,</if>
            <if test="workorderId != null">workorder_id,</if>
            <if test="workorderCode != null">workorder_code,</if>
            <if test="workorderName != null">workorder_name,</if>
            <if test="batchCode != null">batch_code,</if>
            <if test="itemId != null">item_id,</if>
            <if test="itemCode != null and itemCode != ''">item_code,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="specification != null">specification,</if>
            <if test="unitOfMeasure != null and unitOfMeasure != ''">unit_of_measure,</if>
            <if test="barcodeUrl != null">barcode_url,</if>
            <if test="quantityTransfered != null">quantity_transfered,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="cardCode != null">#{cardCode},</if>
            <if test="workorderId != null">#{workorderId},</if>
            <if test="workorderCode != null">#{workorderCode},</if>
            <if test="workorderName != null">#{workorderName},</if>
            <if test="batchCode != null">#{batchCode},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="itemCode != null and itemCode != ''">#{itemCode},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="specification != null">#{specification},</if>
            <if test="unitOfMeasure != null and unitOfMeasure != ''">#{unitOfMeasure},</if>
            <if test="barcodeUrl != null">#{barcodeUrl},</if>
            <if test="quantityTransfered != null">#{quantityTransfered},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateProCard" parameterType="ProCard">
        update pro_card
        <trim prefix="SET" suffixOverrides=",">
            <if test="cardCode != null">card_code = #{cardCode},</if>
            <if test="workorderId != null">workorder_id = #{workorderId},</if>
            <if test="workorderCode != null">workorder_code = #{workorderCode},</if>
            <if test="workorderName != null">workorder_name = #{workorderName},</if>
            <if test="batchCode != null">batch_code = #{batchCode},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="itemCode != null and itemCode != ''">item_code = #{itemCode},</if>
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="unitOfMeasure != null and unitOfMeasure != ''">unit_of_measure = #{unitOfMeasure},</if>
            <if test="barcodeUrl != null">barcode_url = #{barcodeUrl},</if>
            <if test="quantityTransfered != null">quantity_transfered = #{quantityTransfered},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where card_id = #{cardId}
    </update>

    <delete id="deleteProCardByCardId" parameterType="Long">
        delete from pro_card where card_id = #{cardId}
    </delete>

    <delete id="deleteProCardByCardIds" parameterType="String">
        delete from pro_card where card_id in 
        <foreach item="cardId" collection="array" open="(" separator="," close=")">
            #{cardId}
        </foreach>
    </delete>
</mapper>