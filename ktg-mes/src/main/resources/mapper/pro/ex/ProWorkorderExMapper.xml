<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.pro.mapper.ProWorkorderMapper">

    <select id="selectProWorkorderByWorkorderIds" parameterType="list" resultMap="ProWorkorderResult">
        <include refid="selectProWorkorderVo"/>
        where workorder_id in
        <if test="workorderIdList !=null and workorderIdList.size>0">
            <foreach collection="workorderIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectProWorkorderListByParentId" parameterType="Long" resultMap="ProWorkorderResult">
        <include refid="selectProWorkorderVo"/>
        where parent_id = #{parentId}
    </select>

    <select id="checkWorkorderCodeUnique" parameterType="ProWorkorder" resultMap="ProWorkorderResult">
        <include refid="selectProWorkorderVo"/>
        where workorder_code = #{workorderCode} limit 1
    </select>


    <select id="proWorkorderPage" parameterType="ProWorkorderListReqVo" resultMap="ProWorkorderResult">
        <include refid="selectProWorkorderVo"/>
        <where>
            ((submit_status = 'Y' and order_source = 'ERP') or submit_status is null)
            <if test="workorderCode != null  and workorderCode != ''"> and workorder_code = #{workorderCode}</if>
            <if test="workshopId != null "> and workshop_id = #{workshopId}</if>
            <if test="ownerCode != null  and ownerCode != ''"> and owner_code = #{ownerCode}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="productCode != null  and productCode != ''"> and product_code = #{productCode}</if>
            <if test="produceBatchCode != null  and produceBatchCode != ''"> and produce_batch_code like concat('%', #{produceBatchCode}, '%')</if>
            <if test="productName != null  and productName != ''"> and product_name like concat('%', #{productName}, '%')</if>
            <if test="externalLinkBillNo != null  and externalLinkBillNo != ''"> and external_link_bill_no like concat('%', #{externalLinkBillNo}, '%')</if>
            <if test="orderSource != null  and orderSource != ''"> and order_source = #{orderSource}</if>
            <if test="requirement != null  and requirement != ''"> and requirement like concat('%', #{requirement}, '%')</if>
            <if test="requestDateBegin != null and requestDateEnd != null"> and request_date between #{requestDateBegin} and #{requestDateEnd}</if>
            <if test="multipleStatus != null and multipleStatus != ''">
                and status in
                <foreach item="item" collection="statusList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <choose>
                <when test="pushStatus == &quot;Y&quot;">
                    AND push_status = 'Y'
                </when>
                <when test="pushStatus == &quot;N&quot;">
                    AND (push_status = 'N' or push_status is null)
                </when>
            </choose>
        </where>
        order by workorder_id desc
    </select>


    <select id="proWorkorderErpPage" parameterType="ProWorkorderErpPageReqVo" resultMap="ProWorkorderResult">
        <include refid="selectProWorkorderVo"/>
        <where>
            ((order_source = 'SELF' and push_status = 'Y' ) or (order_source = 'ERP' AND submit_status != 'D' ))
            <if test="workorderCode != null  and workorderCode != ''"> and workorder_code  like concat('%', #{workorderCode}, '%')</if>
            <if test="workshopId != null "> and workshop_id = #{workshopId}</if>
            <if test="productCode != null  and productCode != ''"> and product_code like concat('%', #{productCode}, '%')</if>
            <if test="productName != null  and productName != ''"> and product_name like concat('%', #{productName}, '%')</if>
            <if test="brand != null  and brand != ''"> and brand like concat('%', #{brand}, '%')</if>
            <if test="externalLinkBillNo != null  and externalLinkBillNo != ''"> and external_link_bill_no like concat('%', #{externalLinkBillNo}, '%')</if>
            <if test="requestDateBegin != null and requestDateEnd != null"> and request_date between #{requestDateBegin} and #{requestDateEnd}</if>
            <if test="pushDateBegin != null and pushDateEnd != null"> and push_date between #{pushDateBegin} and #{pushDateEnd}</if>
            <if test="ownerCode != null  and ownerCode != ''">and owner_code = #{ownerCode}</if>
            <if test="ownerCodeList != null  and ownerCodeList != ''">
                and owner_code in
                <foreach item="item" collection="ownerCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 状态条件合并 -->
            <choose>
                <when test="status == 'WAIT_PUSH'">
                    AND submit_status = 'N'
                </when>
                <when test="status == 'WORKING'">
                     AND status != 'FINISHED'
                </when>
                <when test="status == 'FINISHED'">
                    AND status = 'FINISHED'
                </when>
            </choose>

        </where>
        order by create_time desc
    </select>

    <select id="selectProWorkorderByWorkorderCode" parameterType="String" resultMap="ProWorkorderResult">
        <include refid="selectProWorkorderVo"/>
        where workorder_code = #{workorderCode}
    </select>

    <select id="selectByProductSkuList" parameterType="list" resultMap="ProWorkorderResult">
        <include refid="selectProWorkorderVo"/>
        where product_code in
        <if test="productSkuList != null and productSkuList.size > 0">
            <foreach collection="productSkuList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectByCondition" resultMap="ProWorkorderResult">
        <include refid="selectProWorkorderVo"/>
        <where>
            <if test="proWorkorderCodeList != null and proWorkorderCodeList.size > 0">
                and workorder_code in
                <foreach collection="proWorkorderCodeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="proWorkorderStatus != null and proWorkorderStatus != ''">
                and status = #{proWorkorderStatus}
            </if>
            <if test="finishDateBegin != null and finishDateEnd != null">
                and finish_date &gt;= FROM_UNIXTIME(#{finishDateBegin}/1000)
                and finish_date &lt;= FROM_UNIXTIME(#{finishDateEnd}/1000)
            </if>
            <if test="productSkuList != null and productSkuList.size > 0">
                and product_code in
                <foreach collection="productSkuList" item="sku" open="(" close=")" separator=",">
                    #{sku}
                </foreach>
            </if>
            <if test="productSkuLotWorkorderList != null and productSkuLotWorkorderList.size > 0">
                and workorder_id in
                <foreach collection="productSkuLotWorkorderList" item="workorderId" open="(" close=")" separator=",">
                    #{workorderId}
                </foreach>
            </if>
        </where>
        order by create_time desc
    </select>

</mapper>