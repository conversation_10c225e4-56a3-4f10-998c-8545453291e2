<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.pro.mapper.ProTaskExceptionMapper">

    <!-- 统计查询ResultMap -->
    <resultMap type="ProTaskException" id="ProTaskExceptionSummaryResult">
        <result property="taskId"    column="task_id"    />
        <result property="taskCode"    column="task_code"    />
        <result property="workorderId"    column="workorder_id"    />
        <result property="workorderCode"    column="workorder_code"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemName"    column="item_name"    />
        <result property="exceptionReason"    column="exception_reason"    />
        <result property="exceptionReasonDesc"    column="exception_reason_desc"    />
        <result property="exceptionQuantity"    column="exception_quantity"    />
        <result property="registerTime"    column="first_register_time"    />
        <result property="registrantName"    column="registrant_names"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="exception_count"    />
    </resultMap>


    <!-- 根据任务ID查询生产异常标记列表 -->
    <select id="selectProTaskExceptionByTaskId" parameterType="Long" resultMap="ProTaskExceptionResult">
        <include refid="selectProTaskExceptionVo"/>
        where task_id = #{taskId}
          and status = '0'
        order by create_time desc
    </select>

    <!-- 根据任务ID列表查询生产异常标记列表 -->
    <select id="selectProTaskExceptionByTaskIdList" resultMap="ProTaskExceptionResult">
        <include refid="selectProTaskExceptionVo"/>
        where task_id in
        <foreach item="taskId" collection="taskIdList" open="(" separator="," close=")">
            #{taskId}
        </foreach>
        and status = '0'
        order by task_id, create_time desc
    </select>

    <!-- 根据工单ID查询生产异常标记列表 -->
    <select id="selectProTaskExceptionByWorkorderId" parameterType="Long" resultMap="ProTaskExceptionResult">
        <include refid="selectProTaskExceptionVo"/>
        where workorder_id = #{workorderId}
          and status = '0'
        order by task_id, create_time desc
    </select>

    <!-- 查询异常原因合并统计信息 -->
    <select id="selectExceptionSummaryByTaskId" parameterType="Long" resultMap="ProTaskExceptionSummaryResult">
        select
            task_id,
            task_code,
            workorder_id,
            workorder_code,
            item_code,
            item_name,
            exception_reason,
            exception_reason_desc,
            sum(exception_quantity) as exception_quantity,
            count(*) as exception_count,
            min(register_time) as first_register_time,
            max(register_time) as last_register_time,
            group_concat(distinct registrant_name separator ',') as registrant_names,
            '0' as status
        from pro_task_exception
        where task_id = #{taskId}
          and status = '0'
        group by task_id, task_code, workorder_id, workorder_code, item_code, item_name,
                 exception_reason, exception_reason_desc
        order by sum(exception_quantity) desc, exception_reason
    </select>

    <!-- 扩展查询：根据物料编码查询异常记录 -->
    <select id="selectProTaskExceptionByItemCode" parameterType="String" resultMap="ProTaskExceptionResult">
        <include refid="selectProTaskExceptionVo"/>
        where item_code = #{itemCode}
          and status = '0'
        order by create_time desc
    </select>

    <!-- 扩展查询：根据异常原因查询异常记录 -->
    <select id="selectProTaskExceptionByReason" parameterType="String" resultMap="ProTaskExceptionResult">
        <include refid="selectProTaskExceptionVo"/>
        where exception_reason = #{exceptionReason}
          and status = '0'
        order by create_time desc
    </select>

    <!-- 扩展查询：根据时间范围查询异常记录 -->
    <select id="selectProTaskExceptionByTimeRange" resultMap="ProTaskExceptionResult">
        <include refid="selectProTaskExceptionVo"/>
        where status = '0'
        <if test="startTime != null">
            and register_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            and register_time &lt;= #{endTime}
        </if>
        <if test="taskId != null">
            and task_id = #{taskId}
        </if>
        <if test="workorderId != null">
            and workorder_id = #{workorderId}
        </if>
        order by register_time desc
    </select>

    <!-- 扩展查询：统计各异常原因的数量 -->
    <select id="selectExceptionReasonStatistics" resultType="java.util.Map">
        select
            exception_reason as reason,
            exception_reason_desc as reasonDesc,
            count(*) as count,
            sum(exception_quantity) as totalQuantity,
            avg(exception_quantity) as avgQuantity
        from pro_task_exception
        where status = '0'
        <if test="taskId != null">
            and task_id = #{taskId}
        </if>
        <if test="workorderId != null">
            and workorder_id = #{workorderId}
        </if>
        <if test="startTime != null">
            and register_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            and register_time &lt;= #{endTime}
        </if>
        group by exception_reason, exception_reason_desc
        order by totalQuantity desc
    </select>

</mapper>
