<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.pro.mapper.ProExceptionDeductionMapper">

    <select id="selectByWorkorderIdAndItemCode" resultMap="ProExceptionDeductionResult">
        <include refid="selectProExceptionDeductionVo"/>
        where workorder_id = #{workorderId} and item_code = #{itemCode}
    </select>

    <select id="selectByWorkorderId" resultMap="ProExceptionDeductionResult">
        <include refid="selectProExceptionDeductionVo"/>
        where workorder_id = #{workorderId}
        order by create_time desc
    </select>

    <select id="selectByStatus" resultMap="ProExceptionDeductionResult">
        <include refid="selectProExceptionDeductionVo"/>
        where status = #{status}
        order by create_time desc
    </select>

    <select id="selectByIssueId" resultMap="ProExceptionDeductionResult">
        <include refid="selectProExceptionDeductionVo"/>
        where issue_id = #{issueId}
        order by create_time desc
    </select>

    <select id="checkDeductionCodeUnique" parameterType="ProExceptionDeduction" resultMap="ProExceptionDeductionResult">
        <include refid="selectProExceptionDeductionVo"/>
        where deduction_code = #{deductionCode}
        <if test="deductionId != null"> and deduction_id != #{deductionId}</if>
        limit 1
    </select>

</mapper>