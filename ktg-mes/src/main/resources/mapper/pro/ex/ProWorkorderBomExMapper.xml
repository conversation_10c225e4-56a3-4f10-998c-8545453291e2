<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.pro.mapper.ProWorkorderBomMapper">

    <delete id="deleteProWorkorderBomByWorkorderId" parameterType="Long">
        delete from pro_workorder_bom where workorder_id = #{workorderId}
    </delete>

    <select id="selectProWorkorderBomListByWorkorderIdList" resultMap="ProWorkorderBomResult">
        <include refid="selectProWorkorderBomVo"/>
        <where>
            <if test="workorderIdList != null and workorderIdList.size > 0">
                workorder_id in
                <foreach collection="workorderIdList" item="workorderId" open="(" close=")" separator=",">
                    #{workorderId}
                </foreach>
            </if>
        </where>
        order by workorder_id, line_id
    </select>
</mapper>