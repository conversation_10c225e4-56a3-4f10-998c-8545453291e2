<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.pro.mapper.ProTaskBomMapper">
    <select id="selectProTaskBomListByWorkorderId" resultMap="ProTaskBomResult">
        <include refid="selectProTaskBomVo"/>
        where workorder_id in
        <foreach item="workorderId" collection="workorderIdList" open="(" separator="," close=")">
            #{workorderId}
        </foreach>
    </select>
</mapper>