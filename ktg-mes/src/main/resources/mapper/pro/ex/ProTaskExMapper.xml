<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.pro.mapper.ProTaskMapper">

    <select id="proTaskPage" parameterType="ProTaskListReqVo" resultMap="ProTaskResult">
        <include refid="selectProTaskVo"/>
        <where>
            <if test="workorderCode != null  and workorderCode != ''"> and workorder_code like concat('%', #{workorderCode}, '%')</if>
            <if test="workorderId != null  and workorderId != ''"> and workorder_id = #{workorderId}</if>
            <if test="taskCode != null  and taskCode != ''"> and task_code like concat('%', #{taskCode}, '%')</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code like concat('%', #{itemCode}, '%')</if>
            <if test="workshopId != null"> and workshop_id = #{workshopId}</if>
            <if test="produceBatchCode != null  and produceBatchCode != ''"> and produce_batch_code like concat('%', #{produceBatchCode}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="wmsOrderNo != null  and wmsOrderNo != ''"> and wms_order_no like concat('%', #{wmsOrderNo}, '%')</if>
            <if test="routeId != null  and routeId != ''"> and route_id = #{routeId}</if>
            <if test="processId != null  and processId != ''"> and process_id = #{processId}</if>
        </where>
        order by create_time desc
    </select>


    <select id="selectProTaskListByWorkorderId" resultMap="ProTaskResult">
        <include refid="selectProTaskVo"/>
        where workorder_id in
        <foreach item="workorderId" collection="workorderIdList" open="(" separator="," close=")">
            #{workorderId}
        </foreach>
    </select>
    <select id="selectProTaskByTaskIdList" resultMap="ProTaskResult">
        <include refid="selectProTaskVo"/>
        where task_id in
        <foreach item="taskId" collection="taskIdList" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </select>

    <select id="selectProTaskListByWorkorderIdAndStatus" parameterType="TaskListQueryReqVo"  resultMap="ProTaskResult">
        <include refid="selectProTaskVo"/>
        where workorder_id =#{workorderId}
        <if test="status != null  and status != ''"> and status = #{status}</if>
        <if test="statusList != null and statusList != ''">
            and status in
            <foreach item="item" collection="statusList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="excludeStatus != null  and excludeStatus != ''"> and status != #{excludeStatus}</if>
        <if test="excludeStatusList != null and excludeStatusList != ''">
            and status not in
            <foreach item="item" collection="excludeStatusList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>