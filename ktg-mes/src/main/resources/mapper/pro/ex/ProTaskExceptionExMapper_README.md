# ProTaskExceptionExMapper.xml 说明文档

## 概述
此文件实现了 `ProTaskExceptionMapper` 接口中未在主 `ProTaskExceptionMapper.xml` 文件中实现的方法，以及一些扩展的查询方法。

## 文件位置
`ktg-mes/src/main/resources/mapper/pro/ex/ProTaskExceptionExMapper.xml`

## 实现的方法

### 1. 必需方法（Mapper接口中定义但主XML未实现）

#### 1.1 selectProTaskExceptionByTaskId
- **功能**: 根据任务ID查询生产异常标记列表
- **参数**: `Long taskId` - 生产任务ID
- **返回**: `List<ProTaskException>` - 异常记录列表
- **特点**: 只查询正常状态的记录，按创建时间倒序排列

#### 1.2 selectProTaskExceptionByTaskIdList
- **功能**: 根据任务ID列表批量查询生产异常标记列表
- **参数**: `List<Long> taskIdList` - 生产任务ID列表
- **返回**: `List<ProTaskException>` - 异常记录列表
- **特点**: 支持批量查询，按任务ID和创建时间排序

#### 1.3 selectProTaskExceptionByWorkorderId
- **功能**: 根据工单ID查询生产异常标记列表
- **参数**: `Long workorderId` - 生产工单ID
- **返回**: `List<ProTaskException>` - 异常记录列表
- **特点**: 查询指定工单下所有任务的异常记录

#### 1.4 selectExceptionSummaryByTaskId
- **功能**: 查询异常原因合并统计信息
- **参数**: `Long taskId` - 生产任务ID
- **返回**: `List<ProTaskException>` - 统计结果列表
- **特点**: 
  - 按异常原因分组统计
  - 合并相同异常原因的数量
  - 提供统计信息（总数量、记录数、时间范围等）
  - 按异常数量倒序排列

### 2. 扩展方法（额外提供的实用查询）

#### 2.1 selectProTaskExceptionByItemCode
- **功能**: 根据物料编码查询异常记录
- **参数**: `String itemCode` - 物料编码
- **返回**: `List<ProTaskException>` - 异常记录列表

#### 2.2 selectProTaskExceptionByReason
- **功能**: 根据异常原因查询异常记录
- **参数**: `String exceptionReason` - 异常原因代码
- **返回**: `List<ProTaskException>` - 异常记录列表

#### 2.3 selectProTaskExceptionByTimeRange
- **功能**: 根据时间范围查询异常记录
- **参数**: 
  - `Date startTime` - 开始时间（可选）
  - `Date endTime` - 结束时间（可选）
  - `Long taskId` - 任务ID（可选）
  - `Long workorderId` - 工单ID（可选）
- **返回**: `List<ProTaskException>` - 异常记录列表

#### 2.4 selectExceptionReasonStatistics
- **功能**: 统计各异常原因的数量和数量信息
- **参数**: 支持多个可选过滤条件
- **返回**: `List<Map<String, Object>>` - 统计结果
- **返回字段**:
  - `reason`: 异常原因代码
  - `reasonDesc`: 异常原因描述
  - `count`: 记录数量
  - `totalQuantity`: 总异常数量
  - `avgQuantity`: 平均异常数量

## ResultMap 说明

### 1. ProTaskExceptionResult
标准的查询结果映射，包含所有字段的完整映射。

### 2. ProTaskExceptionSummaryResult
专门用于统计查询的结果映射，将统计字段映射到合适的属性：
- `exception_count` → `remark` (借用备注字段存储记录数)
- `first_register_time` → `registerTime` (首次登记时间)
- `registrant_names` → `registrantName` (登记人列表)

## 使用示例

```java
// 1. 查询指定任务的异常记录
List<ProTaskException> exceptions = mapper.selectProTaskExceptionByTaskId(taskId);

// 2. 批量查询多个任务的异常记录
List<ProTaskException> batchExceptions = mapper.selectProTaskExceptionByTaskIdList(taskIdList);

// 3. 查询工单下所有异常记录
List<ProTaskException> workorderExceptions = mapper.selectProTaskExceptionByWorkorderId(workorderId);

// 4. 获取异常统计信息
List<ProTaskException> summary = mapper.selectExceptionSummaryByTaskId(taskId);

// 5. 根据物料查询异常
List<ProTaskException> itemExceptions = mapper.selectProTaskExceptionByItemCode("ITEM001");
```

## 注意事项

1. **状态过滤**: 所有查询都默认过滤掉已删除的记录（status = '0'）
2. **排序规则**: 大部分查询按创建时间倒序排列，统计查询按数量倒序
3. **性能考虑**: 建议在相关字段上创建索引以提高查询性能
4. **扩展性**: 可以根据业务需要继续添加新的查询方法

## 数据库索引建议

```sql
-- 提高查询性能的索引建议
CREATE INDEX idx_pro_task_exception_task_id ON pro_task_exception(task_id, status);
CREATE INDEX idx_pro_task_exception_workorder_id ON pro_task_exception(workorder_id, status);
CREATE INDEX idx_pro_task_exception_item_code ON pro_task_exception(item_code, status);
CREATE INDEX idx_pro_task_exception_reason ON pro_task_exception(exception_reason, status);
CREATE INDEX idx_pro_task_exception_register_time ON pro_task_exception(register_time, status);
```
