<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.pro.mapper.ProExceptionDeductionMapper">

    <resultMap type="ProExceptionDeduction" id="ProExceptionDeductionResult">
        <result property="deductionId"    column="deduction_id"    />
        <result property="deductionCode"    column="deduction_code"    />
        <result property="workorderId"    column="workorder_id"    />
        <result property="workorderCode"    column="workorder_code"    />
        <result property="workorderName"    column="workorder_name"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemName"    column="item_name"    />
        <result property="specification"    column="specification"    />
        <result property="unitOfMeasure"    column="unit_of_measure"    />
        <result property="totalDeductionQuantity"    column="total_deduction_quantity"    />
        <result property="status"    column="status"    />
        <result property="statusDesc"    column="status_desc"    />
        <result property="workshopId"    column="workshop_id"    />
        <result property="workshopName"    column="workshop_name"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="ownerCode"    column="owner_code"    />
        <result property="ownerName"    column="owner_name"    />
        <result property="issueId"    column="issue_id"    />
        <result property="issueCode"    column="issue_code"    />
        <result property="wmsAdjustOrderNo"    column="wms_adjust_order_no"    />
        <result property="issueTime"    column="issue_time"    />
        <result property="pushTime"    column="push_time"    />
        <result property="completeTime"    column="complete_time"    />
        <result property="closeTime"    column="close_time"    />
        <result property="closeReason"    column="close_reason"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectProExceptionDeductionVo">
        select deduction_id, deduction_code, workorder_id, workorder_code, workorder_name, item_code, item_name, specification, unit_of_measure, total_deduction_quantity, status, status_desc, workshop_id, workshop_name, warehouse_code, warehouse_name, owner_code, owner_name, issue_id, issue_code, wms_adjust_order_no, issue_time, push_time, complete_time, close_time, close_reason, remark, attr1, attr2, attr3, attr4, create_by, create_time, update_by, update_time from pro_exception_deduction
    </sql>

    <select id="selectProExceptionDeductionList" parameterType="ProExceptionDeduction" resultMap="ProExceptionDeductionResult">
        <include refid="selectProExceptionDeductionVo"/>
        <where>
            <if test="deductionCode != null  and deductionCode != ''"> and deduction_code = #{deductionCode}</if>
            <if test="workorderId != null "> and workorder_id = #{workorderId}</if>
            <if test="workorderCode != null  and workorderCode != ''"> and workorder_code = #{workorderCode}</if>
            <if test="workorderName != null  and workorderName != ''"> and workorder_name like concat('%', #{workorderName}, '%')</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code = #{itemCode}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="specification != null  and specification != ''"> and specification = #{specification}</if>
            <if test="unitOfMeasure != null  and unitOfMeasure != ''"> and unit_of_measure = #{unitOfMeasure}</if>
            <if test="deductionQuantity != null "> and deduction_quantity = #{deductionQuantity}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="statusDesc != null  and statusDesc != ''"> and status_desc = #{statusDesc}</if>
            <if test="workshopId != null "> and workshop_id = #{workshopId}</if>
            <if test="workshopName != null  and workshopName != ''"> and workshop_name like concat('%', #{workshopName}, '%')</if>
            <if test="warehouseCode != null  and warehouseCode != ''"> and warehouse_code = #{warehouseCode}</if>
            <if test="warehouseName != null  and warehouseName != ''"> and warehouse_name like concat('%', #{warehouseName}, '%')</if>
            <if test="ownerCode != null  and ownerCode != ''"> and owner_code = #{ownerCode}</if>
            <if test="ownerName != null  and ownerName != ''"> and owner_name like concat('%', #{ownerName}, '%')</if>
            <if test="issueId != null "> and issue_id = #{issueId}</if>
            <if test="issueCode != null  and issueCode != ''"> and issue_code = #{issueCode}</if>
            <if test="wmsAdjustOrderNo != null  and wmsAdjustOrderNo != ''"> and wms_adjust_order_no = #{wmsAdjustOrderNo}</if>
            <if test="issueTime != null "> and issue_time = #{issueTime}</if>
            <if test="pushTime != null "> and push_time = #{pushTime}</if>
            <if test="completeTime != null "> and complete_time = #{completeTime}</if>
            <if test="closeTime != null "> and close_time = #{closeTime}</if>
            <if test="closeReason != null  and closeReason != ''"> and close_reason = #{closeReason}</if>
        </where>
    </select>

    <select id="selectProExceptionDeductionByDeductionId" parameterType="Long" resultMap="ProExceptionDeductionResult">
        <include refid="selectProExceptionDeductionVo"/>
        where deduction_id = #{deductionId}
    </select>

    <insert id="insertProExceptionDeduction" parameterType="ProExceptionDeduction" useGeneratedKeys="true" keyProperty="deductionId">
        insert into pro_exception_deduction
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deductionCode != null and deductionCode != ''">deduction_code,</if>
            <if test="workorderId != null">workorder_id,</if>
            <if test="workorderCode != null and workorderCode != ''">workorder_code,</if>
            <if test="workorderName != null">workorder_name,</if>
            <if test="itemCode != null and itemCode != ''">item_code,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="specification != null">specification,</if>
            <if test="unitOfMeasure != null">unit_of_measure,</if>
            <if test="totalDeductionQuantity != null">total_deduction_quantity,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="statusDesc != null">status_desc,</if>
            <if test="workshopId != null">workshop_id,</if>
            <if test="workshopName != null">workshop_name,</if>
            <if test="warehouseCode != null">warehouse_code,</if>
            <if test="warehouseName != null">warehouse_name,</if>
            <if test="ownerCode != null">owner_code,</if>
            <if test="ownerName != null">owner_name,</if>
            <if test="issueId != null">issue_id,</if>
            <if test="issueCode != null">issue_code,</if>
            <if test="wmsAdjustOrderNo != null">wms_adjust_order_no,</if>
            <if test="issueTime != null">issue_time,</if>
            <if test="pushTime != null">push_time,</if>
            <if test="completeTime != null">complete_time,</if>
            <if test="closeTime != null">close_time,</if>
            <if test="closeReason != null">close_reason,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deductionCode != null and deductionCode != ''">#{deductionCode},</if>
            <if test="workorderId != null">#{workorderId},</if>
            <if test="workorderCode != null and workorderCode != ''">#{workorderCode},</if>
            <if test="workorderName != null">#{workorderName},</if>
            <if test="itemCode != null and itemCode != ''">#{itemCode},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="specification != null">#{specification},</if>
            <if test="unitOfMeasure != null">#{unitOfMeasure},</if>
            <if test="totalDeductionQuantity != null">#{totalDeductionQuantity},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="statusDesc != null">#{statusDesc},</if>
            <if test="workshopId != null">#{workshopId},</if>
            <if test="workshopName != null">#{workshopName},</if>
            <if test="warehouseCode != null">#{warehouseCode},</if>
            <if test="warehouseName != null">#{warehouseName},</if>
            <if test="ownerCode != null">#{ownerCode},</if>
            <if test="ownerName != null">#{ownerName},</if>
            <if test="issueId != null">#{issueId},</if>
            <if test="issueCode != null">#{issueCode},</if>
            <if test="wmsAdjustOrderNo != null">#{wmsAdjustOrderNo},</if>
            <if test="issueTime != null">#{issueTime},</if>
            <if test="pushTime != null">#{pushTime},</if>
            <if test="completeTime != null">#{completeTime},</if>
            <if test="closeTime != null">#{closeTime},</if>
            <if test="closeReason != null">#{closeReason},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateProExceptionDeduction" parameterType="ProExceptionDeduction">
        update pro_exception_deduction
        <trim prefix="SET" suffixOverrides=",">
            <if test="deductionCode != null and deductionCode != ''">deduction_code = #{deductionCode},</if>
            <if test="workorderId != null">workorder_id = #{workorderId},</if>
            <if test="workorderCode != null and workorderCode != ''">workorder_code = #{workorderCode},</if>
            <if test="workorderName != null">workorder_name = #{workorderName},</if>
            <if test="itemCode != null and itemCode != ''">item_code = #{itemCode},</if>
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="unitOfMeasure != null">unit_of_measure = #{unitOfMeasure},</if>
            <if test="totalDeductionQuantity != null">total_deduction_quantity = #{totalDeductionQuantity},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="statusDesc != null">status_desc = #{statusDesc},</if>
            <if test="workshopId != null">workshop_id = #{workshopId},</if>
            <if test="workshopName != null">workshop_name = #{workshopName},</if>
            <if test="warehouseCode != null">warehouse_code = #{warehouseCode},</if>
            <if test="warehouseName != null">warehouse_name = #{warehouseName},</if>
            <if test="ownerCode != null">owner_code = #{ownerCode},</if>
            <if test="ownerName != null">owner_name = #{ownerName},</if>
            <if test="issueId != null">issue_id = #{issueId},</if>
            <if test="issueCode != null">issue_code = #{issueCode},</if>
            <if test="wmsAdjustOrderNo != null">wms_adjust_order_no = #{wmsAdjustOrderNo},</if>
            <if test="issueTime != null">issue_time = #{issueTime},</if>
            <if test="pushTime != null">push_time = #{pushTime},</if>
            <if test="completeTime != null">complete_time = #{completeTime},</if>
            <if test="closeTime != null">close_time = #{closeTime},</if>
            <if test="closeReason != null">close_reason = #{closeReason},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where deduction_id = #{deductionId}
    </update>

    <delete id="deleteProExceptionDeductionByDeductionId" parameterType="Long">
        delete from pro_exception_deduction where deduction_id = #{deductionId}
    </delete>

    <delete id="deleteProExceptionDeductionByDeductionIds" parameterType="String">
        delete from pro_exception_deduction where deduction_id in
        <foreach item="deductionId" collection="array" open="(" separator="," close=")">
            #{deductionId}
        </foreach>
    </delete>

    <select id="selectByDeductionIds" resultMap="ProExceptionDeductionResult">
        <include refid="selectProExceptionDeductionVo"/>
        where deduction_id in
        <foreach item="deductionId" collection="deductionIds" open="(" separator="," close=")">
            #{deductionId}
        </foreach>
        order by create_time desc
    </select>
</mapper>
