<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.pro.mapper.ProTaskMapper">
    <resultMap type="ProTask" id="ProTaskResult">
        <result property="taskId"    column="task_id"    />
        <result property="taskCode"    column="task_code"    />
        <result property="taskName"    column="task_name"    />
        <result property="workorderId"    column="workorder_id"    />
        <result property="workorderCode"    column="workorder_code"    />
        <result property="workorderName"    column="workorder_name"    />
        <result property="workshopId"    column="workshop_id"    />
        <result property="workshopName"    column="workshop_name"    />
        <result property="workstationId"    column="workstation_id"    />
        <result property="workstationCode"    column="workstation_code"    />
        <result property="workstationName"    column="workstation_name"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="ownerCode"    column="owner_code"    />
        <result property="ownerName"    column="owner_name"    />
        <result property="routeId"    column="route_id"    />
        <result property="routeCode"    column="route_code"    />
        <result property="processId"    column="process_id"    />
        <result property="processCode"    column="process_code"    />
        <result property="processName"    column="process_name"    />
        <result property="itemId"    column="item_id"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemName"    column="item_name"    />
        <result property="specification"    column="specification"    />
        <result property="unitOfMeasure"    column="unit_of_measure"    />
        <result property="quantity"    column="quantity"    />
        <result property="quantityProduced"    column="quantity_produced"    />
        <result property="quantityQuanlify"    column="quantity_quanlify"    />
        <result property="quantityUnquanlify"    column="quantity_unquanlify"    />
        <result property="quantityChanged"    column="quantity_changed"    />
        <result property="clientId"    column="client_id"    />
        <result property="clientCode"    column="client_code"    />
        <result property="clientName"    column="client_name"    />
        <result property="clientNick"    column="client_nick"    />
        <result property="startTime"    column="start_time"    />
        <result property="duration"    column="duration"    />
        <result property="endTime"    column="end_time"    />
        <result property="colorCode"    column="color_code"    />
        <result property="requestDate"    column="request_date"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="produceBatchCode"    column="produce_batch_code"    />
        <result property="wmsOrderNo"    column="wms_order_no"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectProTaskVo">
        select task_id, task_code, task_name, workorder_id, workorder_code, workorder_name, workshop_id, workshop_name, workstation_id, workstation_code, workstation_name, warehouse_code, warehouse_name, owner_code, owner_name, route_id, route_code, process_id, process_code, process_name, item_id, item_code, item_name, specification, unit_of_measure, quantity, quantity_produced, quantity_quanlify, quantity_unquanlify, quantity_changed, client_id, client_code, client_name, client_nick, start_time, duration, end_time, color_code, request_date, status, remark, attr1, attr2, attr3, attr4, produce_batch_code, wms_order_no, create_by, create_time, update_by, update_time from pro_task
    </sql>

    <select id="selectProTaskList" parameterType="ProTask" resultMap="ProTaskResult">
        <include refid="selectProTaskVo"/>
        <where>
            <if test="taskCode != null  and taskCode != ''"> and task_code = #{taskCode}</if>
            <if test="taskName != null  and taskName != ''"> and task_name like concat('%', #{taskName}, '%')</if>
            <if test="workorderId != null "> and workorder_id = #{workorderId}</if>
            <if test="workorderCode != null  and workorderCode != ''"> and workorder_code = #{workorderCode}</if>
            <if test="workorderName != null  and workorderName != ''"> and workorder_name like concat('%', #{workorderName}, '%')</if>
            <if test="workshopId != null "> and workshop_id = #{workshopId}</if>
            <if test="workshopName != null  and workshopName != ''"> and workshop_name like concat('%', #{workshopName}, '%')</if>
            <if test="workstationId != null "> and workstation_id = #{workstationId}</if>
            <if test="workstationCode != null  and workstationCode != ''"> and workstation_code = #{workstationCode}</if>
            <if test="workstationName != null  and workstationName != ''"> and workstation_name like concat('%', #{workstationName}, '%')</if>
            <if test="warehouseCode != null  and warehouseCode != ''"> and warehouse_code = #{warehouseCode}</if>
            <if test="warehouseName != null  and warehouseName != ''"> and warehouse_name like concat('%', #{warehouseName}, '%')</if>
            <if test="ownerCode != null  and ownerCode != ''"> and owner_code = #{ownerCode}</if>
            <if test="ownerName != null  and ownerName != ''"> and owner_name like concat('%', #{ownerName}, '%')</if>
            <if test="routeId != null "> and route_id = #{routeId}</if>
            <if test="routeCode != null  and routeCode != ''"> and route_code = #{routeCode}</if>
            <if test="processId != null "> and process_id = #{processId}</if>
            <if test="processCode != null  and processCode != ''"> and process_code = #{processCode}</if>
            <if test="processName != null  and processName != ''"> and process_name like concat('%', #{processName}, '%')</if>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code = #{itemCode}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="specification != null  and specification != ''"> and specification = #{specification}</if>
            <if test="unitOfMeasure != null  and unitOfMeasure != ''"> and unit_of_measure = #{unitOfMeasure}</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
            <if test="quantityProduced != null "> and quantity_produced = #{quantityProduced}</if>
            <if test="quantityQuanlify != null "> and quantity_quanlify = #{quantityQuanlify}</if>
            <if test="quantityUnquanlify != null "> and quantity_unquanlify = #{quantityUnquanlify}</if>
            <if test="quantityChanged != null "> and quantity_changed = #{quantityChanged}</if>
            <if test="clientId != null "> and client_id = #{clientId}</if>
            <if test="clientCode != null  and clientCode != ''"> and client_code = #{clientCode}</if>
            <if test="clientName != null  and clientName != ''"> and client_name like concat('%', #{clientName}, '%')</if>
            <if test="clientNick != null  and clientNick != ''"> and client_nick = #{clientNick}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="duration != null "> and duration = #{duration}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="colorCode != null  and colorCode != ''"> and color_code = #{colorCode}</if>
            <if test="requestDate != null "> and request_date = #{requestDate}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="attr1 != null  and attr1 != ''"> and attr1 = #{attr1}</if>
            <if test="attr2 != null  and attr2 != ''"> and attr2 = #{attr2}</if>
            <if test="attr3 != null "> and attr3 = #{attr3}</if>
            <if test="attr4 != null "> and attr4 = #{attr4}</if>
            <if test="produceBatchCode != null  and produceBatchCode != ''"> and produce_batch_code = #{produceBatchCode}</if>
            <if test="wmsOrderNo != null  and wmsOrderNo != ''"> and wms_order_no = #{wmsOrderNo}</if>
        </where>
    </select>

    <select id="selectProTaskByTaskId" parameterType="Long" resultMap="ProTaskResult">
        <include refid="selectProTaskVo"/>
        where task_id = #{taskId}
    </select>

    <insert id="insertProTask" parameterType="ProTask" useGeneratedKeys="true" keyProperty="taskId">
        insert into pro_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskCode != null and taskCode != ''">task_code,</if>
            <if test="taskName != null and taskName != ''">task_name,</if>
            <if test="workorderId != null">workorder_id,</if>
            <if test="workorderCode != null and workorderCode != ''">workorder_code,</if>
            <if test="workorderName != null and workorderName != ''">workorder_name,</if>
            <if test="workshopId != null">workshop_id,</if>
            <if test="workshopName != null">workshop_name,</if>
            <if test="workstationId != null">workstation_id,</if>
            <if test="workstationCode != null and workstationCode != ''">workstation_code,</if>
            <if test="workstationName != null and workstationName != ''">workstation_name,</if>
            <if test="warehouseCode != null and warehouseCode != ''">warehouse_code,</if>
            <if test="warehouseName != null and warehouseName != ''">warehouse_name,</if>
            <if test="ownerCode != null and ownerCode != ''">owner_code,</if>
            <if test="ownerName != null and ownerName != ''">owner_name,</if>
            <if test="routeId != null">route_id,</if>
            <if test="routeCode != null">route_code,</if>
            <if test="processId != null">process_id,</if>
            <if test="processCode != null">process_code,</if>
            <if test="processName != null">process_name,</if>
            <if test="itemId != null">item_id,</if>
            <if test="itemCode != null and itemCode != ''">item_code,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="specification != null">specification,</if>
            <if test="unitOfMeasure != null">unit_of_measure,</if>
            <if test="quantity != null">quantity,</if>
            <if test="quantityProduced != null">quantity_produced,</if>
            <if test="quantityQuanlify != null">quantity_quanlify,</if>
            <if test="quantityUnquanlify != null">quantity_unquanlify,</if>
            <if test="quantityChanged != null">quantity_changed,</if>
            <if test="clientId != null">client_id,</if>
            <if test="clientCode != null">client_code,</if>
            <if test="clientName != null">client_name,</if>
            <if test="clientNick != null">client_nick,</if>
            <if test="startTime != null">start_time,</if>
            <if test="duration != null">duration,</if>
            <if test="endTime != null">end_time,</if>
            <if test="colorCode != null">color_code,</if>
            <if test="requestDate != null">request_date,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="produceBatchCode != null">produce_batch_code,</if>
            <if test="wmsOrderNo != null">wms_order_no,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskCode != null and taskCode != ''">#{taskCode},</if>
            <if test="taskName != null and taskName != ''">#{taskName},</if>
            <if test="workorderId != null">#{workorderId},</if>
            <if test="workorderCode != null and workorderCode != ''">#{workorderCode},</if>
            <if test="workorderName != null and workorderName != ''">#{workorderName},</if>
            <if test="workshopId != null">#{workshopId},</if>
            <if test="workshopName != null">#{workshopName},</if>
            <if test="workstationId != null">#{workstationId},</if>
            <if test="workstationCode != null and workstationCode != ''">#{workstationCode},</if>
            <if test="workstationName != null and workstationName != ''">#{workstationName},</if>
            <if test="warehouseCode != null and warehouseCode != ''">#{warehouseCode},</if>
            <if test="warehouseName != null and warehouseName != ''">#{warehouseName},</if>
            <if test="ownerCode != null and ownerCode != ''">#{ownerCode},</if>
            <if test="ownerName != null and ownerName != ''">#{ownerName},</if>
            <if test="routeId != null">#{routeId},</if>
            <if test="routeCode != null">#{routeCode},</if>
            <if test="processId != null">#{processId},</if>
            <if test="processCode != null">#{processCode},</if>
            <if test="processName != null">#{processName},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="itemCode != null and itemCode != ''">#{itemCode},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="specification != null">#{specification},</if>
            <if test="unitOfMeasure != null">#{unitOfMeasure},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="quantityProduced != null">#{quantityProduced},</if>
            <if test="quantityQuanlify != null">#{quantityQuanlify},</if>
            <if test="quantityUnquanlify != null">#{quantityUnquanlify},</if>
            <if test="quantityChanged != null">#{quantityChanged},</if>
            <if test="clientId != null">#{clientId},</if>
            <if test="clientCode != null">#{clientCode},</if>
            <if test="clientName != null">#{clientName},</if>
            <if test="clientNick != null">#{clientNick},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="duration != null">#{duration},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="colorCode != null">#{colorCode},</if>
            <if test="requestDate != null">#{requestDate},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="produceBatchCode != null">#{produceBatchCode},</if>
            <if test="wmsOrderNo != null">#{wmsOrderNo},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateProTask" parameterType="ProTask">
        update pro_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskCode != null and taskCode != ''">task_code = #{taskCode},</if>
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="workorderId != null">workorder_id = #{workorderId},</if>
            <if test="workorderCode != null and workorderCode != ''">workorder_code = #{workorderCode},</if>
            <if test="workorderName != null and workorderName != ''">workorder_name = #{workorderName},</if>
            <if test="workshopId != null">workshop_id = #{workshopId},</if>
            <if test="workshopName != null">workshop_name = #{workshopName},</if>
            <if test="workstationId != null">workstation_id = #{workstationId},</if>
            <if test="workstationCode != null and workstationCode != ''">workstation_code = #{workstationCode},</if>
            <if test="workstationName != null and workstationName != ''">workstation_name = #{workstationName},</if>
            <if test="warehouseCode != null and warehouseCode != ''">warehouse_code = #{warehouseCode},</if>
            <if test="warehouseName != null and warehouseName != ''">warehouse_name = #{warehouseName},</if>
            <if test="ownerCode != null and ownerCode != ''">owner_code = #{ownerCode},</if>
            <if test="ownerName != null and ownerName != ''">owner_name = #{ownerName},</if>
            <if test="routeId != null">route_id = #{routeId},</if>
            <if test="routeCode != null">route_code = #{routeCode},</if>
            <if test="processId != null">process_id = #{processId},</if>
            <if test="processCode != null">process_code = #{processCode},</if>
            <if test="processName != null">process_name = #{processName},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="itemCode != null and itemCode != ''">item_code = #{itemCode},</if>
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="unitOfMeasure != null">unit_of_measure = #{unitOfMeasure},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="quantityProduced != null">quantity_produced = #{quantityProduced},</if>
            <if test="quantityQuanlify != null">quantity_quanlify = #{quantityQuanlify},</if>
            <if test="quantityUnquanlify != null">quantity_unquanlify = #{quantityUnquanlify},</if>
            <if test="quantityChanged != null">quantity_changed = #{quantityChanged},</if>
            <if test="clientId != null">client_id = #{clientId},</if>
            <if test="clientCode != null">client_code = #{clientCode},</if>
            <if test="clientName != null">client_name = #{clientName},</if>
            <if test="clientNick != null">client_nick = #{clientNick},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="colorCode != null">color_code = #{colorCode},</if>
            <if test="requestDate != null">request_date = #{requestDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="produceBatchCode != null">produce_batch_code = #{produceBatchCode},</if>
            <if test="wmsOrderNo != null">wms_order_no = #{wmsOrderNo},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where task_id = #{taskId}
    </update>

    <delete id="deleteProTaskByTaskId" parameterType="Long">
        delete from pro_task where task_id = #{taskId}
    </delete>

    <delete id="deleteProTaskByTaskIds" parameterType="String">
        delete from pro_task where task_id in
        <foreach item="taskId" collection="array" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </delete>


</mapper>