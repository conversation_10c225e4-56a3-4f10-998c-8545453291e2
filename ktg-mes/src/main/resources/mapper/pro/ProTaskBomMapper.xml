<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.pro.mapper.ProTaskBomMapper">

    <resultMap type="ProTaskBom" id="ProTaskBomResult">
        <result property="lineId"    column="line_id"    />
        <result property="workorderId"    column="workorder_id"    />
        <result property="taskId"    column="task_id"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemName"    column="item_name"    />
        <result property="itemTotalIssue"    column="item_total_issue"    />
        <result property="itemTotalRt"    column="item_total_rt"    />
        <result property="systemRequiredMaterialReturn"    column="system_required_material_return"    />
        <result property="lineSideWarehouseLeft"    column="line_side_warehouse_left"    />
        <result property="actualUsage"    column="actual_usage"    />
        <result property="theoreticalUsage"    column="theoretical_usage"    />
        <result property="productionLoss"    column="production_loss"    />
        <result property="lossQuantity"    column="loss_quantity"    />
        <result property="lossPercentage"    column="loss_percentage"    />
        <result property="remark"    column="remark"    />
        <result property="excessShortQuantity"    column="excess_short_quantity"    />
        <result property="systemDisplayLoss"    column="system_display_loss"    />
        <result property="adjustDetailList"    column="adjust_detail_list"    />
        <result property="lossDetail"    column="loss_detail"    />
        <result property="lossSkuLotNo"    column="loss_sku_lot_no"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectProTaskBomVo">
        select line_id, workorder_id, task_id, item_code, item_name, item_total_issue, item_total_rt, system_required_material_return, line_side_warehouse_left, actual_usage, theoretical_usage, production_loss, loss_quantity, loss_percentage, remark, excess_short_quantity, system_display_loss, adjust_detail_list, loss_detail, loss_sku_lot_no, create_by, create_time, update_by, update_time from pro_task_bom
    </sql>

    <select id="selectProTaskBomList" parameterType="ProTaskBom" resultMap="ProTaskBomResult">
        <include refid="selectProTaskBomVo"/>
        <where>
            <if test="workorderId != null "> and workorder_id = #{workorderId}</if>
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code = #{itemCode}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="itemTotalIssue != null "> and item_total_issue = #{itemTotalIssue}</if>
            <if test="itemTotalRt != null "> and item_total_rt = #{itemTotalRt}</if>
            <if test="systemRequiredMaterialReturn != null "> and system_required_material_return = #{systemRequiredMaterialReturn}</if>
            <if test="lineSideWarehouseLeft != null "> and line_side_warehouse_left = #{lineSideWarehouseLeft}</if>
            <if test="actualUsage != null "> and actual_usage = #{actualUsage}</if>
            <if test="theoreticalUsage != null "> and theoretical_usage = #{theoreticalUsage}</if>
            <if test="productionLoss != null "> and production_loss = #{productionLoss}</if>
            <if test="lossQuantity != null "> and loss_quantity = #{lossQuantity}</if>
            <if test="lossPercentage != null "> and loss_percentage = #{lossPercentage}</if>
            <if test="excessShortQuantity != null "> and excess_short_quantity = #{excessShortQuantity}</if>
            <if test="systemDisplayLoss != null "> and system_display_loss = #{systemDisplayLoss}</if>
            <if test="adjustDetailList != null  and adjustDetailList != ''"> and adjust_detail_list = #{adjustDetailList}</if>
            <if test="lossDetail != null  and lossDetail != ''"> and loss_detail = #{lossDetail}</if>
            <if test="lossSkuLotNo != null  and lossSkuLotNo != ''"> and loss_sku_lot_no = #{lossSkuLotNo}</if>
        </where>
    </select>

    <select id="selectProTaskBomByLineId" parameterType="Long" resultMap="ProTaskBomResult">
        <include refid="selectProTaskBomVo"/>
        where line_id = #{lineId}
    </select>

    <insert id="insertProTaskBom" parameterType="ProTaskBom" useGeneratedKeys="true" keyProperty="lineId">
        insert into pro_task_bom
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workorderId != null">workorder_id,</if>
            <if test="taskId != null">task_id,</if>
            <if test="itemCode != null and itemCode != ''">item_code,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="itemTotalIssue != null">item_total_issue,</if>
            <if test="itemTotalRt != null">item_total_rt,</if>
            <if test="systemRequiredMaterialReturn != null">system_required_material_return,</if>
            <if test="lineSideWarehouseLeft != null">line_side_warehouse_left,</if>
            <if test="actualUsage != null">actual_usage,</if>
            <if test="theoreticalUsage != null">theoretical_usage,</if>
            <if test="productionLoss != null">production_loss,</if>
            <if test="lossQuantity != null">loss_quantity,</if>
            <if test="lossPercentage != null">loss_percentage,</if>
            <if test="remark != null">remark,</if>
            <if test="excessShortQuantity != null">excess_short_quantity,</if>
            <if test="systemDisplayLoss != null">system_display_loss,</if>
            <if test="adjustDetailList != null">adjust_detail_list,</if>
            <if test="lossDetail != null">loss_detail,</if>
            <if test="lossSkuLotNo != null">loss_sku_lot_no,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workorderId != null">#{workorderId},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="itemCode != null and itemCode != ''">#{itemCode},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="itemTotalIssue != null">#{itemTotalIssue},</if>
            <if test="itemTotalRt != null">#{itemTotalRt},</if>
            <if test="systemRequiredMaterialReturn != null">#{systemRequiredMaterialReturn},</if>
            <if test="lineSideWarehouseLeft != null">#{lineSideWarehouseLeft},</if>
            <if test="actualUsage != null">#{actualUsage},</if>
            <if test="theoreticalUsage != null">#{theoreticalUsage},</if>
            <if test="productionLoss != null">#{productionLoss},</if>
            <if test="lossQuantity != null">#{lossQuantity},</if>
            <if test="lossPercentage != null">#{lossPercentage},</if>
            <if test="remark != null">#{remark},</if>
            <if test="excessShortQuantity != null">#{excessShortQuantity},</if>
            <if test="systemDisplayLoss != null">#{systemDisplayLoss},</if>
            <if test="adjustDetailList != null">#{adjustDetailList},</if>
            <if test="lossDetail != null">#{lossDetail},</if>
            <if test="lossSkuLotNo != null">#{lossSkuLotNo},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateProTaskBom" parameterType="ProTaskBom">
        update pro_task_bom
        <trim prefix="SET" suffixOverrides=",">
            <if test="workorderId != null">workorder_id = #{workorderId},</if>
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="itemCode != null and itemCode != ''">item_code = #{itemCode},</if>
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="itemTotalIssue != null">item_total_issue = #{itemTotalIssue},</if>
            <if test="itemTotalRt != null">item_total_rt = #{itemTotalRt},</if>
            <if test="systemRequiredMaterialReturn != null">system_required_material_return = #{systemRequiredMaterialReturn},</if>
            <if test="lineSideWarehouseLeft != null">line_side_warehouse_left = #{lineSideWarehouseLeft},</if>
            <if test="actualUsage != null">actual_usage = #{actualUsage},</if>
            <if test="theoreticalUsage != null">theoretical_usage = #{theoreticalUsage},</if>
            <if test="productionLoss != null">production_loss = #{productionLoss},</if>
            <if test="lossQuantity != null">loss_quantity = #{lossQuantity},</if>
            <if test="lossPercentage != null">loss_percentage = #{lossPercentage},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="excessShortQuantity != null">excess_short_quantity = #{excessShortQuantity},</if>
            <if test="systemDisplayLoss != null">system_display_loss = #{systemDisplayLoss},</if>
            <if test="adjustDetailList != null">adjust_detail_list = #{adjustDetailList},</if>
            <if test="lossDetail != null">loss_detail = #{lossDetail},</if>
            <if test="lossSkuLotNo != null">loss_sku_lot_no = #{lossSkuLotNo},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where line_id = #{lineId}
    </update>

    <delete id="deleteProTaskBomByLineId" parameterType="Long">
        delete from pro_task_bom where line_id = #{lineId}
    </delete>

    <delete id="deleteProTaskBomByLineIds" parameterType="String">
        delete from pro_task_bom where line_id in
        <foreach item="lineId" collection="array" open="(" separator="," close=")">
            #{lineId}
        </foreach>
    </delete>
</mapper>