<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.pro.mapper.ProExceptionDeductionLineMapper">
    
    <resultMap type="ProExceptionDeductionLine" id="ProExceptionDeductionLineResult">
        <result property="lineId"    column="line_id"    />
        <result property="deductionId"    column="deduction_id"    />
        <result property="deductionCode"    column="deduction_code"    />
        <result property="workorderId"    column="workorder_id"    />
        <result property="workorderCode"    column="workorder_code"    />
        <result property="taskId"    column="task_id"    />
        <result property="taskCode"    column="task_code"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemName"    column="item_name"    />
        <result property="missingReason"    column="missing_reason"    />
        <result property="missingLotId"    column="missing_lot_id"    />
        <result property="missingQuantity"    column="missing_quantity"    />
        <result property="unitOfMeasure"    column="unit_of_measure"    />
        <result property="triggerType"    column="trigger_type"    />
        <result property="triggerDescription"    column="trigger_description"    />
        <result property="actualLoss"    column="actual_loss"    />
        <result property="systemLoss"    column="system_loss"    />
        <result property="extraFeedbackQuantity"    column="extra_feedback_quantity"    />
        <result property="bondedParticleRatio"    column="bonded_particle_ratio"    />
        <result property="remark"    column="remark"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
        <result property="attr4"    column="attr4"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectProExceptionDeductionLineVo">
        select line_id, deduction_id, deduction_code, workorder_id, workorder_code, task_id, task_code, 
               item_code, item_name, missing_reason, missing_lot_id, missing_quantity, unit_of_measure, 
               trigger_type, trigger_description, actual_loss, system_loss, extra_feedback_quantity, 
               bonded_particle_ratio, remark, attr1, attr2, attr3, attr4, create_by, create_time, 
               update_by, update_time 
        from pro_exception_deduction_line
    </sql>

    <select id="selectProExceptionDeductionLineList" parameterType="ProExceptionDeductionLine" resultMap="ProExceptionDeductionLineResult">
        <include refid="selectProExceptionDeductionLineVo"/>
        <where>  
            <if test="deductionId != null "> and deduction_id = #{deductionId}</if>
            <if test="deductionCode != null  and deductionCode != ''"> and deduction_code like concat('%', #{deductionCode}, '%')</if>
            <if test="workorderId != null "> and workorder_id = #{workorderId}</if>
            <if test="workorderCode != null  and workorderCode != ''"> and workorder_code like concat('%', #{workorderCode}, '%')</if>
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="taskCode != null  and taskCode != ''"> and task_code like concat('%', #{taskCode}, '%')</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code = #{itemCode}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="missingReason != null  and missingReason != ''"> and missing_reason = #{missingReason}</if>
            <if test="triggerType != null  and triggerType != ''"> and trigger_type = #{triggerType}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectProExceptionDeductionLineByLineId" parameterType="Long" resultMap="ProExceptionDeductionLineResult">
        <include refid="selectProExceptionDeductionLineVo"/>
        where line_id = #{lineId}
    </select>

    <select id="selectByDeductionId" resultMap="ProExceptionDeductionLineResult">
        <include refid="selectProExceptionDeductionLineVo"/>
        where deduction_id = #{deductionId}
        order by create_time desc
    </select>

    <select id="selectByWorkorderId" resultMap="ProExceptionDeductionLineResult">
        <include refid="selectProExceptionDeductionLineVo"/>
        where workorder_id = #{workorderId}
        order by create_time desc
    </select>

    <select id="selectByTaskId" resultMap="ProExceptionDeductionLineResult">
        <include refid="selectProExceptionDeductionLineVo"/>
        where task_id = #{taskId}
        order by create_time desc
    </select>

    <select id="selectByTaskIdAndItemCode" resultMap="ProExceptionDeductionLineResult">
        <include refid="selectProExceptionDeductionLineVo"/>
        where task_id = #{taskId} and item_code = #{itemCode}
        order by create_time desc
    </select>

    <select id="checkExistsByTaskIdAndItemCode" resultMap="ProExceptionDeductionLineResult">
        <include refid="selectProExceptionDeductionLineVo"/>
        where task_id = #{taskId} and item_code = #{itemCode}
        limit 1
    </select>
        
    <insert id="insertProExceptionDeductionLine" parameterType="ProExceptionDeductionLine" useGeneratedKeys="true" keyProperty="lineId">
        insert into pro_exception_deduction_line
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deductionId != null">deduction_id,</if>
            <if test="deductionCode != null and deductionCode != ''">deduction_code,</if>
            <if test="workorderId != null">workorder_id,</if>
            <if test="workorderCode != null and workorderCode != ''">workorder_code,</if>
            <if test="taskId != null">task_id,</if>
            <if test="taskCode != null and taskCode != ''">task_code,</if>
            <if test="itemCode != null and itemCode != ''">item_code,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="missingReason != null and missingReason != ''">missing_reason,</if>
            <if test="missingLotId != null">missing_lot_id,</if>
            <if test="missingQuantity != null">missing_quantity,</if>
            <if test="unitOfMeasure != null">unit_of_measure,</if>
            <if test="triggerType != null and triggerType != ''">trigger_type,</if>
            <if test="triggerDescription != null">trigger_description,</if>
            <if test="actualLoss != null">actual_loss,</if>
            <if test="systemLoss != null">system_loss,</if>
            <if test="extraFeedbackQuantity != null">extra_feedback_quantity,</if>
            <if test="bondedParticleRatio != null">bonded_particle_ratio,</if>
            <if test="remark != null">remark,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
            <if test="attr4 != null">attr4,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deductionId != null">#{deductionId},</if>
            <if test="deductionCode != null and deductionCode != ''">#{deductionCode},</if>
            <if test="workorderId != null">#{workorderId},</if>
            <if test="workorderCode != null and workorderCode != ''">#{workorderCode},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="taskCode != null and taskCode != ''">#{taskCode},</if>
            <if test="itemCode != null and itemCode != ''">#{itemCode},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="missingReason != null and missingReason != ''">#{missingReason},</if>
            <if test="missingLotId != null">#{missingLotId},</if>
            <if test="missingQuantity != null">#{missingQuantity},</if>
            <if test="unitOfMeasure != null">#{unitOfMeasure},</if>
            <if test="triggerType != null and triggerType != ''">#{triggerType},</if>
            <if test="triggerDescription != null">#{triggerDescription},</if>
            <if test="actualLoss != null">#{actualLoss},</if>
            <if test="systemLoss != null">#{systemLoss},</if>
            <if test="extraFeedbackQuantity != null">#{extraFeedbackQuantity},</if>
            <if test="bondedParticleRatio != null">#{bondedParticleRatio},</if>
            <if test="remark != null">#{remark},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
            <if test="attr4 != null">#{attr4},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateProExceptionDeductionLine" parameterType="ProExceptionDeductionLine">
        update pro_exception_deduction_line
        <trim prefix="SET" suffixOverrides=",">
            <if test="deductionId != null">deduction_id = #{deductionId},</if>
            <if test="deductionCode != null and deductionCode != ''">deduction_code = #{deductionCode},</if>
            <if test="workorderId != null">workorder_id = #{workorderId},</if>
            <if test="workorderCode != null and workorderCode != ''">workorder_code = #{workorderCode},</if>
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="taskCode != null and taskCode != ''">task_code = #{taskCode},</if>
            <if test="itemCode != null and itemCode != ''">item_code = #{itemCode},</if>
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="missingReason != null and missingReason != ''">missing_reason = #{missingReason},</if>
            <if test="missingLotId != null">missing_lot_id = #{missingLotId},</if>
            <if test="missingQuantity != null">missing_quantity = #{missingQuantity},</if>
            <if test="unitOfMeasure != null">unit_of_measure = #{unitOfMeasure},</if>
            <if test="triggerType != null and triggerType != ''">trigger_type = #{triggerType},</if>
            <if test="triggerDescription != null">trigger_description = #{triggerDescription},</if>
            <if test="actualLoss != null">actual_loss = #{actualLoss},</if>
            <if test="systemLoss != null">system_loss = #{systemLoss},</if>
            <if test="extraFeedbackQuantity != null">extra_feedback_quantity = #{extraFeedbackQuantity},</if>
            <if test="bondedParticleRatio != null">bonded_particle_ratio = #{bondedParticleRatio},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
            <if test="attr4 != null">attr4 = #{attr4},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where line_id = #{lineId}
    </update>

    <delete id="deleteProExceptionDeductionLineByLineId" parameterType="Long">
        delete from pro_exception_deduction_line where line_id = #{lineId}
    </delete>

    <delete id="deleteProExceptionDeductionLineByLineIds" parameterType="String">
        delete from pro_exception_deduction_line where line_id in 
        <foreach item="lineId" collection="array" open="(" separator="," close=")">
            #{lineId}
        </foreach>
    </delete>

    <delete id="deleteByDeductionId" parameterType="Long">
        delete from pro_exception_deduction_line where deduction_id = #{deductionId}
    </delete>
</mapper>
