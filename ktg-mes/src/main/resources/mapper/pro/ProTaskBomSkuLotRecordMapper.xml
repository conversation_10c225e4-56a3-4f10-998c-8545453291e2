<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ktg.mes.pro.mapper.ProTaskBomSkuLotRecordMapper">
    <resultMap type="ProTaskBomSkuLotRecord" id="ProTaskBomSkuLotRecordResult">
        <result property="recordId"    column="record_id"    />
        <result property="workorderId"    column="workorder_id"    />
        <result property="taskId"    column="task_id"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemName"    column="item_name"    />
        <result property="skuLotNo"    column="sku_lot_no"    />
        <result property="type"    column="type"    />
        <result property="subCode"    column="sub_code"    />
        <result property="matchType"    column="match_type"    />
        <result property="quantity"    column="quantity"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectProTaskBomSkuLotRecordVo">
        select record_id, workorder_id, task_id, item_code, item_name, sku_lot_no, type, sub_code, match_type, quantity, create_by, create_time, update_by, update_time from pro_task_bom_sku_lot_record
    </sql>

    <select id="selectProTaskBomSkuLotRecordList" parameterType="ProTaskBomSkuLotRecord" resultMap="ProTaskBomSkuLotRecordResult">
        <include refid="selectProTaskBomSkuLotRecordVo"/>
        <where>
            <if test="workorderId != null "> and workorder_id = #{workorderId}</if>
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code = #{itemCode}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="skuLotNo != null  and skuLotNo != ''"> and sku_lot_no = #{skuLotNo}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="subCode != null  and subCode != ''"> and sub_code = #{subCode}</if>
            <if test="matchType != null "> and match_type = #{matchType}</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
        </where>
    </select>

    <select id="selectProTaskBomSkuLotRecordByRecordId" parameterType="Long" resultMap="ProTaskBomSkuLotRecordResult">
        <include refid="selectProTaskBomSkuLotRecordVo"/>
        where record_id = #{recordId}
    </select>

    <insert id="insertProTaskBomSkuLotRecord" parameterType="ProTaskBomSkuLotRecord" useGeneratedKeys="true" keyProperty="recordId">
        insert into pro_task_bom_sku_lot_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workorderId != null">workorder_id,</if>
            <if test="taskId != null">task_id,</if>
            <if test="itemCode != null and itemCode != ''">item_code,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="skuLotNo != null">sku_lot_no,</if>
            <if test="type != null">type,</if>
            <if test="subCode != null">sub_code,</if>
            <if test="matchType != null">match_type,</if>
            <if test="quantity != null">quantity,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workorderId != null">#{workorderId},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="itemCode != null and itemCode != ''">#{itemCode},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="skuLotNo != null">#{skuLotNo},</if>
            <if test="type != null">#{type},</if>
            <if test="subCode != null">#{subCode},</if>
            <if test="matchType != null">#{matchType},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateProTaskBomSkuLotRecord" parameterType="ProTaskBomSkuLotRecord">
        update pro_task_bom_sku_lot_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="workorderId != null">workorder_id = #{workorderId},</if>
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="itemCode != null and itemCode != ''">item_code = #{itemCode},</if>
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="skuLotNo != null">sku_lot_no = #{skuLotNo},</if>
            <if test="type != null">type = #{type},</if>
            <if test="subCode != null">sub_code = #{subCode},</if>
            <if test="matchType != null">match_type = #{matchType},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where record_id = #{recordId}
    </update>

    <delete id="deleteProTaskBomSkuLotRecordByRecordId" parameterType="Long">
        delete from pro_task_bom_sku_lot_record where record_id = #{recordId}
    </delete>

    <delete id="deleteProTaskBomSkuLotRecordByRecordIds" parameterType="String">
        delete from pro_task_bom_sku_lot_record where record_id in
        <foreach item="recordId" collection="array" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </delete>
</mapper>