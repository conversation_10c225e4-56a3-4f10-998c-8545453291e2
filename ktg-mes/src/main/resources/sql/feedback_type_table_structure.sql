-- 报工任务表结构检查和修改SQL
-- 如果feedback_type字段不存在，则添加该字段
-- 执行时间：2025-08-12
-- 作者：ktg

-- 检查feedback_type字段是否存在
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'pro_feedback' 
  AND COLUMN_NAME = 'feedback_type';

-- 如果上述查询结果为空，说明字段不存在，需要执行以下SQL添加字段
-- ALTER TABLE pro_feedback 
-- ADD COLUMN feedback_type VARCHAR(20) DEFAULT 'REGULAR' COMMENT '报工类型（REGULAR-常规报工，EXTRA-额外报工）' 
-- AFTER record_id;

-- 如果字段已存在但没有默认值，可以执行以下SQL修改字段
-- ALTER TABLE pro_feedback 
-- MODIFY COLUMN feedback_type VARCHAR(20) DEFAULT 'REGULAR' COMMENT '报工类型（REGULAR-常规报工，EXTRA-额外报工）';

-- 创建索引以提高查询性能
-- CREATE INDEX idx_pro_feedback_type ON pro_feedback(feedback_type);
-- CREATE INDEX idx_pro_feedback_task_type ON pro_feedback(task_id, feedback_type);
