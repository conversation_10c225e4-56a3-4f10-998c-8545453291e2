-- 报工任务历史数据修复SQL
-- 将所有历史报工记录的报工类型设置为常规报工
-- 执行时间：2025-08-12
-- 作者：ktg

-- 更新所有历史报工记录的报工类型为常规报工
UPDATE pro_feedback 
SET feedback_type = 'REGULAR', 
    update_time = NOW(), 
    update_by = 'system'
WHERE feedback_type IS NULL 
   OR feedback_type = '';

-- 验证更新结果
SELECT 
    feedback_type,
    COUNT(*) as count
FROM pro_feedback 
GROUP BY feedback_type;

-- 输出修复完成信息
SELECT 
    CONCAT('历史数据修复完成，共更新 ', 
           (SELECT COUNT(*) FROM pro_feedback WHERE feedback_type = 'REGULAR'), 
           ' 条记录为常规报工类型') as result;
