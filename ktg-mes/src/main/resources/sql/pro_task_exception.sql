-- ----------------------------
-- 生产任务异常标记表
-- ----------------------------
DROP TABLE IF EXISTS `pro_task_exception`;
CREATE TABLE `pro_task_exception` (
  `exception_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '异常记录ID',
  `task_id` bigint(20) NOT NULL COMMENT '生产任务ID',
  `task_code` varchar(64) NOT NULL COMMENT '生产任务编号',
  `workorder_id` bigint(20) NOT NULL COMMENT '生产工单ID',
  `workorder_code` varchar(64) NOT NULL COMMENT '生产工单编号',
  `item_code` varchar(64) NOT NULL COMMENT '原材料代码',
  `item_name` varchar(255) NOT NULL COMMENT '原材料名称',
  `exception_reason` varchar(64) NOT NULL COMMENT '异常原因（字典值）',
  `exception_reason_desc` varchar(255) DEFAULT NULL COMMENT '异常原因描述（字典展示名称）',
  `exception_quantity` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '异常数量',
  `registrant` varchar(64) DEFAULT NULL COMMENT '登记人',
  `registrant_name` varchar(100) DEFAULT NULL COMMENT '登记人姓名',
  `register_time` datetime DEFAULT NULL COMMENT '登记时间',
  `attachment_url` varchar(500) DEFAULT NULL COMMENT '附件URL',
  `attachment_name` varchar(255) DEFAULT NULL COMMENT '附件名称',
  `attachment_size` bigint(20) DEFAULT NULL COMMENT '附件大小（字节）',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1删除）',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  `attr1` varchar(64) DEFAULT NULL COMMENT '预留字段1',
  `attr2` varchar(255) DEFAULT NULL COMMENT '预留字段2',
  `attr3` int(11) DEFAULT 0 COMMENT '预留字段3',
  `attr4` int(11) DEFAULT 0 COMMENT '预留字段4',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`exception_id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_workorder_id` (`workorder_id`),
  KEY `idx_item_code` (`item_code`),
  KEY `idx_exception_reason` (`exception_reason`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COMMENT='生产任务异常标记表';

-- ----------------------------
-- 初始化数据（可选）
-- ----------------------------
-- INSERT INTO `pro_task_exception` VALUES (1, 1, 'TASK001', 1, 'WO001', 'ITEM001', '原材料A', 'MATERIAL_DEFECT', '来料不良', 10.0000, 'admin', '管理员', '2025-08-12 10:00:00', NULL, NULL, NULL, '0', '测试数据', NULL, NULL, 0, 0, 'admin', '2025-08-12 10:00:00', '', NULL);

-- ----------------------------
-- 表结构说明
-- ----------------------------
/*
表名：pro_task_exception
用途：记录生产任务中的异常情况，包括原材料异常、生产异常等

主要字段说明：
1. exception_id: 主键，自增ID
2. task_id: 关联生产任务表(pro_task)的主键
3. workorder_id: 关联生产工单表(pro_workorder)的主键
4. item_code/item_name: 异常相关的原材料信息
5. exception_reason: 异常原因的字典值，关联系统字典表
6. exception_reason_desc: 异常原因的中文描述
7. exception_quantity: 异常数量，支持小数点后4位
8. registrant/registrant_name: 登记人信息
9. register_time: 登记时间
10. attachment_*: 附件相关字段，支持上传异常证明文件
11. status: 记录状态，0-正常，1-删除
12. 继承BaseEntity的通用字段：remark, attr1-4, create_by, create_time, update_by, update_time

索引说明：
- 主键索引：exception_id
- 业务索引：task_id, workorder_id, item_code, exception_reason
- 查询优化索引：status, create_time

数据类型选择：
- bigint(20): 用于ID类型字段，支持大数据量
- varchar: 用于文本字段，根据实际需要设置长度
- decimal(12,4): 用于数量字段，支持精确计算
- datetime: 用于时间字段
- char(1): 用于状态字段，节省存储空间

字符集：utf8mb4，支持emoji和特殊字符
存储引擎：InnoDB，支持事务和外键约束
*/
