-- ----------------------------
-- 异常扣减单表头表
-- ----------------------------
DROP TABLE IF EXISTS `pro_exception_deduction`;
CREATE TABLE `pro_exception_deduction` (
  `deduction_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '扣减单ID',
  `deduction_code` varchar(64) NOT NULL COMMENT '扣减单编号',
  `workorder_id` bigint(20) NOT NULL COMMENT '生产工单ID',
  `workorder_code` varchar(64) NOT NULL COMMENT '生产工单编号',
  `workorder_name` varchar(255) DEFAULT NULL COMMENT '生产工单名称',
  `item_code` varchar(64) NOT NULL COMMENT '物料编码',
  `item_name` varchar(255) NOT NULL COMMENT '物料名称',
  `specification` varchar(500) DEFAULT NULL COMMENT '规格型号',
  `unit_of_measure` varchar(64) DEFAULT NULL COMMENT '单位',
  `total_deduction_quantity` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '总代扣数量',
  `status` varchar(64) NOT NULL DEFAULT 'COUNTING' COMMENT '状态',
  `status_desc` varchar(100) DEFAULT NULL COMMENT '状态描述',
  `workshop_id` bigint(20) DEFAULT NULL COMMENT '车间ID',
  `workshop_name` varchar(255) DEFAULT NULL COMMENT '车间名称',
  `warehouse_code` varchar(64) DEFAULT NULL COMMENT '仓库编码',
  `warehouse_name` varchar(255) DEFAULT NULL COMMENT '仓库名称',
  `owner_code` varchar(64) DEFAULT NULL COMMENT '货主编码',
  `owner_name` varchar(255) DEFAULT NULL COMMENT '货主名称',
  `issue_id` bigint(20) DEFAULT NULL COMMENT '关联领料单ID',
  `issue_code` varchar(64) DEFAULT NULL COMMENT '关联领料单编号',
  `wms_adjust_order_no` varchar(64) DEFAULT NULL COMMENT 'WMS调减单号',
  `issue_time` datetime DEFAULT NULL COMMENT '领料时间',
  `push_time` datetime DEFAULT NULL COMMENT '推送时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `close_time` datetime DEFAULT NULL COMMENT '关闭时间',
  `close_reason` varchar(500) DEFAULT NULL COMMENT '关闭原因',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  `attr1` varchar(64) DEFAULT NULL COMMENT '预留字段1',
  `attr2` varchar(255) DEFAULT NULL COMMENT '预留字段2',
  `attr3` bigint(20) DEFAULT NULL COMMENT '预留字段3',
  `attr4` bigint(20) DEFAULT NULL COMMENT '预留字段4',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`deduction_id`),
  UNIQUE KEY `uk_deduction_code` (`deduction_code`),
  KEY `idx_workorder_id` (`workorder_id`),
  KEY `idx_item_code` (`item_code`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_workorder_item` (`workorder_id`, `item_code`),
  KEY `idx_issue_id` (`issue_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COMMENT='异常扣减单表头表';

-- ----------------------------
-- 异常扣减单表体表
-- ----------------------------
DROP TABLE IF EXISTS `pro_exception_deduction_line`;
CREATE TABLE `pro_exception_deduction_line` (
  `line_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '表体ID',
  `deduction_id` bigint(20) NOT NULL COMMENT '扣减单ID',
  `deduction_code` varchar(64) NOT NULL COMMENT '扣减单编号',
  `workorder_id` bigint(20) NOT NULL COMMENT '生产工单ID',
  `workorder_code` varchar(64) NOT NULL COMMENT '生产工单编号',
  `task_id` bigint(20) NOT NULL COMMENT '生产任务ID',
  `task_code` varchar(64) NOT NULL COMMENT '生产任务编码',
  `item_code` varchar(64) NOT NULL COMMENT '物料编码',
  `item_name` varchar(255) NOT NULL COMMENT '物料名称',
  `missing_reason` varchar(100) NOT NULL COMMENT '缺失原因',
  `missing_lot_id` varchar(64) DEFAULT NULL COMMENT '缺失批次ID',
  `missing_quantity` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '缺失数量',
  `unit_of_measure` varchar(64) DEFAULT NULL COMMENT '单位',
  `trigger_type` varchar(64) NOT NULL COMMENT '触发类型',
  `trigger_description` varchar(500) DEFAULT NULL COMMENT '触发说明',
  `actual_loss` decimal(12,4) DEFAULT NULL COMMENT '实际损耗',
  `system_loss` decimal(12,4) DEFAULT NULL COMMENT '系统损耗',
  `extra_feedback_quantity` decimal(12,4) DEFAULT NULL COMMENT '额外报工数量',
  `bonded_particle_ratio` decimal(8,4) DEFAULT NULL COMMENT '保税粒子使用比例',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  `attr1` varchar(64) DEFAULT NULL COMMENT '预留字段1',
  `attr2` varchar(255) DEFAULT NULL COMMENT '预留字段2',
  `attr3` bigint(20) DEFAULT NULL COMMENT '预留字段3',
  `attr4` bigint(20) DEFAULT NULL COMMENT '预留字段4',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`line_id`),
  KEY `idx_deduction_id` (`deduction_id`),
  KEY `idx_workorder_id` (`workorder_id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_item_code` (`item_code`),
  KEY `idx_missing_reason` (`missing_reason`),
  KEY `idx_trigger_type` (`trigger_type`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_task_item` (`task_id`, `item_code`),
  CONSTRAINT `fk_exception_deduction_line_header` FOREIGN KEY (`deduction_id`) REFERENCES `pro_exception_deduction` (`deduction_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COMMENT='异常扣减单表体表';

-- ----------------------------
-- 表结构说明
-- ----------------------------
/*
表头表：pro_exception_deduction
用途：记录生产工单的异常扣减单表头信息，基于生产工单+物料SKU的数据维度创建

主要字段说明：
1. deduction_id: 主键，自增ID
2. deduction_code: 扣减单编号，唯一标识
3. workorder_id/workorder_code: 关联生产工单信息
4. item_code/item_name: 物料信息
5. total_deduction_quantity: 总代扣数量，该生产工单下该物料需要代扣的数量汇总
6. status: 状态（COUNTING-统计中，PENDING_ISSUE-待领料，PENDING_PUSH-待推送，COMPLETED-已完成，CLOSED-已关闭）
7. issue_id/issue_code: 关联的领料单信息
8. wms_adjust_order_no: WMS系统调减单号
9. 时间字段: issue_time-领料时间，push_time-推送时间，complete_time-完成时间，close_time-关闭时间
10. close_reason: 关闭原因

表体表：pro_exception_deduction_line
用途：记录异常扣减单的详细信息，包含任务编码、缺失原因、缺失批次id、缺失数量等

主要字段说明：
1. line_id: 主键，自增ID
2. deduction_id: 关联表头ID
3. task_id/task_code: 生产任务信息
4. missing_reason: 缺失原因（额外生产报工、实际损耗等）
5. missing_lot_id: 缺失批次ID（多到货的批次id）
6. missing_quantity: 缺失数量
7. trigger_type: 触发类型（TASK_BOM_RECORD-任务余料清点记录proTaskBom、TASK_LOT_RECORD-任务批次id记录）
8. actual_loss/system_loss: 实际损耗和系统损耗
9. extra_feedback_quantity: 额外报工数量
10. bonded_particle_ratio: 保税粒子使用比例

触发逻辑：
1. 任务余料清点记录proTaskBom时：实际损耗-系统损耗>0
2. 任务余料清点记录任务批次id时：存在额外报工的保税粒子情况

索引说明：
- 主键索引：deduction_id, line_id
- 唯一索引：deduction_code
- 业务索引：workorder_id, item_code, status, task_id, missing_reason, trigger_type
- 复合索引：workorder_id+item_code, task_id+item_code
- 外键约束：deduction_id关联表头

状态流转：
COUNTING -> PENDING_ISSUE -> PENDING_PUSH -> COMPLETED
                |                |
                v                v
             CLOSED          CLOSED
*/
