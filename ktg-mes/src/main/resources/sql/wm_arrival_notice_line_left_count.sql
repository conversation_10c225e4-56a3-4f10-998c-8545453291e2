-- 到货通知单行表添加余料清点字段
-- 执行时间：2025-08-12
-- 作者：ktg

-- 检查字段是否存在
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'wm_arrival_notice_line' 
  AND COLUMN_NAME IN ('count_quantity_qualified', 'count_quantity_defective');

-- 如果上述查询结果为空，说明字段不存在，需要执行以下SQL添加字段

-- 添加清点数量(正品)字段
ALTER TABLE wm_arrival_notice_line 
ADD COLUMN count_quantity_qualified DECIMAL(12,4) DEFAULT 0.0000 COMMENT '清点数量(正品)' 
AFTER adjust_detail;

-- 添加清点数量(次品)字段
ALTER TABLE wm_arrival_notice_line 
ADD COLUMN count_quantity_defective DECIMAL(12,4) DEFAULT 0.0000 COMMENT '清点数量(次品)' 
AFTER count_quantity_qualified;

-- 创建索引以提高查询性能
CREATE INDEX idx_wm_arrival_notice_line_count ON wm_arrival_notice_line(count_quantity_qualified, count_quantity_defective);

-- 验证字段添加结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'wm_arrival_notice_line' 
  AND COLUMN_NAME IN ('count_quantity_qualified', 'count_quantity_defective')
ORDER BY ORDINAL_POSITION;

-- 输出完成信息
SELECT 
    CONCAT('到货通知单行表字段添加完成，新增字段：count_quantity_qualified, count_quantity_defective') as result;
