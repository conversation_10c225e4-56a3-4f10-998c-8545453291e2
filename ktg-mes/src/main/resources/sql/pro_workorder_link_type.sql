-- 生产工单表添加生产方式字段
-- 执行时间：2025-08-12
-- 作者：ktg

-- 检查link_type字段是否存在
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'pro_workorder' 
  AND COLUMN_NAME = 'link_type';

-- 如果上述查询结果为空，说明字段不存在，需要执行以下SQL添加字段

-- 添加生产方式字段
ALTER TABLE pro_workorder 
ADD COLUMN link_type INT DEFAULT 1 COMMENT '生产方式（1-S TO S，2-S TO F）' 
AFTER push_erp_attach;

-- 创建索引以提高查询性能
CREATE INDEX idx_pro_workorder_link_type ON pro_workorder(link_type);

-- 更新历史数据，默认设置为S TO S模式
UPDATE pro_workorder 
SET link_type = 1, 
    update_time = NOW(), 
    update_by = 'system'
WHERE link_type IS NULL;

-- 验证字段添加结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'pro_workorder' 
  AND COLUMN_NAME = 'link_type';

-- 验证更新结果
SELECT 
    link_type,
    COUNT(*) as count
FROM pro_workorder 
GROUP BY link_type;

-- 输出完成信息
SELECT 
    CONCAT('生产工单表字段添加完成，新增字段：link_type，历史数据已更新为S TO S模式') as result;
